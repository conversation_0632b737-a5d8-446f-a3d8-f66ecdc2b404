include:
  - project: 'projets/Innovation/pole-dir/infrastructure'
    file: 'acr.gitlab-ci.yml'
  - project: 'projets/Innovation/pole-dir/infrastructure'
    file: 'charts_helm/fastapi-stack/gitlab-ci.v1.yml'

stages:
  - build
  - deploy

variables:
  PROJECT_NAME: riposte
  IMAGE_TAG: ${DOCKER_REGISTRY_HOST}/innovation/fraude-timbre
  IMAGE_TAG_SNAPSHOT: ${CI_COMMIT_REF_NAME}

# ACA BUILD
build-api:
  extends: .docker-build-job
  variables:
    SOURCE_PATH: ./api
    IMAGE_CONTAINER: api
  only:
    changes:
      - api/**/*
    refs:
      - main
      
# Build FRONTEND (main only)
build-frontend:
  extends: .docker-build-job
  variables:
    SOURCE_PATH: ./frontend
    IMAGE_CONTAINER: frontend
    OTHER_ARGS: "--build-arg BUILD_ID=${CI_PIPELINE_ID}"
  only:
    changes:
      - frontend/**/*
    refs:
      - main

# Deployment
# Production
deploy-aca-frontend-production:
  extends: .aca-deploy-job
  needs: [build-frontend]
  variables:
    ACA_APP_NAME: riposte-production-frontend
  only:
    changes:
      - frontend/**/*
    refs:
      - main

deploy-aca-api-production:
  extends: .aca-deploy-job
  needs: [build-api]
  variables:
    ACA_APP_NAME: riposte-production-api
  only:
    changes:
      - api/**/*
    refs:
      - main

# Recette
deploy-aca-frontend-recette:
  extends: .aca-deploy-job
  needs: [build-frontend]
  variables:
    ACA_APP_NAME: riposte-recette-frontend
  only:
    changes:
      - frontend/**/*
    refs:
      - main

deploy-aca-api-recette:
  extends: .aca-deploy-job
  needs: [build-api]
  variables:
    ACA_APP_NAME: riposte-recette-api
  only:
    changes:
      - api/**/*
    refs:
      - main












#######################
# STAGING environment #
# KUBERNETES          #
#######################
frontend-staging:
  extends: .build-deploy-frontend
  variables:
    ENV: "production"
    HELM_RELEASE_NAME: "fraude-timbre-staging"
    K8S_NS: "fraude-timbre-staging"
  only:
    refs:
      - develop

api-staging:
  extends: .build-deploy-api
  variables:
    HELM_RELEASE_NAME: "fraude-timbre-staging"
    K8S_NS: "fraude-timbre-staging"
    FOLDER: "api"
  only:
    refs:
      - develop
