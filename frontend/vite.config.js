import { TanStackRouterVite } from '@tanstack/router-vite-plugin';
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import tsconfigPaths from 'vite-tsconfig-paths';
import tailwindcss from '@tailwindcss/vite';
// Ajouter une clé CACHEBUSTER fixe qui sera remplacée par l'entrypoint
// const cacheBusterPlaceholder = 'CACHEBUSTER';
var cacheBusterPlaceholder = Date.now();
// https://vitejs.dev/config https://vitest.dev/config
export default defineConfig({
    plugins: [react(), tsconfigPaths(), TanStackRouterVite(), tailwindcss()],
    server: {
        allowedHosts: true
    },
    build: {
        rollupOptions: {
            output: {
                // Ajouter CACHEBUSTER dans les noms de fichiers
                entryFileNames: "assets/[name].[hash]-".concat(cacheBusterPlaceholder, ".js"),
                chunkFileNames: "assets/[name].[hash]-".concat(cacheBusterPlaceholder, ".js"),
                assetFileNames: "assets/[name].[hash]-".concat(cacheBusterPlaceholder, ".[ext]")
            }
        }
    }
});
