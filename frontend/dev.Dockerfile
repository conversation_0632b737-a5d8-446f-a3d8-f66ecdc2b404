FROM node:20 as dev-stage

WORKDIR /app

# Copy package files and install dependencies
COPY package*.json /app/
COPY pnpm-lock.yaml /app/
RUN npm install -g pnpm
RUN pnpm install

# Copy the rest of the source code
COPY . /app

# Set up an ARG/ENV for API URL if needed
ARG VITE_API_URL=${VITE_API_URL}
ENV VITE_API_URL=$VITE_API_URL

# Expose the development port
EXPOSE 5173

# Default command to start dev server
CMD ["npm", "run", "dev", "--", "--host", "--port", "80"]
