import { useState } from 'react'
import { createFileRoute } from '@tanstack/react-router'
import { useNavigate } from '@tanstack/react-router'
import { Button } from '@/components/Forms/Button'
import { TbCamera } from 'react-icons/tb'
import { toast } from 'react-hot-toast'

import { UsersService } from '@/services/usersService'
import useExternalAuth from '@/hooks/useExternalAuth'
import ClassicScanner from '@/components/Pages/control/scandit/ClassicScanner'

export const MobilePhotoPage = () => {
  const [isScanning, setIsScanning] = useState(false)
  const navigate = useNavigate()
  const { externalLogin } = useExternalAuth()

  // Appelée quand un QR code est détecté (et décodé) par Scandit
  const handleScan = async (scanResult: string) => {
    try {
      await externalLogin(scanResult)

      // Test via la route /user/me
      const user = await UsersService.readUserMe()
      if (user) {
        toast.success('Connexion réussie')
        navigate({ to: '/mobile' })
        return
      }
      setIsScanning(false)
    } catch (error) {
      console.error("Erreur lors de l'enregistrement du token:", error)
      toast.error("Erreur lors de l'association de l'appareil")
    }
  }

  return (
    <div className="flex w-full flex-col items-center gap-4 p-4">
      <h1 className="text-2xl font-bold">Connexion via QR code</h1>

      <div className="w-full max-w-lg rounded-lg border border-gray-200 bg-white p-4 text-center">
        <p className="mb-4 text-red-600">Appareil non associé</p>
        <p className="mb-4 text-gray-700">
          Vous devez d&apos;abord scanner un QR code d&apos;association pour
          pouvoir prendre des photos.
        </p>

        <div className="relative mt-4 aspect-video w-full overflow-hidden rounded-lg bg-gray-100">
          {isScanning ? (
            <ClassicScanner onDetected={(data) => handleScan(data)} />
          ) : (
            <div className="flex h-full items-center justify-center">
              <Button onClick={() => setIsScanning(true)}>
                <TbCamera className="mr-2 h-5 w-5" />
                Activer le scanner
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export const Route = createFileRoute('/login-external')({
  component: MobilePhotoPage,
})
