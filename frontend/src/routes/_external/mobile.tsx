import { createFileRoute } from '@tanstack/react-router'
import { useContext } from 'react'
import MobilePhotoComponent from '@/components/Pages/external/MobilePhotoComponent'
import useExternalAuth from '@/hooks/useExternalAuth'
import { ControlContext } from '@/contexts/Control'

import { FiLogOut } from 'react-icons/fi'
import { getProductLabel } from '@/utils/controlHelpers'

export const Route = createFileRoute('/_external/mobile')({
  component: App,
})

function App() {
  const { currentEnveloppe } = useContext(ControlContext)
  const { logout } = useExternalAuth()

  if (!currentEnveloppe) {
    return <div>Loading...</div>
  }

  return (
    <div className="flex min-h-screen flex-col">
      <div className="flex items-center justify-between bg-gray-100 p-4">
        {/* Envelope info section */}
        <div className="flex flex-col">
          <h2 className="text-lg font-semibold text-gray-800">
            Enveloppe #{currentEnveloppe?.id}
          </h2>
          <div className="mt-1 flex flex-wrap gap-2">
            {currentEnveloppe?.produit && (
              <span className="rounded-full bg-blue-100 px-3 py-1 text-sm font-medium text-blue-800">
                {getProductLabel(currentEnveloppe.produit)}
              </span>
            )}
            {currentEnveloppe?.poids > 0 && (
              <span className="rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800">
                {currentEnveloppe.poids}g
              </span>
            )}
            {currentEnveloppe?.expediteur && (
              <span className="rounded-full bg-purple-100 px-3 py-1 text-sm font-medium text-purple-800">
                Exp: {currentEnveloppe.expediteur.nom}
              </span>
            )}
            {currentEnveloppe?.destination && (
              <span className="rounded-full bg-amber-100 px-3 py-1 text-sm font-medium text-amber-800">
                Dest: {currentEnveloppe.destination.num}
              </span>
            )}
            {currentEnveloppe?.surdimensionne && (
              <span className="rounded-full bg-red-100 px-3 py-1 text-sm font-medium text-red-800">
                Surdimensionné
              </span>
            )}
            {currentEnveloppe?.surpoids && (
              <span className="rounded-full bg-orange-100 px-3 py-1 text-sm font-medium text-orange-800">
                Surpoids
              </span>
            )}
          </div>
        </div>

        {/* Logout button */}
        <button
          className="flex h-12 w-12 items-center justify-center rounded-full bg-red-500 text-white shadow-md transition hover:bg-red-600 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:outline-none"
          onClick={logout}
          aria-label="Déconnexion"
        >
          <FiLogOut className="h-5 w-5" />
        </button>
      </div>

      <div className="flex flex-1 items-start justify-center">
        <MobilePhotoComponent />
      </div>
    </div>
  )
}

export default App
