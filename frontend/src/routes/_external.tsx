import { Outlet, createFileRoute, redirect } from '@tanstack/react-router'
import { Suspense } from 'react'
import { LoadingPage } from '@/components/Pages/LoadingPage'
import useExternalAuth, { isExternalLoggedIn } from '@/hooks/useExternalAuth'
import { ControlProvider } from '@/providers/ControlProvider'


export const Route = createFileRoute('/_external')({
  component: PrivateLayout,
  beforeLoad: async () => {
    if (!isExternalLoggedIn()) {
      throw redirect({
        to: '/login',
      })
    }
  },
})

function PrivateLayout() {
  const { isLoading } = useExternalAuth()  

  if (isLoading) {
    return <LoadingPage />
  }

  if (isExternalLoggedIn()) {
    return (
      <ControlProvider>
        <div className="fixed top-0 left-0 h-screen w-screen overflow-hidden">
          <main
            id="main-container"
            >
            <Suspense fallback={<LoadingPage />}>
              <Outlet />
            </Suspense>
          </main>
        </div>
      </ControlProvider>
    )
  }
}
