import { LotExpediteurService } from '@/services/lotExpediteurService'
import { useSuspenseQuery } from '@tanstack/react-query'
import { createFileRoute, <PERSON> } from '@tanstack/react-router'
import { Button } from '@/components/Forms/Button'
import { FaBox, FaEnvelope, FaFileCsv, FaEye } from 'react-icons/fa'
import { formatDate } from '@/utils/datesUtils'
import { useState } from 'react'
import { useDisclosure } from '@/hooks/useDisclosure'
import { ValorisationModal } from '@/components/Pages/control/ValorisationModal'
import { TbBrandStackoverflow } from 'react-icons/tb'
import { getStatusPliConfig } from '@/utils/statusMetadatasHelpers'

function getLotInfos(lotExpediteurId: string) {
  return {
    queryFn: async () => {
      const lot = await LotExpediteurService.getLotById(
        parseInt(lotExpediteurId)
      )
      return lot
    },
    queryKey: ['lot', { lotExpediteurId }],
  }
}

const handleEnveloppeClick = (id: number) => {
  const url = `/control?id=${id}`

  window.open(url, '_blank')
}

function LotExpediteurPage() {
  const queryFn = Route.useLoaderData()
  const { data: lot } = useSuspenseQuery(queryFn)
  const [selectedValorisation, setSelectedValorisation] = useState<any>(null)
  const {
    isOpen: isValorisationModalOpen,
    onOpen: openValorisationModal,
    onClose: closeValorisationModal,
  } = useDisclosure()

  const handleExportCsv = () => {
    LotExpediteurService.getLotByIdToCsv(lot.id)
      .then((response) => {
        const url = window.URL.createObjectURL(response)
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `lot_${lot.id}.csv`)
        document.body.appendChild(link)
        link.click()
        link.remove()
      })
      .catch((error) => {
        console.error('Erreur lors du téléchargement du CSV:', error)
      })
  }

  const handleValorisationClick = (valorisation: any) => {
    setSelectedValorisation(valorisation)
    openValorisationModal()
  }

  return (
    <div className="flex min-h-screen w-full flex-col bg-gradient-to-b from-blue-50 to-white">
      {/* Header */}
      <header className="border-b border-gray-200 bg-white p-6 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="space-y-3">
            {/* Breadcrumb */}
            <nav className="flex items-center space-x-2 text-sm">
              <Link
                to="/lots-expediteurs"
                className="flex items-center font-medium text-blue-600 transition-colors hover:text-blue-700"
              >
                <FaBox className="mr-1.5 h-3 w-3" />
                Lots expéditeurs
              </Link>
              <span className="text-gray-400">/</span>
              <span className="font-medium text-gray-700">Lot #{lot?.id}</span>
            </nav>

            {/* Main Title */}
            <h1 className="text-2xl font-bold text-gray-900">
              Lot #{lot?.id} - {lot.expediteur.nom}
            </h1>

            {/* Subtitle */}
            <p className="text-gray-600">
              Créé le {formatDate(lot.created_at, 'DD/MM/YYYY à HH:mm')}
            </p>
          </div>

          <Button
            onClick={handleExportCsv}
            variant="outline"
            color="blue"
            className="flex items-center"
          >
            <FaFileCsv className="mr-2 h-4 w-4" />
            Exporter CSV
          </Button>
        </div>
      </header>

      <main className="flex-1 p-6">
        <div className="mx-auto max-w-7xl space-y-6">
          {/* Lot Overview Cards */}
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            {/* Lot Status Card */}
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <h3 className="mb-4 text-lg font-semibold text-gray-800">
                Informations du lot
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Statut:</span>
                  <span
                    className={`inline-flex items-center rounded-full px-3 py-1 text-xs font-semibold ${
                      lot.statut === 'OUVERT'
                        ? 'bg-green-100 text-green-800'
                        : lot.statut === 'FERME'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {lot.statut}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Nombre de plis:</span>
                  <span className="font-semibold">{lot.nombre_enveloppes}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    Nombre de casiers:
                  </span>
                  <span className="font-semibold">{lot.casiers.length}</span>
                </div>
              </div>
            </div>

            {/* Lot Status Card */}
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <h3 className="mb-4 text-lg font-semibold text-gray-800">
                Expediteur
              </h3>
              <div className="space-y-2 text-sm">
                <div className="font-medium">{lot?.expediteur?.nom}</div>
                {lot?.expediteur?.adresse && (
                  <div className="text-gray-600">
                    {lot?.expediteur?.adresse}
                  </div>
                )}
                {(lot?.expediteur?.code_postal || lot?.expediteur?.ville) && (
                  <div className="text-gray-600">
                    {lot?.expediteur?.code_postal} {lot?.expediteur?.ville}
                  </div>
                )}
                {lot?.expediteur?.siret && (
                  <div className="text-xs text-gray-500">
                    SIRET: {lot?.expediteur?.siret}
                  </div>
                )}
                {lot?.expediteur?.coclico && (
                  <div className="text-xs text-gray-500">
                    COCLICO: {lot?.expediteur?.coclico}
                  </div>
                )}
              </div>
            </div>

            {/* Valorisation Summary Card */}
            <div className="rounded-lg bg-white p-6 shadow-sm">
              <h3 className="mb-4 text-lg font-semibold text-gray-800">
                Valorisation
              </h3>
              {lot.valorisation && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">
                      Sous-affranchissement:
                    </span>
                    <span className="font-semibold text-red-600">
                      {lot.valorisation.postage.montant_sous_affranchissement.toFixed(
                        2
                      )}
                      €
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Coût total:</span>
                    <span className="font-semibold">
                      {lot.valorisation.postage.cout_enveloppe.toFixed(2)}€
                    </span>
                  </div>
                  <div className="flex w-full items-center justify-center">
                    <Button
                      variant="outline"
                      className="flex items-center"
                      onClick={() => handleValorisationClick(lot.valorisation)}
                    >
                      <FaEye className="mr-2 h-3 w-3" />
                      Voir détails
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Envelopes List */}
          <div className="">
            <div className="divide-y divide-gray-100">
              {/* Envelopes List Grouped by Casier */}
              {(() => {
                // Group envelopes by casier
                const enveloppesByCasier = lot.enveloppes.reduce(
                  (acc, enveloppe) => {
                    const casierNumero =
                      enveloppe.casier?.numero || 'Sans casier'
                    if (!acc[casierNumero]) {
                      acc[casierNumero] = []
                    }
                    acc[casierNumero].push(enveloppe)
                    return acc
                  },
                  {} as Record<string, typeof lot.enveloppes>
                )

                return Object.entries(enveloppesByCasier).map(
                  ([casierNumero, enveloppes]) => (
                    <div
                      key={casierNumero}
                      className="mb-6 rounded-lg bg-white shadow-sm"
                    >
                      <div className="border-b border-gray-200 p-6">
                        <div className="flex items-center justify-between">
                          <h2 className="flex items-center text-xl font-semibold text-gray-800">
                            <TbBrandStackoverflow className="mr-2 h-5 w-5 text-blue-500" />
                            {casierNumero}
                            <span className="ml-2 text-sm font-normal text-gray-500">
                              ({enveloppes.length} pli
                              {enveloppes.length > 1 ? 's' : ''})
                            </span>
                          </h2>
                        </div>
                      </div>

                      <div className="divide-y divide-gray-100">
                        {enveloppes.map((enveloppe) => {
                          const statusConfig = getStatusPliConfig(
                            enveloppe.statut
                          )
                          return (
                            <div key={enveloppe.id} className="p-6">
                              {/* Envelope Header */}
                              <div
                                className="-m-3 flex cursor-pointer items-center justify-between rounded-lg p-3 hover:bg-gray-50"
                                onClick={() =>
                                  handleEnveloppeClick(enveloppe.id)
                                }
                              >
                                <div className="flex items-center space-x-4">
                                  <div className="flex items-center space-x-2">
                                    {enveloppe?.photos &&
                                    enveloppe.photos.length > 0 ? (
                                      <div
                                        key={enveloppe.photos[0].id}
                                        className="group relative"
                                      >
                                        <img
                                          src={enveloppe.photos[0].public_url}
                                          alt={`Photo miniature`}
                                          className="h-14 w-14 rounded-lg border border-gray-200 object-cover"
                                        />
                                      </div>
                                    ) : (
                                      <div className="flex h-14 w-14 items-center justify-center rounded-lg border border-gray-200 bg-gray-50">
                                        <FaEnvelope className="h-5 w-5 text-gray-400" />
                                      </div>
                                    )}
                                  </div>

                                  <div>
                                    <h3 className="font-semibold text-gray-800">
                                      {enveloppe.affranchissements &&
                                      enveloppe.affranchissements[0]?.code
                                        ? enveloppe.affranchissements[0].code
                                        : `PLI #${enveloppe.id}`}
                                    </h3>
                                    <p className="text-sm text-gray-600">
                                      {enveloppe.poids}g •{' '}
                                      {formatDate(
                                        enveloppe.updated_at,
                                        'DD/MM/YYYY HH:mm'
                                      )}
                                    </p>
                                  </div>
                                </div>

                                <div className="flex items-center space-x-3">
                                  <span
                                    className={`inline-flex items-center rounded-full px-3 py-1 text-xs font-semibold ${statusConfig.bg} ${statusConfig.text} ${statusConfig.border} border`}
                                  >
                                    <span className="mr-1">
                                      {statusConfig.icon}
                                    </span>
                                    {statusConfig.label}
                                  </span>

                                  {enveloppe.valorisation && (
                                    <div className="text-right">
                                      <div className="text-sm font-semibold text-red-600">
                                        {enveloppe.valorisation.postage.montant_sous_affranchissement.toFixed(
                                          2
                                        )}
                                        €
                                      </div>
                                      <div className="text-xs text-gray-500">
                                        sous-aff.
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  )
                )
              })()}
            </div>
          </div>
        </div>
      </main>

      {/* Valorisation Modal */}
      <ValorisationModal
        valorisation={selectedValorisation}
        isOpen={isValorisationModalOpen}
        onClose={closeValorisationModal}
      />
    </div>
  )
}

export const Route = createFileRoute(
  '/_layout/lots-expediteurs/$lotExpediteurId'
)({
  component: LotExpediteurPage,
  loader: async ({ params }) => {
    const { lotExpediteurId } = params
    return getLotInfos(lotExpediteurId)
  },
})
