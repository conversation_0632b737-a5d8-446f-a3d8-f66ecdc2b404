import { createFileRoute } from '@tanstack/react-router'
import { useState, useRef, useEffect } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { Button } from '@/components/Forms/Button'
import {
  FaBox,
  FaEnvelope,
  FaEuroSign,
  FaFileCsv,
  FaFilter,
  FaSort,
  FaSortUp,
  FaSortDown,
  FaEllipsisV,
  FaChevronDown,
  FaTimes,
  FaExternalLinkAlt,
} from 'react-icons/fa'
import { TbRefresh, TbPlus, TbAlertCircle } from 'react-icons/tb'
import { formatDate } from '@/utils/datesUtils'

import { useDisclosure } from '@/hooks/useDisclosure'
import { Link } from '@tanstack/react-router'
import { ValorisationModal } from '@/components/Pages/control/ValorisationModal'
import { toast } from 'react-hot-toast'
import { ApiError } from '@/core/ApiError'
import { Sender } from '@/models/sender'
import { LotExpediteurService } from '@/services/lotExpediteurService'
import { EnumerationService } from '@/services/enumerationService'
import { SiteService } from '@/services/siteService'
import Pagination from '@/components/Pagination'
import { LotExpediteur } from '@/models/lotExpediteurs'
import { getStatusLotConfig } from '@/utils/statusMetadatasHelpers'
import { FilterDropdown } from '@/components/FilterDropdown'
import { NewLotModal } from '@/components/Pages/lots/NewLotModal'
import { StatusChangeConfirmModal } from '@/components/Pages/lots/StatusChangeConfirmModal'

/* -------------------------------------------------------------------------- */
/*                              ACTION DROPDOWN                              */
/* -------------------------------------------------------------------------- */
interface ActionDropdownProps {
  lot: LotExpediteur
  statusOptions: string[]
  onStatusChange: (lot: LotExpediteur, status: string) => void
  onViewValorisation: (lot: LotExpediteur) => void
  onRequestNewCasier: (lot: LotExpediteur) => void
  onExportCsv: (lot: LotExpediteur) => void
}

const ActionDropdown: React.FC<ActionDropdownProps> = ({
  lot,
  statusOptions,
  onStatusChange,
  onViewValorisation,
  onRequestNewCasier,
  onExportCsv,
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex h-8 w-8 items-center justify-center rounded-full text-gray-400 hover:bg-gray-100 hover:text-gray-600"
      >
        <FaEllipsisV className="h-4 w-4" />
      </button>

      {isOpen && (
        <div className="absolute top-full right-0 z-50 mt-1 w-48 rounded-lg border border-gray-200 bg-white shadow-lg">
          <div className="py-1">
            <button
              onClick={() => {
                const url = `/lots-expediteurs/${lot.id}`

                window.open(url, '_blank')
              }}
              className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
            >
              <FaExternalLinkAlt className="mr-3 h-4 w-4" />
              Ouvrir le détail
            </button>
            <button
              onClick={() => {
                onExportCsv(lot)
                setIsOpen(false)
              }}
              className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
            >
              <FaFileCsv className="mr-3 h-4 w-4" />
              Exporter CSV
            </button>

            <button
              onClick={() => {
                onViewValorisation(lot)
                setIsOpen(false)
              }}
              className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
            >
              <FaEuroSign className="mr-3 h-4 w-4" />
              Voir valorisation
            </button>

            {lot.statut === 'OUVERT' && (
              <button
                onClick={() => {
                  onRequestNewCasier(lot)
                  setIsOpen(false)
                }}
                className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
              >
                <FaBox className="mr-3 h-4 w-4" />
                Nouveau casier
              </button>
            )}

            <div className="my-1 border-t border-gray-100"></div>

            <div className="px-4 py-2">
              <p className="text-xs font-medium tracking-wide text-gray-500 uppercase">
                Changer le statut
              </p>
            </div>

            {statusOptions.map((status) => (
              <button
                key={status}
                onClick={() => {
                  onStatusChange(lot, status)
                  setIsOpen(false)
                }}
                className={`flex w-full items-center px-4 py-2 text-sm hover:bg-gray-50 ${
                  status === lot.statut
                    ? 'bg-blue-50 font-medium text-blue-700'
                    : 'text-gray-700'
                }`}
              >
                {status === lot.statut && (
                  <span className="mr-3 flex h-4 w-4 items-center justify-center">
                    ✓
                  </span>
                )}
                {status !== lot.statut && (
                  <span className="mr-3 h-4 w-4"></span>
                )}
                {status}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

/* -------------------------------------------------------------------------- */
/*                                MAIN PAGE                                  */
/* -------------------------------------------------------------------------- */
export const LotsExpediteursPage = () => {
  const queryClient = useQueryClient()
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [statusFilter, setStatusFilter] = useState<string | undefined>()
  const [siteFilter, setSiteFilter] = useState<number | undefined>()
  const [selectedLot, setSelectedLot] = useState<LotExpediteur | null>(null)
  const [newStatus, setNewStatus] = useState<string>('')
  const [sortBy, setSortBy] = useState<string>('created_at')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  // Filter dropdown states
  const [statusFilterOpen, setStatusFilterOpen] = useState(false)
  const [siteFilterOpen, setSiteFilterOpen] = useState(false)

  /* ----------------------------- DISCLOSURES ----------------------------- */
  const {
    isOpen: isConfirmModalOpen,
    onOpen: openConfirmModal,
    onClose: closeConfirmModal,
  } = useDisclosure()

  const [valorisation, setValorisation] = useState<any>(null)
  const {
    isOpen: isValorisationModalOpen,
    onOpen: openValorisationModal,
    onClose: closeValorisationModal,
  } = useDisclosure()

  const {
    isOpen: isSelectSenderModalOpen,
    onOpen: openSelectSenderModal,
    onClose: closeSelectSenderModal,
  } = useDisclosure()
  const [selectedSender, setSelectedSender] = useState<Sender | undefined>()

  /* -------------------------------- QUERIES ------------------------------ */
  const {
    data: lots,
    error,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: [
      'lots-expediteurs',
      currentPage,
      statusFilter,
      siteFilter,
      sortBy,
      sortOrder,
    ],
    queryFn: () =>
      LotExpediteurService.getLots(currentPage, 10, {
        statut: statusFilter,
        site_id: siteFilter,
        sort_by: sortBy,
        sort_order: sortOrder,
      }),
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  })

  const { data: statusOptions = [] } = useQuery({
    queryKey: ['lotsExpediteurStatus'],
    queryFn: () => EnumerationService.getLotsExpediteurStatut(),
  })

  const { data: sites = [] } = useQuery({
    queryKey: ['sites'],
    queryFn: () => SiteService.getSites(),
  })

  /* ------------------------------ HANDLERS ------------------------------ */
  const handleStatusChange = (lot: LotExpediteur, status: string) => {
    setSelectedLot(lot)
    setNewStatus(status)
    openConfirmModal()
  }

  const confirmStatusChange = () => {
    if (selectedLot && newStatus) {
      LotExpediteurService.updateLotStatus(selectedLot.id, newStatus)
        .then(() => {
          queryClient.invalidateQueries({ queryKey: ['lots-expediteurs'] })
          closeConfirmModal()
          toast.success(
            `Statut du lot #${selectedLot.id} mis à jour avec succès`
          )
        })
        .catch((error: ApiError) => {
          const errorDetail = (error.body as any)?.detail || 'Erreur inconnue'
          toast.error(`Erreur lors de la mise à jour du statut: ${errorDetail}`)
          console.error('Erreur lors de la mise à jour du statut:', error)
          closeConfirmModal()
        })
    }
  }

  const handleViewValorisation = (lot: LotExpediteur) => {
    setValorisation(lot.valorisation)
    openValorisationModal()
  }

  const handleCreateLot = async (expediteur_id: number) => {
    try {
      await LotExpediteurService.createLot(expediteur_id)
      queryClient.invalidateQueries({ queryKey: ['lots-expediteurs'] })
      toast.success('Lot créé avec succès')
      closeSelectSenderModal()
      setSelectedSender(undefined)
    } catch (error: any) {
      const errorDetail = (error.body as any)?.detail || 'Erreur inconnue'
      toast.error(`Erreur lors de la création du lot: ${errorDetail}`)
      console.error('Erreur lors de la création du lot:', error)
    }
  }

  const handleRequestNewCasier = async (lot: LotExpediteur) => {
    try {
      await LotExpediteurService.requestNewCasier(lot.id)
      queryClient.invalidateQueries({ queryKey: ['lots-expediteurs'] })
      toast.success('Nouveau casier attribué avec succès')
    } catch (error: any) {
      const errorDetail = (error.body as any)?.detail || 'Erreur inconnue'
      toast.error(`Erreur lors de l'attribution du casier: ${errorDetail}`)
      console.error("Erreur lors de l'attribution du casier:", error)
    }
  }

  const handleExportCsv = (lot: LotExpediteur) => {
    LotExpediteurService.getLotByIdToCsv(lot.id)
      .then((response) => {
        const url = window.URL.createObjectURL(response)
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', `lot_${lot.id}.csv`)
        document.body.appendChild(link)
        link.click()
        link.remove()
      })
      .catch((error) => {
        console.error('Erreur lors du téléchargement du CSV:', error)
      })
  }

  /* -------------------------------- SORTING ----------------------------- */
  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(column)
      setSortOrder('asc')
    }
  }

  const getSortIcon = (column: string) => {
    if (sortBy !== column) {
      return <FaSort className="ml-1 h-3 w-3 text-gray-400" />
    }
    return sortOrder === 'asc' ? (
      <FaSortUp className="ml-1 h-3 w-3 text-blue-500" />
    ) : (
      <FaSortDown className="ml-1 h-3 w-3 text-blue-500" />
    )
  }

  /* -------------------------------- FILTERS ----------------------------- */
  const clearFilters = () => {
    setStatusFilter(undefined)
    setSiteFilter(undefined)
    setCurrentPage(1) // Reset pagination when clearing filters
  }

  const hasActiveFilters = statusFilter || siteFilter

  /* -------------------------------- RENDER -------------------------------- */
  return (
    <div className="flex min-h-screen w-full flex-col bg-gradient-to-b from-blue-50 to-white">
      {/* Page Header */}
      <header className="border-b border-gray-200 bg-white p-6 shadow-sm">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Gestion des lots expéditeurs
            </h1>
            <p className="mt-2 text-gray-600">
              Gérez les lots de plis regroupés par expéditeur
            </p>
          </div>

          <div className="flex items-center gap-3">
            <Button
              onClick={() =>
                queryClient.invalidateQueries({
                  queryKey: ['lots-expediteurs'],
                })
              }
              variant="outline"
              className="flex items-center"
              disabled={isFetching}
            >
              <TbRefresh
                className={`h-4 w-4 ${isFetching ? 'animate-spin' : ''}`}
              />
              <span className="ml-2 hidden sm:inline">Actualiser</span>
            </Button>

            <Button
              variant="outline"
              color="blue"
              className="flex items-center"
              onClick={openSelectSenderModal}
            >
              <TbPlus className="mr-2 h-4 w-4" />
              Créer un lot
            </Button>

            <Link to="/casiers">
              <Button color="blue" className="flex items-center">
                <FaBox className="mr-2 h-4 w-4" />
                Gestion des casiers
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Main content area */}
      <main className="flex-1 p-6">
        <div className="w-full px-32">
          {/* Filters Bar */}
          {hasActiveFilters && (
            <div className="mb-4 flex items-center gap-2 rounded-lg bg-blue-50 p-3">
              <FaFilter className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">
                Filtres actifs:
              </span>

              {statusFilter && (
                <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                  Statut: {statusFilter}
                  <button
                    onClick={() => {
                      setStatusFilter(undefined)
                      setCurrentPage(1) // Reset pagination when removing filter
                    }}
                    className="ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-blue-200"
                  >
                    <FaTimes className="h-2 w-2" />
                  </button>
                </span>
              )}

              {siteFilter && (
                <span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">
                  Site: {sites.find((s) => s.id === siteFilter)?.nom}
                  <button
                    onClick={() => {
                      setSiteFilter(undefined)
                      setCurrentPage(1) // Reset pagination when removing filter
                    }}
                    className="ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-blue-200"
                  >
                    <FaTimes className="h-2 w-2" />
                  </button>
                </span>
              )}

              <button
                onClick={clearFilters}
                className="ml-auto text-sm font-medium text-blue-600 hover:text-blue-800"
              >
                Effacer tous les filtres
              </button>
            </div>
          )}

          {/* Table */}
          <div className="rounded-lg bg-white shadow-sm">
            {isLoading ? (
              <div className="flex h-64 items-center justify-center">
                <div className="flex flex-col items-center">
                  <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-500"></div>
                  <p className="mt-2 text-sm text-gray-500">
                    Chargement des lots...
                  </p>
                </div>
              </div>
            ) : error ? (
              <div className="flex h-64 items-center justify-center">
                <div className="text-center">
                  <div className="mx-auto h-12 w-12 text-red-400">
                    <TbAlertCircle />
                  </div>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    Erreur de chargement
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Une erreur est survenue lors du chargement des lots.
                  </p>
                </div>
              </div>
            ) : lots && lots.items.length === 0 ? (
              <div className="flex h-64 items-center justify-center">
                <div className="text-center">
                  <FaBox className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    Aucun lot trouvé
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {hasActiveFilters
                      ? 'Aucun lot ne correspond aux filtres sélectionnés.'
                      : 'Commencez par créer votre premier lot expéditeur.'}
                  </p>
                  {!hasActiveFilters && (
                    <div className="mt-6">
                      <Button
                        onClick={openSelectSenderModal}
                        className="inline-flex items-center"
                      >
                        <TbPlus className="mr-2 h-4 w-4" />
                        Créer un lot
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      className="cursor-pointer px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase hover:bg-gray-100"
                      onClick={() => handleSort('id')}
                    >
                      <div className="flex items-center">
                        ID
                        {getSortIcon('id')}
                      </div>
                    </th>

                    <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                      Expéditeur
                    </th>

                    <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                      Casiers
                    </th>

                    <th className="relative px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                      <button
                        onClick={() => setSiteFilterOpen(!siteFilterOpen)}
                        className="flex items-center hover:text-gray-700"
                      >
                        Site
                        <FaChevronDown
                          className={`ml-1 h-3 w-3 transition-transform ${siteFilterOpen ? 'rotate-180' : ''}`}
                        />
                        {siteFilter && (
                          <div className="ml-1 h-2 w-2 rounded-full bg-blue-500"></div>
                        )}
                      </button>

                      <FilterDropdown
                        isOpen={siteFilterOpen}
                        onClose={() => setSiteFilterOpen(false)}
                        title="Filtrer par site"
                        hasActiveFilter={!!siteFilter}
                      >
                        <div className="space-y-2">
                          <button
                            onClick={() => {
                              setSiteFilter(undefined)
                              setSiteFilterOpen(false)
                              setCurrentPage(1) // Reset pagination when changing filter
                            }}
                            className={`block w-full rounded px-3 py-2 text-left text-sm hover:bg-gray-50 ${
                              !siteFilter
                                ? 'bg-blue-50 font-medium text-blue-700'
                                : 'text-gray-700'
                            }`}
                          >
                            Tous les sites
                          </button>
                          {sites.map((site) => (
                            <button
                              key={site.id}
                              onClick={() => {
                                setSiteFilter(site.id)
                                setSiteFilterOpen(false)
                                setCurrentPage(1) // Reset pagination when changing filter
                              }}
                              className={`block w-full rounded px-3 py-2 text-left text-sm hover:bg-gray-50 ${
                                siteFilter === site.id
                                  ? 'bg-blue-50 font-medium text-blue-700'
                                  : 'text-gray-700'
                              }`}
                            >
                              {site.nom}
                            </button>
                          ))}
                        </div>
                      </FilterDropdown>
                    </th>

                    <th className="relative px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                      <button
                        onClick={() => setStatusFilterOpen(!statusFilterOpen)}
                        className="flex items-center hover:text-gray-700"
                      >
                        Statut
                        <FaChevronDown
                          className={`ml-1 h-3 w-3 transition-transform ${statusFilterOpen ? 'rotate-180' : ''}`}
                        />
                        {statusFilter && (
                          <div className="ml-1 h-2 w-2 rounded-full bg-blue-500"></div>
                        )}
                      </button>

                      <FilterDropdown
                        isOpen={statusFilterOpen}
                        onClose={() => setStatusFilterOpen(false)}
                        title="Filtrer par statut"
                        hasActiveFilter={!!statusFilter}
                      >
                        <div className="space-y-2">
                          <button
                            onClick={() => {
                              setStatusFilter(undefined)
                              setStatusFilterOpen(false)
                              setCurrentPage(1) // Reset pagination when changing filter
                            }}
                            className={`block w-full rounded px-3 py-2 text-left text-sm hover:bg-gray-50 ${
                              !statusFilter
                                ? 'bg-blue-50 font-medium text-blue-700'
                                : 'text-gray-700'
                            }`}
                          >
                            Tous les statuts
                          </button>
                          {statusOptions.map((status) => (
                            <button
                              key={status}
                              onClick={() => {
                                setStatusFilter(status)
                                setStatusFilterOpen(false)
                                setCurrentPage(1) // Reset pagination when changing filter
                              }}
                              className={`block w-full rounded px-3 py-2 text-left text-sm hover:bg-gray-50 ${
                                statusFilter === status
                                  ? 'bg-blue-50 font-medium text-blue-700'
                                  : 'text-gray-700'
                              }`}
                            >
                              {status}
                            </button>
                          ))}
                        </div>
                      </FilterDropdown>
                    </th>

                    <th
                      className="cursor-pointer px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase hover:bg-gray-100"
                      onClick={() => handleSort('nombre_enveloppes')}
                    >
                      <div className="flex items-center">
                        Nb. Plis
                        {getSortIcon('nombre_enveloppes')}
                      </div>
                    </th>

                    <th
                      className="cursor-pointer px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase hover:bg-gray-100"
                      onClick={() => handleSort('sous_affranchissement')}
                    >
                      <div className="flex items-center">
                        Sous-aff
                        {getSortIcon('sous_affranchissement')}
                      </div>
                    </th>

                    <th
                      className="cursor-pointer px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase hover:bg-gray-100"
                      onClick={() => handleSort('created_at')}
                    >
                      <div className="flex items-center">
                        Date de création
                        {getSortIcon('created_at')}
                      </div>
                    </th>

                    <th className="relative px-6 py-3">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>

                <tbody className="divide-y divide-gray-200 bg-white">
                  {lots?.items.map((lot) => {
                    const statusConfig = getStatusLotConfig(lot.statut)
                    return (
                      <tr
                        key={lot.id}
                        className="transition-colors hover:bg-gray-50"
                      >
                        {/* ID */}
                        <td className="px-6 py-4 whitespace-nowrap">
                          <Link
                            to="/lots-expediteurs/$lotExpediteurId"
                            params={{ lotExpediteurId: lot.id.toString() }}
                            className="font-medium text-blue-600 hover:text-blue-800"
                          >
                            #{lot.id}
                          </Link>
                        </td>

                        {/* Expéditeur */}
                        <td className="px-6 py-4">
                          <div className="flex items-center">
                            <div className="ml-3">
                              <div className="font-medium text-gray-900">
                                {lot.expediteur.nom}
                              </div>
                              {lot.expediteur.ville && (
                                <div className="text-sm text-gray-500">
                                  {lot.expediteur.ville}
                                </div>
                              )}
                            </div>
                          </div>
                        </td>

                        {/* Casiers */}
                        <td className="px-6 py-4">
                          <div className="flex flex-col gap-1">
                            {lot.casier && (
                              <div className="flex items-center">
                                <FaBox className="mr-2 h-4 w-4 text-blue-500" />
                                <span className="font-medium text-blue-700">
                                  {lot.casier.numero}
                                </span>
                                <span className="ml-1 rounded bg-blue-100 px-1.5 py-0.5 text-xs text-blue-600">
                                  actuel
                                </span>
                              </div>
                            )}
                            {lot.casiers
                              ?.filter((c) => c.id !== lot.casier?.id)
                              .slice(0, 2)
                              .map((casier) => (
                                <div
                                  key={casier.id}
                                  className="flex items-center text-sm text-gray-500"
                                >
                                  <FaBox className="mr-2 h-3 w-3" />
                                  {casier.numero}
                                </div>
                              ))}
                            {lot.casiers && lot.casiers.length > 3 && (
                              <div className="text-xs text-gray-400">
                                +{lot.casiers.length - 3} autres
                              </div>
                            )}
                          </div>
                        </td>

                        {/* Site */}
                        <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900">
                          {lot.site ? lot.site.nom : '-'}
                        </td>

                        {/* Statut */}
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold ${statusConfig.border} ${statusConfig.bg} ${statusConfig.text}`}
                          >
                            <span className="mr-1">{statusConfig.icon}</span>
                            {statusConfig.label}
                          </span>
                        </td>

                        {/* Nb Plis */}
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center text-sm text-gray-900">
                            <FaEnvelope className="mr-2 h-4 w-4 text-gray-400" />
                            <span className="font-medium">
                              {lot.nombre_enveloppes}
                            </span>
                          </div>
                        </td>

                        {/* Sous-aff */}
                        <td className="px-6 py-4 whitespace-nowrap">
                          {lot.valorisation ? (
                            <div className="flex items-center text-sm">
                              <FaEuroSign className="mr-2 h-4 w-4 text-orange-400" />
                              <span className="font-medium text-orange-600">
                                {lot.valorisation.postage.montant_sous_affranchissement.toFixed(
                                  2
                                )}
                                €
                              </span>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-400">-</span>
                          )}
                        </td>

                        {/* Date */}
                        <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500">
                          {formatDate(lot.created_at, 'DD/MM/YYYY')}
                          <div className="text-xs text-gray-400">
                            {formatDate(lot.created_at, 'HH:mm')}
                          </div>
                        </td>

                        {/* Actions */}
                        <td className="px-6 py-4 text-right text-sm font-medium whitespace-nowrap">
                          <ActionDropdown
                            lot={lot}
                            statusOptions={statusOptions}
                            onStatusChange={handleStatusChange}
                            onViewValorisation={handleViewValorisation}
                            onRequestNewCasier={handleRequestNewCasier}
                            onExportCsv={handleExportCsv}
                          />
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            )}

            {/* Pagination */}
            {lots && lots.total_pages > 1 && (
              <div className="border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="flex sm:flex-1 sm:items-center sm:justify-between">
                    <div>
                      <p className="text-sm text-gray-700">
                        Affichage de{' '}
                        <span className="font-medium">
                          {(currentPage - 1) * 10 + 1}
                        </span>{' '}
                        à{' '}
                        <span className="font-medium">
                          {Math.min(currentPage * 10, lots.total_items)}
                        </span>{' '}
                        sur{' '}
                        <span className="font-medium">{lots.total_items}</span>{' '}
                        résultats
                      </p>
                    </div>

                    <div>
                      <Pagination
                        currentPage={currentPage}
                        totalPages={lots.total_pages}
                        setCurrentPage={setCurrentPage}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Loading Overlay */}
      {isFetching && !isLoading && (
        <div className="fixed top-4 right-4 z-50">
          <div className="flex items-center rounded-lg bg-white px-4 py-2 shadow-lg">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
            <span className="ml-2 text-sm text-gray-600">Actualisation...</span>
          </div>
        </div>
      )}

      <NewLotModal
        isSelectSenderModalOpen={isSelectSenderModalOpen}
        closeModal={closeSelectSenderModal}
        selectedSender={selectedSender}
        handleCreateLot={handleCreateLot}
        setSelectedSender={setSelectedSender}
      />

      <StatusChangeConfirmModal
        isOpen={isConfirmModalOpen}
        onClose={closeConfirmModal}
        selectedLot={selectedLot}
        newStatus={newStatus}
        onConfirm={confirmStatusChange}
      />

      {/* Valorisation Modal */}
      <ValorisationModal
        valorisation={valorisation}
        isOpen={isValorisationModalOpen}
        onClose={closeValorisationModal}
      />
    </div>
  )
}

export const Route = createFileRoute('/_layout/lots-expediteurs/')({
  component: LotsExpediteursPage,
})
