import { createFileRoute } from '@tanstack/react-router'
import useAuth from '@/hooks/useAuth'
import { Role } from '@/models/usersModel'
import DashboardPage from '@/components/Pages/dashboard/DashboardPage'
export const Route = createFileRoute('/_layout/')({
  component: App,
  loader: async () => {
    return {}
  },
})

function App() {
  const { user } = useAuth()

  if (!user || user.role === Role.GUEST) {
    return (
      <div className="max-w-md rounded-md border border-yellow-400 bg-yellow-100 p-4 text-center">
        <p className="text-yellow-700">
          Vous n'avez pas les permissions nécessaires pour accéder à cette
          application. Veuillez contacter votre administrateur pour obtenir les
          accès.
        </p>
      </div>
    )
  }

  return <DashboardPage />
}

export default App
