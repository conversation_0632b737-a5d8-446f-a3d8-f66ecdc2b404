import { createFileRoute } from '@tanstack/react-router'
import { useState } from 'react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Button } from '@/components/Forms/Button'
import {
  FaBox,
  FaTrash,
  FaEdit,
  Fa<PERSON>ser,
  FaBox<PERSON><PERSON>,
  FaEnvelope,
} from 'react-icons/fa'
import { TbRefresh, TbArrowsExchange } from 'react-icons/tb'
import Modal from '@/components/Forms/Modal'
import { useDisclosure } from '@/hooks/useDisclosure'
import { CasierService } from '@/services/casierService'
import { EnumerationService } from '@/services/enumerationService'
import { SiteService } from '@/services/siteService'
import { toast } from 'react-hot-toast'
import { ApiError } from '@/core/ApiError'
import useAuth from '@/hooks/useAuth'
import { Role } from '@/models/usersModel'
import Pagination from '@/components/Pagination'
import { DeplacementPlisModal } from '@/components/Pages/casiers/DeplacementPlisModal'

export const CasiersPage = () => {
  const { user } = useAuth()
  const isManager = user?.role === Role.MANAGER || user?.role === Role.ADMIN
  const queryClient = useQueryClient()
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [searchTerm, setSearchTerm] = useState<string | undefined>()
  const [selectedCasier, setSelectedCasier] = useState<any>(null)
  const [isEditing, setIsEditing] = useState<boolean>(false)
  const {
    isOpen: isModalOpen,
    onOpen: openModal,
    onClose: closeModal,
  } = useDisclosure()
  const {
    isOpen: isDeplacementModalOpen,
    onOpen: openDeplacementModal,
    onClose: closeDeplacementModal,
  } = useDisclosure()
  const [casierPourDeplacement, setCasierPourDeplacement] = useState<any>(null)
  const [formData, setFormData] = useState({
    numero: '',
    emplacement: '',
    statut: '',
    site_id: 0,
  })

  const {
    data: pageData,
    error,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ['casiers', currentPage, searchTerm],
    queryFn: () =>
      CasierService.getCasiers((currentPage - 1) * 10, 10, searchTerm),
    staleTime: 1000 * 60 * 5,
  })

  const casiers = pageData?.items ?? []
  const totalPages = pageData?.total_pages ?? 1

  const { data: statutOptions = [] } = useQuery({
    queryKey: ['casierStatut'],
    queryFn: () => EnumerationService.getCasierStatut(),
  })

  const { data: sites = [] } = useQuery({
    queryKey: ['sites'],
    queryFn: () => SiteService.getSites(),
  })

  const createCasierMutation = useMutation({
    mutationFn: (casier: any) => CasierService.createCasier(casier),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['casiers'] })
      toast.success('Casier créé avec succès')
      closeModal()
    },
    onError: (error: ApiError) => {
      const errorDetail = (error.body as any)?.detail || 'Erreur inconnue'
      toast.error(`Erreur lors de la création du casier: ${errorDetail}`)
      console.error('Erreur:', error)
    },
  })

  const updateCasierMutation = useMutation({
    mutationFn: ({ id, casier }: { id: number; casier: any }) =>
      CasierService.updateCasier(id, casier),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['casiers'] })
      toast.success('Casier mis à jour avec succès')
      closeModal()
    },
    onError: (error: ApiError) => {
      const errorDetail = (error.body as any)?.detail || 'Erreur inconnue'
      toast.error(`Erreur lors de la mise à jour du casier: ${errorDetail}`)
      console.error('Erreur:', error)
    },
  })

  const deleteCasierMutation = useMutation({
    mutationFn: (id: number) => CasierService.deleteCasier(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['casiers'] })
      toast.success('Casier supprimé avec succès')
    },
    onError: (error: ApiError) => {
      const errorDetail = (error.body as any)?.detail || 'Erreur inconnue'
      toast.error(`Erreur lors de la suppression du casier: ${errorDetail}`)
      console.error('Erreur:', error)
    },
  })

  const handleOpenCreateModal = () => {
    setIsEditing(false)
    setFormData({
      numero: '',
      emplacement: '',
      statut: statutOptions.length > 0 ? statutOptions[0] : '',
      site_id: sites.length > 0 ? sites[0].id : 0,
    })
    openModal()
  }

  const handleOpenEditModal = (casier: any) => {
    setIsEditing(true)
    setSelectedCasier(casier)
    setFormData({
      numero: casier.numero,
      emplacement: casier.emplacement || '',
      statut: casier.statut,
      site_id: casier.site_id,
    })
    openModal()
  }

  const handleDeleteCasier = (id: number) => {
    if (confirm('Êtes-vous sûr de vouloir supprimer ce casier ?')) {
      deleteCasierMutation.mutate(id)
    }
  }

  const handleOpenDeplacementModal = (casier: any) => {
    setCasierPourDeplacement(casier)
    openDeplacementModal()
  }

  const handleCloseDeplacementModal = () => {
    setCasierPourDeplacement(null)
    closeDeplacementModal()
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (isEditing && selectedCasier) {
      updateCasierMutation.mutate({
        id: selectedCasier.id,
        casier: formData,
      })
    } else {
      createCasierMutation.mutate(formData)
    }
  }

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: name === 'site_id' ? parseInt(value) : value,
    }))
  }

  return (
    <div className="flex min-h-screen w-full flex-col bg-gradient-to-b from-blue-50 to-white">
      {/* Page Header */}
      <header className="border-b border-gray-200 p-4">
        <h1 className="text-2xl font-extrabold text-gray-700">
          Gestion des casiers
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          Gérez les casiers pour le tri des courriers.
        </p>
      </header>

      {/* Main content area */}
      <main className="mx-auto flex w-full max-w-6xl flex-1 flex-col gap-4 p-4">
        {/* Filters Card */}
        <div className="flex flex-col gap-4 rounded-lg bg-white p-4 shadow-sm md:flex-row md:items-end">
          <div>
            <label
              htmlFor="searchTerm"
              className="block text-sm font-semibold text-gray-600"
            >
              Recherche
            </label>
            <input
              id="searchTerm"
              type="text"
              value={searchTerm || ''}
              onChange={(e) => setSearchTerm(e.target.value || undefined)}
              placeholder="Numéro ou emplacement ou site"
              className="mt-1 block w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
            />
          </div>
          <div className="flex-grow"></div>
          <Button
            onClick={() =>
              queryClient.invalidateQueries({ queryKey: ['casiers'] })
            }
            variant="outline"
            className="flex items-center"
            disabled={isFetching}
          >
            <TbRefresh
              className={`h-5 w-5 ${isFetching ? 'animate-spin' : ''}`}
            />
            <span className="ml-2">Actualiser</span>
          </Button>
          <Button
            onClick={handleOpenCreateModal}
            color="green"
            className="flex items-center"
          >
            <span className="ml-2">Nouveau casier</span>
          </Button>
        </div>

        {/* Casiers Table */}
        <div className="rounded-lg bg-white p-4 shadow-sm">
          {isLoading ? (
            <div className="flex h-64 items-center justify-center">
              <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-500"></div>
            </div>
          ) : error ? (
            <div className="flex h-64 items-center justify-center text-red-500">
              Une erreur est survenue lors du chargement des casiers.
            </div>
          ) : casiers && casiers.length === 0 ? (
            <div className="flex h-64 flex-col items-center justify-center text-gray-500">
              <FaBox className="mb-2 h-12 w-12 opacity-20" />
              <p>Aucun casier trouvé.</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full table-auto border-collapse text-sm">
                <thead className="border-b border-gray-200 text-left text-xs text-gray-500 uppercase">
                  <tr>
                    <th className="px-4 py-3">ID</th>
                    <th className="px-4 py-3">Numéro</th>
                    <th className="px-4 py-3">Emplacement</th>
                    <th className="px-4 py-3">Statut</th>
                    <th className="px-4 py-3">Site</th>
                    <th className="px-4 py-3">Lot Expéditeur</th>
                    <th className="px-4 py-3">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100 text-gray-700">
                  {casiers?.map((casier) => (
                    <tr key={casier.id} className="hover:bg-blue-50">
                      <td className="px-4 py-3">{casier.id}</td>
                      <td className="px-4 py-3">{casier.numero}</td>
                      <td className="px-4 py-3">{casier.emplacement || '-'}</td>
                      <td className="px-4 py-3">
                        <span
                          className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                            casier.statut == "OCCUPE"
                              ? 'bg-blue-100 text-blue-800' // Si lot_expediteur existe, afficher comme OCCUPE
                              : casier.statut === 'DISPONIBLE'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {casier.statut}
                        </span>
                      </td>
                      <td className="px-4 py-3">
                        {casier.site ? casier.site.nom : '-'}
                      </td>
                      <td className="px-4 py-3">
                        {casier.lot_expediteur &&
                        casier.lot_expediteur.expediteur ? (
                          <div className="flex flex-col text-sm text-gray-500">
                            <div className="flex items-center">
                              <FaUser className="mr-2 h-4 w-4" />
                              {casier.lot_expediteur.expediteur.nom}
                            </div>
                            <div className="flex items-center">
                              <div className="flex items-center">
                                <FaBoxOpen className="mr-2 h-4 w-4" />
                                <span>#{casier.lot_expediteur.id}</span>
                              </div>
                              <div className="ml-4 flex items-center">
                                <FaEnvelope className="mr-2 h-4 w-4" />
                                <span>
                                  {casier.lot_expediteur.nombre_enveloppes || 0}
                                </span>
                              </div>
                            </div>
                          </div>
                        ) : (
                          '-'
                        )}
                      </td>
                      <td className="px-4 py-3">
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            onClick={() => handleOpenEditModal(casier)}
                          >
                            <FaEdit />
                          </Button>
                          {/* Bouton de déplacement - uniquement pour les managers et si le casier a des plis */}
                          {isManager && casier.lot_expediteur && (
                            <Button
                              variant="outline"
                              color="blue"
                              onClick={() => handleOpenDeplacementModal(casier)}
                              title="Déplacer les plis vers un autre site"
                            >
                              <TbArrowsExchange />
                            </Button>
                          )}
                          <Button
                            variant="outline"
                            color="red"
                            onClick={() => handleDeleteCasier(casier.id)}
                          >
                            <FaTrash />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          {pageData && casiers && casiers.length > 0 && (
            <div className="mt-4 flex items-center justify-between gap-2 border-t border-gray-200 pt-4 sm:flex-row">
              <p className="text-sm text-gray-500">
                Affichage de{' '}
                <span className="font-medium">
                  {(currentPage - 1) * 10 + 1}
                </span>{' '}
                à{' '}
                <span className="font-medium">
                  {Math.min(currentPage * 10, pageData!.total_items)}
                </span>{' '}
                sur <span className="font-medium">{pageData?.total_items}</span>{' '}
                résultats
              </p>
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                setCurrentPage={setCurrentPage}
              />
            </div>
          )}
        </div>
      </main>

      {/* Modal pour création/édition de casier */}
      <Modal
        title={isEditing ? 'Modifier un casier' : 'Créer un nouveau casier'}
        isShown={isModalOpen}
        closeModal={closeModal}
      >
        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <div>
            <label
              htmlFor="numero"
              className="block text-sm font-medium text-gray-700"
            >
              Numéro du casier
            </label>
            <input
              type="text"
              id="numero"
              name="numero"
              value={formData.numero}
              onChange={handleInputChange}
              required
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:outline-none sm:text-sm"
            />
          </div>

          <div>
            <label
              htmlFor="emplacement"
              className="block text-sm font-medium text-gray-700"
            >
              Emplacement
            </label>
            <input
              type="text"
              id="emplacement"
              name="emplacement"
              value={formData.emplacement}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:outline-none sm:text-sm"
            />
          </div>

          <div>
            <label
              htmlFor="statut"
              className="block text-sm font-medium text-gray-700"
            >
              Statut
            </label>
            <select
              id="statut"
              name="statut"
              value={formData.statut}
              onChange={handleInputChange}
              required
              className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:outline-none sm:text-sm"
            >
              {statutOptions.map((statut) => (
                <option key={statut} value={statut}>
                  {statut}
                </option>
              ))}
            </select>
          </div>

          {isManager && (
            <div>
              <label
                htmlFor="site_id"
                className="block text-sm font-medium text-gray-700"
              >
                Site
              </label>
              <select
                id="site_id"
                name="site_id"
                value={formData.site_id}
                onChange={handleInputChange}
                required
                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:outline-none sm:text-sm"
              >
                <option value="">Sélectionner un site</option>
                {sites.map((site) => (
                  <option key={site.id} value={site.id}>
                    {site.nom}
                  </option>
                ))}
              </select>
            </div>
          )}

          <div className="mt-6 flex justify-end space-x-3">
            <Button variant="outline" onClick={closeModal} type="button">
              Annuler
            </Button>
            <Button type="submit">
              {isEditing ? 'Mettre à jour' : 'Créer'}
            </Button>
          </div>
        </form>
      </Modal>

      {/* Modal de déplacement de plis */}
      <DeplacementPlisModal
        isOpen={isDeplacementModalOpen}
        onClose={handleCloseDeplacementModal}
        casierSource={casierPourDeplacement}
      />
    </div>
  )
}

export const Route = createFileRoute('/_layout/casiers')({
  component: CasiersPage,
})
