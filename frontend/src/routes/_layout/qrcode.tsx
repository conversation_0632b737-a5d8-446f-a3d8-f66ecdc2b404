import { useState, useEffect } from 'react'
import { createFileRoute } from '@tanstack/react-router'
import { Button } from '@/components/Forms/Button'
import { TbRefresh } from 'react-icons/tb'
import { toast } from 'react-hot-toast'
import { UsersService } from '@/services/usersService'

// Composant pour générer un QR code
function GenerateQRCode() {
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchQRCode = async (newToken: boolean = false) => {
    setLoading(true)
    setError(null)
    try {
      const response = await UsersService.getQrCodeBlob(newToken)
      const url = URL.createObjectURL(response)
      setQrCodeUrl(url)
    } catch (err) {
      console.log(err)
      setError('Erreur lors de la génération du QR code')
      toast.error('Impossible de générer le QR code')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchQRCode(false)

    // Nettoyer l'URL de l'objet lors du démontage du composant
    return () => {
      if (qrCodeUrl) {
        URL.revokeObjectURL(qrCodeUrl)
      }
    }
  }, [])

  return (
    <div className="flex w-full flex-col items-center gap-4 p-4">
      <h1 className="text-2xl font-bold">Associer un appareil mobile</h1>

      <div className="w-full max-w-lg rounded-lg border border-gray-200 bg-white p-4">
        <div className="mb-4 text-center">
          <p className="text-gray-700">
            Scannez ce QR code avec votre appareil mobile pour l'associer à
            votre compte et prendre des photos de vos enveloppes.
          </p>
        </div>

        <div className="flex flex-col items-center justify-center">
          {loading ? (
            <div className="flex h-64 w-64 items-center justify-center rounded-lg bg-gray-100">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-blue-500 border-t-transparent"></div>
            </div>
          ) : error ? (
            <div className="flex h-64 w-64 items-center justify-center rounded-lg bg-red-50 p-4 text-center text-red-600">
              {error}
              <Button onClick={() => fetchQRCode(true)} className="mt-4">
                <TbRefresh className="mr-2 h-5 w-5" />
                Réessayer
              </Button>
            </div>
          ) : (
            <div className="relative">
              <img
                src={qrCodeUrl || ''}
                alt="QR Code d'association"
                className="h-64 w-64 rounded-lg object-contain"
              />
              <Button
                onClick={() => fetchQRCode(true)}
                className="absolute -top-2 -right-2"
                title="Générer un nouveau QR code"
              >
                <TbRefresh className="h-5 w-5" />
              </Button>
            </div>
          )}
        </div>

        <div className="mt-4 text-center text-sm text-gray-500">
          <p>
            Ce QR code est unique et lié à votre compte. Ne le partagez pas avec
            d'autres personnes.
          </p>
        </div>
      </div>
    </div>
  )
}

export function QRCodePage() {
  return (
    <div className="flex w-full flex-col items-center gap-4 p-4">
      <h1 className="text-2xl font-bold">QR Code</h1>

      <GenerateQRCode />
    </div>
  )
}

export const Route = createFileRoute('/_layout/qrcode')({
  component: QRCodePage,
})
