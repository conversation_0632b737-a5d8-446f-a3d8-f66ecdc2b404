import { useState, useEffect } from 'react'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { Button } from '@/components/Forms/Button'
import { toast } from 'react-hot-toast'
import { UsersService } from '@/services/usersService'
import { SiteService } from '@/services/siteService'
import { Role, UserPublic } from '@/models/usersModel'
import { Site } from '@/models/site'
import { useDebounce } from '@/hooks/useDebounce'
import { useDisclosure } from '@/hooks/useDisclosure'
import Pagination from '@/components/Pagination'
import { FaUser, FaPlus } from 'react-icons/fa'

import IconButton from '@/components/IconButton'
import { createFileRoute } from '@tanstack/react-router'
import { EditUserModal } from '@/components/Pages/users/EditUserModal'
import { TbEdit } from 'react-icons/tb'
import { CreateUserModal } from '@/components/Pages/users/CreateUserModal'

export function UsersPage() {
  const queryClient = useQueryClient()
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [searchInput, setSearchInput] = useState<string>('')
  const [roleFilter, setRoleFilter] = useState<string | undefined>()
  const [siteFilter, setSiteFilter] = useState<number | undefined>()
  const [editingUser, setEditingUser] = useState<UserPublic | null>(null)

  const searchFilter = useDebounce(searchInput, 500)

  const {
    isOpen: isCreateModalOpen,
    onOpen: openCreateModal,
    onClose: closeCreateModal,
  } = useDisclosure()

  const {
    isOpen: isEditModalOpen,
    onOpen: openEditModal,
    onClose: closeEditModal,
  } = useDisclosure()

  const {
    data: usersData,
    error,
    isLoading,
    isFetching,
  } = useQuery({
    queryKey: ['users', currentPage, searchFilter, roleFilter, siteFilter],
    queryFn: () =>
      UsersService.readUsers({
        page: currentPage,
        pageSize: 10,
        search: searchFilter || undefined,
        role: roleFilter as Role | undefined,
        site_id: siteFilter,
      }),
    staleTime: 1000 * 60 * 5,
  })

  const { data: sites = [] } = useQuery({
    queryKey: ['sites'],
    queryFn: () => SiteService.getSites(),
    staleTime: 1000 * 60 * 10,
  })

  const createUserMutation = useMutation({
    mutationFn: (userData: {
      email: string
      password: string
      first_name: string
      last_name: string
      role: Role
      site_id: number
    }) => {
      // Transform the data to match your API expectations
      const apiData = {
        email: userData.email,
        password: userData.password,
        full_name: `${userData.first_name} ${userData.last_name}`.trim(),
        role: userData.role,
        site_id: userData.site_id,
      }
      return UsersService.createUser({ requestBody: apiData })
    },
    onSuccess: () => {
      toast.success('Utilisateur créé avec succès')
      closeCreateModal()
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
    onError: (error: any) => {
      const errorMessage =
        error?.body?.detail || "Erreur lors de la création de l'utilisateur"
      toast.error(errorMessage)
    },
  })

  const updateUserMutation = useMutation({
    mutationFn: ({
      userId,
      userData,
    }: {
      userId: number
      userData: { role?: Role; site_id?: number }
    }) => UsersService.updateUser({ userId, requestBody: userData }),
    onSuccess: () => {
      toast.success('Utilisateur mis à jour avec succès')
      closeEditModal()
      setEditingUser(null)
      queryClient.invalidateQueries({ queryKey: ['users'] })
    },
    onError: () => {
      toast.error('Erreur lors de la mise à jour')
    },
  })

  useEffect(() => {
    setCurrentPage(1)
  }, [searchFilter, roleFilter, siteFilter])

  const handleSearchFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value || '')
  }

  const getSiteNameById = (siteId: number) => {
    const site = sites.find((site: Site) => site.id === siteId)
    return site ? site.nom : 'Non attribué'
  }

  const getRoleLabel = (role: string) => {
    const roleLabels = {
      guest: 'Invité',
      user: 'Utilisateur',
      manager: 'Manager',
      admin: 'Administrateur',
    }
    return roleLabels[role as keyof typeof roleLabels] || role
  }

  const getRoleConfig = (role: string) => {
    const configs = {
      admin: {
        bg: 'bg-red-100',
        text: 'text-red-800',
        border: 'border-red-200',
      },
      manager: {
        bg: 'bg-blue-100',
        text: 'text-blue-800',
        border: 'border-blue-200',
      },
      user: {
        bg: 'bg-green-100',
        text: 'text-green-800',
        border: 'border-green-200',
      },
      guest: {
        bg: 'bg-gray-100',
        text: 'text-gray-800',
        border: 'border-gray-200',
      },
    }
    return configs[role as keyof typeof configs] || configs.guest
  }

  return (
    <div className="flex min-h-screen w-full flex-col bg-gradient-to-b from-blue-50 to-white">
      {/* Page Header */}
      <header className="border-b border-gray-200 bg-white p-6 shadow-sm">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Gestion des utilisateurs
            </h1>
            <p className="mt-2 text-gray-600">
              Gérez les utilisateurs et leurs permissions
            </p>
          </div>
          <Button onClick={openCreateModal} className="flex items-center">
            <FaPlus className="mr-2 h-4 w-4" />
            Nouvel utilisateur
          </Button>
        </div>
      </header>

      {/* Main content area */}
      <main className="flex w-full flex-1 flex-col gap-4 p-4 px-32">
        {/* Filters Card */}
        <div className="flex flex-col gap-4 rounded-lg bg-white p-4 shadow-sm md:flex-row md:items-end">
          <div className="flex-1">
            <label
              htmlFor="globalFilter"
              className="block text-sm font-semibold text-gray-600"
            >
              Rechercher par email, nom...
            </label>
            <div className="relative">
              <input
                id="globalFilter"
                type="text"
                placeholder="<EMAIL>"
                value={searchInput}
                onChange={handleSearchFilterChange}
                className="mt-1 w-full rounded-md border border-gray-300 p-2 text-sm shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
              />
              {searchInput !== searchFilter && (
                <div className="absolute top-1/2 right-3 -translate-y-1/2">
                  <div className="h-4 w-4 animate-pulse rounded-full border-2 border-blue-300"></div>
                </div>
              )}
            </div>
          </div>
          <div>
            <label
              htmlFor="roleFilter"
              className="block text-sm font-semibold text-gray-600"
            >
              Rôle
            </label>
            <select
              id="roleFilter"
              value={roleFilter || ''}
              onChange={(e) => setRoleFilter(e.target.value || undefined)}
              className="mt-1 w-full rounded-md border border-gray-300 p-2 text-sm shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
            >
              <option value="">Tous les rôles</option>
              <option value="guest">Invité</option>
              <option value="user">Utilisateur</option>
              <option value="manager">Manager</option>
              <option value="admin">Administrateur</option>
            </select>
          </div>
          <div>
            <label
              htmlFor="siteFilter"
              className="block text-sm font-semibold text-gray-600"
            >
              Site
            </label>
            <select
              id="siteFilter"
              value={siteFilter || ''}
              onChange={(e) =>
                setSiteFilter(
                  e.target.value ? parseInt(e.target.value) : undefined
                )
              }
              className="mt-1 w-full rounded-md border border-gray-300 p-2 text-sm shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
            >
              <option value="">Tous les sites</option>
              {sites.map((site) => (
                <option key={site.id} value={site.id}>
                  {site.nom}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Loading state */}
        {isLoading && (
          <div className="flex flex-1 items-center justify-center rounded-lg border border-dashed border-gray-300 p-4 text-gray-500">
            <div className="flex flex-col items-center">
              <div className="mb-2 h-8 w-8 animate-spin rounded-full border-4 border-gray-300 border-t-blue-600"></div>
              <p>Chargement des utilisateurs...</p>
            </div>
          </div>
        )}

        {/* Error state */}
        {!isLoading && error && (
          <div className="rounded-lg bg-red-50 p-4 text-red-700">
            {error instanceof Error ? error.message : 'Erreur de chargement'}
          </div>
        )}

        {/* No data state */}
        {!isLoading && !error && usersData && usersData.total_items === 0 && (
          <div className="flex flex-1 items-center justify-center rounded-lg border border-dashed border-gray-300 p-4 text-sm text-gray-400">
            Aucun utilisateur trouvé
          </div>
        )}

        {/* Table Card */}
        {!isLoading && !error && usersData && usersData.items.length > 0 && (
          <div className="rounded-lg bg-white shadow-sm">
            <div className="overflow-x-auto">
              {isFetching && !isLoading && (
                <div className="absolute top-4 right-4">
                  <div className="h-5 w-5 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
                </div>
              )}
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 text-left text-xs text-nowrap text-gray-500 uppercase">
                  <tr>
                    <th className="px-4 py-3">Utilisateur</th>
                    <th className="px-4 py-3">Email</th>
                    <th className="px-4 py-3">Rôle</th>
                    <th className="px-4 py-3">Site</th>
                    <th className="px-4 py-3">Statut</th>

                    <th className="px-4 py-3"></th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100 text-gray-700">
                  {usersData.items.map((user) => {
                    const roleConfig = getRoleConfig(user.role)
                    return (
                      <tr key={user.id} className="hover:bg-blue-50">
                        <td className="px-4 py-3">
                          <div className="flex items-center">
                            <FaUser className="mr-3 h-4 w-4 text-gray-400" />
                            <div>
                              <div className="font-medium text-gray-900">
                                {user.full_name || 'Nom non renseigné'}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3 font-medium">{user.email}</td>
                        <td className="px-4 py-3">
                          <span
                            className={`inline-flex items-center rounded-full px-3 py-1 text-xs font-semibold ${roleConfig.bg} ${roleConfig.text} ${roleConfig.border} border`}
                          >
                            {getRoleLabel(user.role)}
                          </span>
                        </td>
                        <td className="px-4 py-3">
                          {getSiteNameById(user.site_id)}
                        </td>
                        <td className="px-4 py-3">
                          <span
                            className={`inline-flex items-center rounded-full px-3 py-1 text-xs font-semibold ${
                              user.is_active
                                ? 'border-green-200 bg-green-100 text-green-800'
                                : 'border-red-200 bg-red-100 text-red-800'
                            } border`}
                          >
                            {user.is_active ? 'Actif' : 'Inactif'}
                          </span>
                        </td>

                        <td className="px-4 py-3">
                          <IconButton
                            variant="ghost"
                            ariaLabel="Modifier l'utilisateur"
                            icon={TbEdit}
                            colorScheme="primary"
                            onClick={() => {
                              setEditingUser(user)
                              openEditModal()
                            }}
                            className="flex items-center justify-center"
                          />
                        </td>
                      </tr>
                    )
                  })}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="mt-4 flex flex-col items-center justify-between gap-2 border-t border-gray-200 p-6 pt-4 sm:flex-row">
              <p className="text-sm text-gray-500">
                Affichage de{' '}
                <span className="font-medium">
                  {(currentPage - 1) * 10 + 1}
                </span>{' '}
                à{' '}
                <span className="font-medium">
                  {Math.min(currentPage * 10, usersData.total_items)}
                </span>{' '}
                sur <span className="font-medium">{usersData.total_items}</span>{' '}
                résultats
              </p>
              <Pagination
                currentPage={currentPage}
                totalPages={usersData.total_pages}
                setCurrentPage={setCurrentPage}
              />
            </div>
          </div>
        )}
      </main>

      <CreateUserModal
        sites={sites}
        isOpen={isCreateModalOpen}
        onClose={closeCreateModal}
        onSave={(userData) => {
          createUserMutation.mutate(userData)
        }}
        isLoading={createUserMutation.isPending}
      />

      {/* Edit User Modal */}
      <EditUserModal
        user={editingUser}
        sites={sites}
        isOpen={isEditModalOpen}
        onClose={() => {
          closeEditModal()
          setEditingUser(null)
        }}
        onSave={(userData) => {
          if (editingUser) {
            updateUserMutation.mutate({
              userId: editingUser.id,
              userData,
            })
          }
        }}
        isLoading={updateUserMutation.isPending}
      />
    </div>
  )
}

export const Route = createFileRoute('/_layout/users')({
  component: UsersPage,
})
