import { useState, useRef, useCallback } from 'react'
import { But<PERSON> } from '@/components/Forms/Button'
import {
  TbCamera,
  TbCameraOff,
  TbTrash,
  TbScan,
  TbGridDots,
  TbCircleCheck,
  TbCircleX,
} from 'react-icons/tb'
import { createFileRoute } from '@tanstack/react-router'
import { toast } from 'react-hot-toast'
import { useMutation } from '@tanstack/react-query'

// Types and services
import type { ValidationResponse } from '@/models/affranchissement'
import { AffranchissementService } from '@/services/affranchissementService'

import { CodeInputWithScanner } from '@/components/Pages/control/CodeInputWithScanner'

export const Route = createFileRoute('/_layout/scan')({
  component: ScanPage,
})

export function ScanPage() {
  const [isScanning, setIsScanning] = useState(true)
  const [error, setError] = useState<string>()
  const [batchMode, setBatchMode] = useState(true) // Default: batch mode

  // Store multiple scan results
  const [scanResults, setScanResults] = useState<
    Map<string, ValidationResponse>
  >(new Map())
  // Keep track of scanned codes to avoid duplicates
  const scannedCodesRef = useRef<Set<string>>(new Set())

  // Mutation to verify affranchissement codes
  const mutation = useMutation({
    mutationFn: (code: string) =>
      AffranchissementService.verifierAffranchissement({ code }),
    onSuccess: (response, code) => {
      // Save new result
      setScanResults((prev) => {
        const newMap = new Map(prev)
        newMap.set(code, response)
        return newMap
      })

      // Show toast notifications
      if (!response.is_valid) {
        toast.error(response.message || "Code d'affranchissement invalide")
      } else {
        toast.success("Code d'affranchissement valide")
      }
    },
    onError: () => {
      setError("Erreur lors de la vérification de l'affranchissement")
      toast.error('Erreur lors de la vérification')
    },
  })

  // Called whenever a code is successfully scanned
  const handleScan = useCallback(
    (scannedCode: string) => {
      if (scannedCode && !scannedCodesRef.current.has(scannedCode)) {
        scannedCodesRef.current.add(scannedCode)
        mutation.mutate(scannedCode)
      }
    },
    [mutation]
  )

  // Handle manual code input
  const handleManualInput = useCallback(
    (code: string) => {
      if (!scannedCodesRef.current.has(code)) {
        handleScan(code)
      }
    },
    [handleScan]
  )

  // Clear all scanned codes/results
  const clearScans = useCallback(() => {
    setScanResults(new Map())
    scannedCodesRef.current.clear()
    setError(undefined)
  }, [])

  // Remove a single result
  const removeScan = useCallback((code: string) => {
    setScanResults((prev) => {
      const newMap = new Map(prev)
      newMap.delete(code)
      return newMap
    })
    scannedCodesRef.current.delete(code)
  }, [])

  // Toggle single vs. batch (Matrix) scanning mode
  const toggleScanMode = useCallback(() => {
    setBatchMode((prev) => !prev)
  }, [])

  return (
    <div className="flex min-h-screen w-full flex-col bg-gradient-to-b from-blue-50 to-white">
      {/* Page Header */}
      <header className="border-b border-gray-200 p-4">
        <h1 className="text-2xl font-extrabold text-gray-700">
          Scanner des affranchissements
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          Scannez ou saisissez manuellement un code pour le vérifier
        </p>
      </header>

      {/* Main Content: Responsive Columns */}
      <main className="mx-auto flex w-full max-w-6xl flex-1 flex-col gap-4 overflow-hidden p-4 md:flex-row">
        {/* LEFT COLUMN (Scanner UI) */}
        <div className="flex w-full flex-col rounded-lg bg-white p-4 shadow-md md:w-1/2">
          <div className="mb-4">
            <CodeInputWithScanner onSubmit={handleManualInput} />
          </div>

          <div className="mb-4 flex items-center justify-between">
            {/* Toggle single/multiple mode */}
            <Button
              variant="outline"
              onClick={toggleScanMode}
              className="flex items-center gap-2"
            >
              {batchMode ? (
                <>
                  <TbScan className="h-5 w-5" />
                  <span>Mode unique</span>
                </>
              ) : (
                <>
                  <TbGridDots className="h-5 w-5" />
                  <span>Mode multiple</span>
                </>
              )}
            </Button>

            {/* Toggle camera on/off */}
            {isScanning ? (
              <Button
                variant="outline"
                onClick={() => setIsScanning(false)}
                className="flex items-center gap-2 text-red-600"
              >
                <TbCameraOff className="h-5 w-5" />
                <span>Arrêter</span>
              </Button>
            ) : (
              <Button
                variant="outline"
                onClick={() => setIsScanning(true)}
                className="flex items-center gap-2"
              >
                <TbCamera className="h-5 w-5" />
                <span>Activer</span>
              </Button>
            )}
          </div>

          {/* Scanner Preview */}
          {/* <div className="relative aspect-video w-full overflow-hidden rounded-lg bg-gray-100 shadow">
            {isScanning ? (
              <ControlScanner onAddAffranchissement={handleScan} />
            ) : (
              <div className="flex h-full w-full items-center justify-center text-gray-400">
                Caméra désactivée
              </div>
            )}
          </div> */}

          {/* Error Display */}
          {error && (
            <div className="mt-4 rounded-lg bg-red-50 p-4 text-red-600">
              {error}
            </div>
          )}
        </div>

        {/* RIGHT COLUMN (Results) */}
        <div className="flex flex-1 flex-col overflow-y-auto rounded-lg bg-white p-4 shadow-md">
          {/* Results Header */}
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-lg font-bold text-gray-700">
              Résultats
              {scanResults.size > 0 && ` (${scanResults.size})`}
            </h2>
            {scanResults.size > 0 && (
              <Button
                variant="outline"
                onClick={clearScans}
                className="flex items-center gap-2 text-sm"
              >
                <TbTrash className="h-4 w-4" />
                <span>Effacer tout</span>
              </Button>
            )}
          </div>

          {/* Results List */}
          {scanResults.size === 0 ? (
            <div className="flex h-full flex-col items-center justify-center rounded-lg border border-dashed border-gray-300 p-4 text-sm text-gray-400">
              Aucun code scanné
            </div>
          ) : (
            <div className="space-y-4">
              {Array.from(scanResults.entries()).map(([code, result]) => {
                const isValid = result.is_valid
                return (
                  <div
                    key={code}
                    className={`relative rounded-lg p-4 transition-all duration-300 ${
                      isValid
                        ? 'bg-green-50 text-green-800'
                        : 'bg-red-50 text-red-800'
                    }`}
                  >
                    {/* Delete Button */}
                    <button
                      onClick={() => removeScan(code)}
                      className="absolute top-2 right-2 text-gray-400 hover:text-gray-600"
                    >
                      <TbTrash className="h-5 w-5" />
                    </button>

                    {/* Valid/Invalid Status */}
                    <div className="flex items-center gap-2 font-bold">
                      {isValid ? (
                        <>
                          <TbCircleCheck className="h-5 w-5" />
                          <span>Affranchissement valide</span>
                        </>
                      ) : (
                        <>
                          <TbCircleX className="h-5 w-5" />
                          <span>Affranchissement invalide</span>
                        </>
                      )}
                    </div>

                    {/* Affranchissement Details */}
                    {result.affranchissement && (
                      <div className="mt-2 text-sm">
                        <div>
                          <span className="font-semibold">Code:</span>{' '}
                          {result.affranchissement.code}
                        </div>
                        {result.affranchissement.type && (
                          <div>
                            <span className="font-semibold">Type:</span>{' '}
                            {result.affranchissement.type}
                          </div>
                        )}
                        {result.affranchissement.montant && (
                          <div>
                            <span className="font-semibold">Montant:</span>{' '}
                            {result.affranchissement.montant}€
                          </div>
                        )}
                        {result.affranchissement.verifications &&
                          result.affranchissement.verifications?.length > 0 && (
                            <div className="mt-2 space-y-1">
                              {result.affranchissement.verifications.map(
                                (verification, index) => (
                                  <div key={index}>
                                    • {verification.message}
                                  </div>
                                )
                              )}
                            </div>
                          )}
                      </div>
                    )}

                    {/* Additional Message */}
                    {result.message && (
                      <div className="mt-2 text-sm">{result.message}</div>
                    )}
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
