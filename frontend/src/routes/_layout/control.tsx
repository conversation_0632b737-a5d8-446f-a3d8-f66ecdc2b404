import { createFileRoute } from '@tanstack/react-router'
import { z } from 'zod'
import { ControlContext } from '@/contexts/Control'
import { useContext, useEffect } from 'react'
import { ControlTerminer } from '@/components/Pages/control/ControlTerminer'
import { ControlAffranchissementMain } from '@/components/Pages/control/ControlAffranchissementMain'
import useAuth from '@/hooks/useAuth'
import { TbPlus } from 'react-icons/tb'
import { Button } from '@/components/Forms/Button'
import { useNavigate } from '@tanstack/react-router'
import { formatDate } from '@/utils/datesUtils'

export const Route = createFileRoute('/_layout/control')({
  component: RouteComponent,
  validateSearch: (searchParams) => {
    const schema = z.object({
      id: z.number().optional(),
    })
    return schema.parse(searchParams)
  },
})

function RouteComponent() {
  const {
    currentEnveloppe,
    setEnveloppeKey,
    isFinished,
    setIsFinished,
    resetAll,
  } = useContext(ControlContext)
  const search = Route.useSearch()
  const { id } = search
  const navigate = useNavigate()

  useEffect(() => {
    // When the route changes, fetch the envelope (or edition version if no id)
    setIsFinished(false)
    setEnveloppeKey(id ?? 'edition')
  }, [id, setEnveloppeKey, setIsFinished])

  const renderContent = () => {
    if (currentEnveloppe === undefined && id != undefined) {
      return <div>Enveloppe non trouvée</div>
    }
    if (currentEnveloppe && isFinished) return <ControlTerminer />
    return <ControlAffranchissementMain />
  }

  const { user } = useAuth()

  // Determine which badge to show based on enveloppeKey
  const renderBadge = () => {
    if (id === undefined) {
      return (
        <span className="ml-3 rounded-full bg-green-100 px-3 py-1 text-xs font-medium text-green-800">
          Nouveau
        </span>
      )
    } else {
      return (
        <>
          <span className="ml-3 rounded-full bg-blue-100 px-3 py-1 text-xs font-medium text-blue-800">
            Modification
          </span>
          <span className="ml-3 rounded-full bg-gray-100 px-3 py-1 text-xs font-medium text-gray-800">
            Mis à jour le {formatDate(currentEnveloppe?.updated_at)}
          </span>
        </>
      )
    }
  }

  // Handle creating a new envelope
  const handleCreateNewEnvelope = () => {
    resetAll()
    setEnveloppeKey('edition')
    navigate({ to: '/control' }) // Navigate without the id parameter
  }

  return (
    <div className="h-screen w-full">
      <div className="flex flex-grow flex-col gap-2 p-8">
        <div className="flex flex-row items-center">
          <h1 className="flex flex-grow items-center text-2xl font-bold text-blue-800">
            Contrôler Plis - {user?.site?.nom}
            {!isFinished && renderBadge()}
          </h1>

          {/* New Envelope button - only show when in modification mode */}
          {!isFinished && (
            <Button
              color="green"
              variant="light"
              className="flex items-center gap-2"
              onClick={handleCreateNewEnvelope}
            >
              <TbPlus className="h-5 w-5" />
              <span>Commencer un nouveau pli</span>
            </Button>
          )}
        </div>
        {renderContent()}
      </div>
    </div>
  )
}
