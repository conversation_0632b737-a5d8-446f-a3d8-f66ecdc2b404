import { createFileRoute } from '@tanstack/react-router'
import {
  useQuery,
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from '@tanstack/react-query'
import {
  FaClock,
  FaEnvelope,
  FaExclamationTriangle,
  <PERSON>a<PERSON><PERSON>,
  FaStamp,
  FaTrash,
} from 'react-icons/fa'
import { useState, useEffect } from 'react'
import type { Enveloppe, Valorisation } from '@/models/enveloppe'
import { getStatusPliConfig } from '@/utils/statusMetadatasHelpers'

import { EnveloppeService } from '@/services/enveloppeService'
import { EnumerationService } from '@/services/enumerationService'
import { Button } from '@/components/Forms/Button'
import { useDisclosure } from '@/hooks/useDisclosure'
import { ValorisationModal } from '@/components/Pages/control/ValorisationModal'
import { formatDate } from '@/utils/datesUtils'
import Pagination from '@/components/Pagination'
import { PaginatedResponse } from '@/models/paginatedResponse'
import { useDebounce } from '@/hooks/useDebounce'
import IconButton from '@/components/IconButton'

export const EnveloppesPage = () => {
  const queryClient = useQueryClient()
  const {
    isOpen: isValorisationModalOpen,
    onOpen: openValorisationModal,
    onClose: closeValorisationModal,
  } = useDisclosure()
  const [selectedValorisation, setSelectedValorisation] =
    useState<Valorisation | null>(null)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [searchInput, setSearchInput] = useState<string>('')
  const [statusFilter, setStatusFilter] = useState<string | undefined>()

  // Utiliser useDebounce pour retarder les requêtes de recherche
  const searchFilter = useDebounce(searchInput, 500)

  // Changed from useSuspenseQuery to useQuery to prevent suspense on filter changes
  const {
    data: enveloppes,
    error,
    isLoading,
    isFetching,
  } = useQuery<PaginatedResponse<Enveloppe>>({
    queryKey: ['enveloppes', currentPage, searchFilter, statusFilter],
    queryFn: () =>
      EnveloppeService.getEnveloppes(currentPage, 10, {
        recherche: searchFilter,
        statut: statusFilter,
      }),
    staleTime: 1000 * 60 * 5, // 5 minutes
  })

  // Réinitialiser la page à 1 quand les filtres changent
  useEffect(() => {
    setCurrentPage(1)
  }, [searchFilter, statusFilter])

  const { data: statusOptions = [] } = useSuspenseQuery<string[]>({
    queryKey: ['enveloppesStatus'],
    queryFn: EnumerationService.getEnveloppesStatus,
    staleTime: 1000 * 60 * 5, // 5 minutes
  })

  const deleteEnveloppeMutation = useMutation({
    mutationFn: (id: number) => EnveloppeService.deleteEnveloppe(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['enveloppes'] })
    },
  })

  const handleEnveloppeClick = (id: number) => {
    const url = `/control?id=${id}`

    window.open(url, '_blank')
    // navigate({ to: '/control', search: { id } })
  }

  const handleValorisationClick = (
    e: React.MouseEvent,
    valorisation: Valorisation | null
  ) => {
    e.stopPropagation()
    if (valorisation) {
      setSelectedValorisation(valorisation)
      openValorisationModal()
    }
  }

  const handleDeleteEnveloppe = (e: React.MouseEvent, id: number) => {
    e.stopPropagation()
    if (confirm('Êtes-vous sûr de vouloir supprimer cette enveloppe ?')) {
      deleteEnveloppeMutation.mutate(id)
    }
  }

  // Mise à jour de l'input de recherche sans déclencher immédiatement la requête
  const handleSearchFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchInput(e.target.value || '')
  }

  return (
    <div className="flex min-h-screen w-full flex-col bg-gradient-to-b from-blue-50 to-white">
      {/* Page Header */}
      <header className="border-b border-gray-200 bg-white p-6 shadow-sm">
        <div className="flex items-center justify-start">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Liste des plis traités
            </h1>
            <p className="mt-2 text-gray-600">
              Gérez et visualisez vos plis traités
            </p>
          </div>
        </div>
      </header>

      {/* Main content area */}
      <main className="flex w-full flex-1 flex-col gap-4 p-4 px-32">
        {/* Filters Card */}
        <div className="flex flex-col gap-4 rounded-lg bg-white p-4 shadow-sm md:flex-row md:items-end">
          <div className="flex-1">
            <label
              htmlFor="globalFilter"
              className="block text-sm font-semibold text-gray-600"
            >
              Filtrer par email, expediteur, CAB, ...
            </label>
            <div className="relative">
              <input
                id="globalFilter"
                type="text"
                placeholder="<EMAIL>"
                value={searchInput}
                onChange={handleSearchFilterChange}
                className="mt-1 w-full rounded-md border border-gray-300 p-2 text-sm shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
              />
              {searchInput !== searchFilter && (
                <div className="absolute top-1/2 right-3 -translate-y-1/2">
                  <div className="h-4 w-4 animate-pulse rounded-full border-2 border-blue-300"></div>
                </div>
              )}
            </div>
          </div>
          <div>
            <label
              htmlFor="statusFilter"
              className="block text-sm font-semibold text-gray-600"
            >
              Statut
            </label>
            <select
              id="statusFilter"
              value={statusFilter || ''}
              onChange={(e) => setStatusFilter(e.target.value || undefined)}
              className="mt-1 w-full rounded-md border border-gray-300 p-2 text-sm shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:outline-none"
            >
              <option value="">Tous les statuts</option>
              {statusOptions.map((status) => (
                <option key={status} value={status}>
                  {status}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Initial loading state */}
        {isLoading && (
          <div className="flex flex-1 items-center justify-center rounded-lg border border-dashed border-gray-300 p-4 text-gray-500">
            <div className="flex flex-col items-center">
              <div className="mb-2 h-8 w-8 animate-spin rounded-full border-4 border-gray-300 border-t-blue-600"></div>
              <p>Chargement des données...</p>
            </div>
          </div>
        )}

        {/* Error state */}
        {!isLoading && error && (
          <div className="rounded-lg bg-red-50 p-4 text-red-700">
            {error instanceof Error ? error.message : 'Erreur de chargement'}
          </div>
        )}

        {/* No data state */}
        {!isLoading && !error && enveloppes && enveloppes.total_items === 0 && (
          <div className="flex flex-1 items-center justify-center rounded-lg border border-dashed border-gray-300 p-4 text-sm text-gray-400">
            Aucune enveloppe trouvée
          </div>
        )}

        {/* Table Card */}
        {!isLoading && !error && enveloppes && enveloppes.items.length > 0 && (
          <div className="rounded-lg bg-white shadow-sm">
            <div className="overflow-x-auto">
              {/* Show a subtle loading indicator when fetching new data */}
              {isFetching && !isLoading && (
                <div className="absolute top-4 right-4">
                  <div className="h-5 w-5 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
                </div>
              )}

              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50 text-left text-xs text-nowrap text-gray-500 uppercase">
                  <tr>
                    <th className="px-4 py-3">Expéditeur</th>
                    <th className="px-4 py-3">Poids ( G )</th>
                    <th className="px-4 py-3">Créateur</th>
                    <th className="px-4 py-3">Statut</th>
                    <th className="px-4 py-3">Code S10</th>
                    <th className="px-4 py-3">Coûts</th>
                    <th className="px-4 py-3">Valorisation</th>
                    <th className="px-4 py-3">Mis à jour</th>
                    <th className="px-4 py-3"></th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100 text-gray-700">
                  {enveloppes.items.map((enveloppe) => (
                    <tr
                      key={enveloppe.id}
                      className="cursor-pointer hover:bg-blue-50"
                      onClick={() => handleEnveloppeClick(enveloppe.id)}
                    >
                      <td className="truncate px-4 py-3 font-semibold">
                        <div className="flex items-center">
                          <div className="ml-3">
                            <div className="font-medium text-gray-900">
                              {enveloppe?.expediteur?.nom}
                            </div>
                            {enveloppe?.expediteur?.ville && (
                              <div className="text-sm text-gray-500">
                                {enveloppe?.expediteur?.ville}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        {enveloppe.poids ? (
                          <div className="flex justify-center">
                            {enveloppe.poids}
                          </div>
                        ) : (
                          <div className="flex justify-center">
                            <span className="text-slate-400">-</span>
                          </div>
                        )}
                      </td>
                      <td
                        className="max-w-[150px] truncate px-4 py-3"
                        title={enveloppe.user?.email || '-'}
                      >
                        {enveloppe.user?.email || (
                          <div className="flex justify-center">
                            <span className="text-slate-400">-</span>
                          </div>
                        )}
                      </td>
                      {/* Status Column - Enhanced */}
                      <td className="px-6 py-4">
                        {(() => {
                          const statusConfig = getStatusPliConfig(
                            enveloppe.statut
                          )
                          return (
                            <div className="flex items-center space-x-2">
                              <span
                                className={`inline-flex items-center rounded-full px-3 py-1 text-xs font-semibold ${statusConfig.bg} ${statusConfig.text} ${statusConfig.border} border`}
                              >
                                <span className="mr-1">
                                  {statusConfig.icon}
                                </span>
                                {statusConfig.label}
                              </span>
                            </div>
                          )
                        })()}
                      </td>

                      <td className="px-4 py-3">
                        {enveloppe.affranchissements?.find(
                          (a) => a.categorie === 'CODE' && a.type === 'S10'
                        )?.code || (
                          <div className="flex justify-center">
                            <span className="text-slate-400">-</span>
                          </div>
                        )}
                      </td>
                      <td className="px-4 py-3">
                        {enveloppe.valorisation && (
                          <div className="flex flex-col space-y-1">
                            <span className="flex items-center text-blue-500">
                              <FaEnvelope className="mr-1 h-3 w-3" />
                              {Number(
                                enveloppe.valorisation.postage.cout_enveloppe
                              ).toFixed(2)}
                              €
                            </span>
                            <span className="flex items-center text-yellow-600">
                              <FaStamp className="mr-1 h-3 w-3" />
                              {Number(
                                enveloppe.valorisation.postage
                                  .cout_affranchissements_valide
                              ).toFixed(2)}
                              €
                            </span>
                            <span
                              className={`flex items-center ${Number(enveloppe.valorisation.postage.montant_sous_affranchissement) <= 0 ? 'text-green-500' : 'text-red-600'}`}
                            >
                              <FaExclamationTriangle className="mr-1 h-3 w-3" />
                              {Number(
                                enveloppe.valorisation.postage
                                  .montant_sous_affranchissement
                              ).toFixed(2)}
                              €
                            </span>
                          </div>
                        )}
                      </td>
                      <td className="px-4 py-3">
                        {enveloppe.valorisation ? (
                          <Button
                            className="flex items-center text-xs"
                            variant="outline"
                            onClick={(e) =>
                              handleValorisationClick(e, enveloppe.valorisation)
                            }
                          >
                            Détails
                            <FaEye className="ml-2 h-4 w-4" />
                          </Button>
                        ) : (
                          <div className="flex justify-center">
                            <span className="text-slate-400">-</span>
                          </div>
                        )}
                      </td>
                      <td className="px-4 py-3 text-sm whitespace-nowrap text-gray-600">
                        <span className="inline-flex items-center gap-1">
                          <FaClock className="text-gray-400" />
                          {formatDate(
                            enveloppe.updated_at,
                            'DD/MM/YYYY – HH:mm'
                          )}
                        </span>
                      </td>
                      <td className="px-4 py-3">
                        <IconButton
                          variant="ghost"
                          ariaLabel="Supprimer l'enveloppe"
                          icon={FaTrash}
                          colorScheme="danger"
                          onClick={(e) =>
                            handleDeleteEnveloppe(e, enveloppe.id)
                          }
                          className="flex items-center justify-center"
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            <div className="mt-4 flex flex-col items-center justify-between gap-2 border-t border-gray-200 p-6 pt-4 sm:flex-row">
              <p className="text-sm text-gray-500">
                Affichage de{' '}
                <span className="font-medium">
                  {(currentPage - 1) * 10 + 1}
                </span>{' '}
                à{' '}
                <span className="font-medium">
                  {Math.min(currentPage * 10, enveloppes.total_items)}
                </span>{' '}
                sur{' '}
                <span className="font-medium">{enveloppes.total_items}</span>{' '}
                résultats
              </p>
              <Pagination
                currentPage={currentPage}
                totalPages={enveloppes.total_pages}
                setCurrentPage={setCurrentPage}
              />
            </div>
          </div>
        )}
      </main>

      {/* Modal for Valorisation Details */}
      {selectedValorisation && (
        <ValorisationModal
          valorisation={selectedValorisation}
          isOpen={isValorisationModalOpen}
          onClose={closeValorisationModal}
        />
      )}
    </div>
  )
}

export const Route = createFileRoute('/_layout/enveloppes')({
  component: EnveloppesPage,
})
