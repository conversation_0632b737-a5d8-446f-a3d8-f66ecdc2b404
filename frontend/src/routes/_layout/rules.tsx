import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { ReglesMetierService } from '@/services/reglesMetierService'
import { RegleMetier, TypeRegleMetierEnum } from '@/models/reglesMetier'
import { useForm, Controller } from 'react-hook-form'
import Modal from '@/components/Forms/Modal'
import { But<PERSON> } from '@/components/Forms/Button'
import { TbP<PERSON>, TbLoader, TbInfoCircle } from 'react-icons/tb'
import Pagination from '@/components/Pagination'

import { createFileRoute } from '@tanstack/react-router'
import RuleActionsMenu from '@/components/Pages/rules/RuleActionsMenu'

/* ----------------------------- Badges -------------------------------- */
const badgeClass = {
  SEQUENCE: 'bg-indigo-100 text-indigo-600',
  VALEUR: 'bg-emerald-100 text-emerald-600',
} as const

/* ------------------------ Custom editors ----------------------------- */
const SequenceEditor = ({ control }: { control: any }) => (
  <div className="grid grid-cols-2 gap-4">
    {[
      { key: 'debut', label: 'Début' },
      { key: 'fin', label: 'Fin' },
      { key: 'service', label: 'Service' },
      { key: 'emetteur', label: 'Émetteur' },
      { key: 'valeur', label: 'Valeur (€)', type: 'number' },
    ].map(({ key, label, type }) => (
      <div key={key} className="flex flex-col gap-1">
        <label className="text-xs font-medium text-gray-600">{label}</label>
        <Controller
          name={`valeur.${key}`}
          control={control}
          defaultValue=""
          render={({ field }) => (
            <input
              {...field}
              type={type || "text"}
              step={type === 'number' ? "0.01" : undefined}
              className="rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none"
            />
          )}
        />
      </div>
    ))}
    <div className="col-span-2 flex flex-col gap-1">
      <label className="text-xs font-medium text-gray-600">
        Date d'attribution
      </label>
      <Controller
        name="valeur.date_attribution"
        control={control}
        defaultValue=""
        render={({ field }) => (
          <input
            type="date"
            {...field}
            onChange={(e) => {
              // Convertir le format YYYY-MM-DD en DD/MM/YYYY pour le backend
              if (e.target.value) {
                const [year, month, day] = e.target.value.split('-');
                field.onChange(`${day}/${month}/${year}`);
              } else {
                field.onChange('');
              }
            }}
            // Convertir le format DD/MM/YYYY en YYYY-MM-DD pour l'affichage
            value={field.value ? (() => {
              const [day, month, year] = field.value.split('/');
              return day && month && year ? `${year}-${month}-${day}` : '';
            })() : ''}
            className="rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none"
          />
        )}
      />
    </div>
  </div>
)

const ValeurEditor = ({ control }: { control: any }) => (
  <div className="flex flex-col gap-1">
    <label className="text-xs font-medium text-gray-600">Valeur</label>
    <Controller
      name="valeur.valeur"
      control={control}
      defaultValue=""
      render={({ field }) => (
        <input
          {...field}
          className="rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none"
        />
      )}
    />
  </div>
)

/* ------------------------- Main page --------------------------------- */
export default function RulesPage() {
  const qc = useQueryClient()
  const [search, setSearch] = useState('')
  const [modalOpen, setModalOpen] = useState(false)
  const [editing, setEditing] = useState<RegleMetier | null>(null)

  /* Pagination */
  const PAGE_SIZE = 12
  const [currentPage, setCurrentPage] = useState(1)

  const skip = (currentPage - 1) * PAGE_SIZE

  const { data: ruleResponse, isLoading } = useQuery({
    queryKey: ['rules', search, currentPage],
    queryFn: () => ReglesMetierService.list({ search, limit: PAGE_SIZE, skip }),
  })

  const rules: RegleMetier[] = ruleResponse?.items ?? []
  const total = ruleResponse?.total_items ?? 0
  const totalPages = Math.max(1, Math.ceil(total / PAGE_SIZE))

  const createMutation = useMutation({
    mutationFn: (d: RegleMetier) => ReglesMetierService.create(d),
    onSuccess: () => qc.invalidateQueries({ queryKey: ['rules'] }),
  })
  const updateMutation = useMutation({
    mutationFn: ({ id, body }: { id: number; body: RegleMetier }) =>
      ReglesMetierService.update(id, body),
    onSuccess: () => qc.invalidateQueries({ queryKey: ['rules'] }),
  })
  const deleteMutation = useMutation({
    mutationFn: (id: number) => ReglesMetierService.remove(id),
    onSuccess: () => qc.invalidateQueries({ queryKey: ['rules'] }),
  })

  const { register, handleSubmit, control, reset, watch } =
    useForm<RegleMetier>({
      defaultValues: {
        cle: '',
        type_affranchissement: 'S10' as any,
        type_regle: 'SEQUENCE' as any,
        valeur: {},
        active: true,
      },
    })

  const onSubmit = (data: RegleMetier) => {
    if (editing) {
      updateMutation.mutate({ id: editing.id!, body: data })
    } else {
      createMutation.mutate(data)
    }
    setModalOpen(false)
  }
  const currentType = watch('type_regle')

  return (
    <div className="mx-auto flex flex-col gap-8 p-6 lg:p-10">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-end sm:justify-between">
        <div className="flex flex-col gap-2">
          <h1 className="text-3xl font-bold text-blue-800">Règles métier</h1>
          <div className="flex items-center gap-3">
            <input
              type="text"
              placeholder="Rechercher par clé..."
              className="w-full max-w-xs rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none"
              value={search}
              onChange={(e) => {
                setCurrentPage(1)
                setSearch(e.target.value)
              }}
            />
          </div>
        </div>
        <Button
          variant="primary"
          className="flex items-center gap-2 self-start sm:self-auto"
          onClick={() => {
            reset()
            setEditing(null)
            setModalOpen(true)
          }}
        >
          <TbPlus className="h-5 w-5" /> Nouvelle règle
        </Button>
      </div>

      {/* Table */}
      <div className="rounded-lg border border-gray-200 bg-white shadow-sm">
        <table className="min-w-full divide-y divide-gray-200 text-sm">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left font-medium text-gray-600">
                Clé
              </th>
              <th className="px-4 py-3 text-left font-medium text-gray-600">
                Affranch.
              </th>
              <th className="px-4 py-3 text-left font-medium text-gray-600">
                Type
              </th>
              <th className="px-4 py-3 text-right font-medium text-gray-600">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100">
            {isLoading ? (
              <tr>
                <td colSpan={4} className="px-4 py-10 text-center">
                  <TbLoader className="mx-auto h-6 w-6 animate-spin text-blue-600" />
                </td>
              </tr>
            ) : rules ? (
              rules.map((rule) => (
                <tr key={rule.id} className="hover:bg-gray-50">
                  <td className="px-4 py-2 font-medium whitespace-nowrap text-gray-800">
                    {rule.cle}
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap text-gray-700">
                    {rule.type_affranchissement}
                  </td>
                  <td className="px-4 py-2 whitespace-nowrap">
                    <span
                      className={`rounded-full px-2 py-0.5 text-xs font-semibold ${badgeClass[rule.type_regle]}`}
                    >
                      {rule.type_regle}
                    </span>
                  </td>
                  <td className="px-4 py-2 text-right whitespace-nowrap">
                    <RuleActionsMenu
                      rule={rule}
                      onEdit={(r) => {
                        setEditing(r)
                        reset(r)
                        setModalOpen(true)
                      }}
                      onDelete={(r) => deleteMutation.mutate(r.id!)}
                    />
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={4} className="px-4 py-10 text-center">
                  <TbInfoCircle className="mx-auto h-6 w-6 text-gray-400" />
                  <p className="mt-2 text-gray-500">Aucune règle trouvée</p>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {ruleResponse && (
        <div className="mt-2 flex flex-col items-center justify-between gap-2 border-t border-gray-200 pt-2 sm:flex-row">
          <p className="text-sm text-gray-500">
            Affichage de{' '}
            <span className="font-medium">
              {(currentPage - 1) * PAGE_SIZE + 1}
            </span>{' '}
            à{' '}
            <span className="font-medium">
              {Math.min(currentPage * PAGE_SIZE, ruleResponse.total_items)}
            </span>{' '}
            sur <span className="font-medium">{ruleResponse.total_items}</span>{' '}
            résultats
          </p>
          <Pagination
            currentPage={currentPage}
            setCurrentPage={setCurrentPage}
            totalPages={totalPages}
          />
        </div>
      )}

      {/* Modal */}
      {modalOpen && (
        <Modal
          title={
            <span className="text-lg font-semibold">
              {editing ? 'Modifier la règle' : 'Créer une règle'}
            </span>
          }
          isShown={modalOpen}
          closeModal={() => setModalOpen(false)}
        >
          <form
            className="flex flex-col gap-6"
            onSubmit={handleSubmit(onSubmit)}
          >
            <div className="grid grid-cols-2 gap-4">
              <div className="col-span-2 flex flex-col gap-1">
                <label className="text-xs font-medium text-gray-600">Clé</label>
                <input
                  {...register('cle', { required: true })}
                  className="rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none"
                />
              </div>
              <div className="flex flex-col gap-1">
                <label className="text-xs font-medium text-gray-600">
                  Type affranchissement
                </label>
                <select
                  {...register('type_affranchissement')}
                  className="rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none"
                >
                  <option value="S10">S10</option>
                  <option value="TIMBRE">TIMBRE</option>
                </select>
              </div>
              <div className="flex flex-col gap-1">
                <label className="text-xs font-medium text-gray-600">
                  Type règle
                </label>
                <select
                  {...register('type_regle')}
                  className="rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none"
                >
                  <option value="SEQUENCE">SEQUENCE</option>
                  <option value="VALEUR">VALEUR</option>
                </select>
              </div>
              <div className="col-span-2 flex items-center gap-2">
                <input
                  type="checkbox"
                  id="active"
                  {...register('active')}
                  className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label htmlFor="active" className="text-sm font-medium text-gray-700">
                  Règle active
                </label>
              </div>
            </div>

            {currentType === TypeRegleMetierEnum.SEQUENCE ? (
              <SequenceEditor control={control} />
            ) : (
              <ValeurEditor control={control} />
            )}

            <div className="mt-4 flex justify-end gap-3">
              <Button
                variant="outline"
                type="button"
                onClick={() => setModalOpen(false)}
              >
                Annuler
              </Button>
              <Button
                variant="primary"
                type="submit"
                disabled={createMutation.isPending || updateMutation.isPending}
              >
                {editing ? 'Mettre à jour' : 'Créer'}
              </Button>
            </div>
          </form>
        </Modal>
      )}
    </div>
  )
}

export const Route = createFileRoute('/_layout/rules')({
  component: RulesPage,
})
