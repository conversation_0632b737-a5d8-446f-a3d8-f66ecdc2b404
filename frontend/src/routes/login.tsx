import { isLoggedIn } from '@/hooks/useAuth'
import { createFileRoute, redirect, Link } from '@tanstack/react-router'
import { TbCamera } from 'react-icons/tb'

import Logo from '@/assets/logo.svg'
import { Button } from '@/components/Forms/Button'
import { keycloakService } from '@/services/keycloakService'
export const Route = createFileRoute('/login')({
  component: Login,
  beforeLoad: async () => {
    if (isLoggedIn()) {
      throw redirect({ to: '/' })
    }
  },
})

export default function Login() {

  // Fonction de connexion Keycloak simplifiée
  const handleKeycloakLogin = () => {
    try {
      // Vérifier si le paramètre 'external' est présent dans l'URL
      const urlParams = new URLSearchParams(window.location.search);
      const isExternal = urlParams.has('external');

      // Construire l'URL d'authentification avec ou sans kc_idp_hint
      const loginUrl = keycloakService.buildAuthUrl(isExternal ? '' : 'gaia');
      console.log('Redirection vers:', loginUrl);
      
      // Redirection directe
      window.location.href = loginUrl;
    } catch (error) {
      console.error('Erreur lors de la redirection vers Keycloak:', error);
    }
  };


  return (
    <div className="mx-auto flex min-h-screen items-center justify-center overflow-auto">
      <div className="my-8 flex w-full flex-grow flex-col items-center justify-center space-y-8">
        <div>
          <img
            className="h-16"
            src={Logo}
            alt="Logo La Poste"
            aria-hidden="true"
          />
        </div>
        <div className="relative flex w-full max-w-lg flex-col space-y-4 rounded-md border border-gray-200 bg-white p-8 mb-24">
          <div className="flex flex-col items-start space-y-2">
            <h1 className="text-2xl font-bold">Connectez-vous</h1>
            <div className="mt-2 h-px w-24 bg-primary" />
          </div>

          <div className="flex flex-col items-center justify-center">
              <div className="mt-2 flex flex-col items-center">
                <Button type="submit" variant="primary" onClick={handleKeycloakLogin}>
                  Se connecter avec Keycloak
                </Button>
                
                <div className="mt-6 flex flex-col items-center">
                  <Link
                    to="/login-external"
                    className="flex items-center justify-center gap-2 rounded-md bg-blue-100 px-4 py-2 text-blue-700 hover:bg-blue-200 transition-colors"
                  >
                    <TbCamera className="h-5 w-5" />
                    <span>Associer votre appareil via QR Code</span>
                  </Link>
                </div>
              </div>
            </div>
        </div>
      </div>
    </div>
  )
}
