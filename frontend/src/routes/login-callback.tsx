import { useEffect, useState, useRef } from 'react';
import { useNavigate, createFileRoute } from '@tanstack/react-router';
import { keycloakService } from '@/services/keycloakService';
import { UsersService } from '@/services/usersService';
import { ApiError } from '@/core/ApiError';
import { tokenService } from '@/services/tokenService';

export const Route = createFileRoute("/login-callback")({
  component: Callback,
});

function Callback() {
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);
  const processedRef = useRef(false);

  useEffect(() => {
    // Si déjà traité, ne pas continuer
    if (processedRef.current) return;
    
    const handleCallback = async () => {
      // Marquer comme traité immédiatement
      processedRef.current = true;
      
      try {
        // Récupérer le code d'autorisation de l'URL
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        
        if (!code) {
          throw new Error('Code d\'autorisation manquant');
        }
        
        // Échanger le code contre un token
        await keycloakService.exchangeCodeForToken(code);

        // Tester la connexion
        await UsersService.readUserMe();

        // Rediriger vers la page d'accueil
        navigate({ to: '/' });
      } catch (error) {
        tokenService.remove();
        setError("Une erreur est survenue lors de l'authentification");

        if(error instanceof ApiError) {
          setError("Votre compte est inactif. Veuillez contacter l'administrateur.");
          tokenService.remove();
        } 
      }
    };
    
    handleCallback();
  }, [navigate]);
  
  if (error) {
    return (
      <div className="mx-auto flex min-h-screen items-center justify-center overflow-auto">
        <div className="flex flex-col items-center justify-center">
          <div className="text-red-500 text-center p-4">
            <p className="text-xl">{error}</p>
            <p className="mt-2">
              <a href="/login" className="underline">
                Retour à la page de connexion
              </a>
            </p>
          </div>
        </div>
      </div>
    );
  }

  if(error === null) {
    return (
      <div className="mx-auto flex min-h-screen items-center justify-center overflow-auto">
        <div className="flex flex-col items-center justify-center">
          <p className="text-xl">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mx-auto flex min-h-screen items-center justify-center overflow-auto">
      <div className="flex flex-col items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
        <p className="mt-4">Traitement de l'authentification...</p>
      </div>
    </div>
  );
}

export default Callback; 