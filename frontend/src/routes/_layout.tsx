import { Outlet, createFileRoute, redirect } from '@tanstack/react-router'
import { Suspense } from 'react'

import { LoadingPage } from '@/components/Pages/LoadingPage'
import { SideMenu } from '@/components/SideMenu'
import useAuth, { isLoggedIn } from '@/hooks/useAuth'
import { ControlProvider } from '@/providers/ControlProvider'
import { SiteProvider } from '@/providers/SiteProvider'

export const Route = createFileRoute('/_layout')({
  component: PrivateLayout,
  beforeLoad: async () => {
    if (!isLoggedIn()) {
      throw redirect({
        to: '/login',
      })
    }
  },
})

function PrivateLayout() {
  const { isLoading } = useAuth()

  const isRecette =
    String(import.meta.env.VITE_PUBLIC_ENVIRONMENT).trim() !== 'production'

  if (isLoading) {
    return <LoadingPage />
  }

  if (isLoggedIn()) {
    return (
      <SiteProvider>
        <ControlProvider>
          {/**
           * Use flex across the entire viewport. The SideMenu is flex-shrink-0
           * so it maintains its width. The main content is flex-grow so it
           * expands/collapses accordingly.
           */}
          <div className="flex h-screen w-screen overflow-hidden">
            <SideMenu />
            <main className="flex-grow overflow-y-auto">
              <Suspense fallback={<LoadingPage />}>
                <Outlet />
              </Suspense>
            </main>
            {isRecette && (
              <div className="fixed top-0 right-0 z-50 m-2 rounded-full bg-amber-500 px-3 py-1 text-xs font-bold text-white opacity-50">
                RECETTE
              </div>
            )}
          </div>
        </ControlProvider>
      </SiteProvider>
    )
  }
}
