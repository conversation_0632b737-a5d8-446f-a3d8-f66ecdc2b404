import { OpenAPI } from "@/core/OpenAPI";
import { keycloakService } from "@/services/keycloakService";
import { tokenService } from "@/services/tokenService";

let isRefreshing = false;
let refreshPromise: Promise<any> | null = null;

export const setupAuthInterceptors = () => {

  // Intercepteur pour les requêtes
  OpenAPI.interceptors.request.use(async (config) => {
    // Si le token est expiré, on le rafraîchit avant d'envoyer la requête
    if (tokenService.isStored() && tokenService.isExpired()) {
      try {
        // Utilisation d'une seule promesse pour tous les appels simultanés
        if (!isRefreshing) {
          isRefreshing = true;
          
          const refreshToken = tokenService.get('refresh_token');
          if (!refreshToken) {
            throw new Error("Aucun refresh token disponible");
          }
          
          refreshPromise = keycloakService.refreshToken()
            .then()
            .finally(() => {
              isRefreshing = false;
              refreshPromise = null;
            });
        }
        
        // Attendre que le token soit rafraîchi
        const newToken = await refreshPromise;
        
        // Mettre à jour le header avec le nouveau token
        if (config.headers && newToken) {
          config.headers['Authorization'] = `Bearer ${tokenService.get('access_token')}`;
        }
      } catch (error) {
        console.error("Échec du rafraîchissement du token:", error);
        tokenService.remove()
        window.location.href = '/login';
      }
    }
    
    return config;
  });
};