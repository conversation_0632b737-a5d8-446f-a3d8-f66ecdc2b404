/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as LoginExternalImport } from './routes/login-external'
import { Route as LoginCallbackImport } from './routes/login-callback'
import { Route as LoginImport } from './routes/login'
import { Route as LayoutImport } from './routes/_layout'
import { Route as ExternalImport } from './routes/_external'
import { Route as LayoutIndexImport } from './routes/_layout/index'
import { Route as LayoutUsersImport } from './routes/_layout/users'
import { Route as LayoutScanImport } from './routes/_layout/scan'
import { Route as LayoutRulesImport } from './routes/_layout/rules'
import { Route as LayoutQrcodeImport } from './routes/_layout/qrcode'
import { Route as LayoutEnveloppesImport } from './routes/_layout/enveloppes'
import { Route as LayoutControlImport } from './routes/_layout/control'
import { Route as LayoutChangelogImport } from './routes/_layout/changelog'
import { Route as LayoutCasiersImport } from './routes/_layout/casiers'
import { Route as ExternalMobileImport } from './routes/_external/mobile'
import { Route as LayoutLotsExpediteursIndexImport } from './routes/_layout/lots-expediteurs/index'
import { Route as LayoutLotsExpediteursLotExpediteurIdImport } from './routes/_layout/lots-expediteurs/$lotExpediteurId'

// Create/Update Routes

const LoginExternalRoute = LoginExternalImport.update({
  id: '/login-external',
  path: '/login-external',
  getParentRoute: () => rootRoute,
} as any)

const LoginCallbackRoute = LoginCallbackImport.update({
  id: '/login-callback',
  path: '/login-callback',
  getParentRoute: () => rootRoute,
} as any)

const LoginRoute = LoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRoute,
} as any)

const LayoutRoute = LayoutImport.update({
  id: '/_layout',
  getParentRoute: () => rootRoute,
} as any)

const ExternalRoute = ExternalImport.update({
  id: '/_external',
  getParentRoute: () => rootRoute,
} as any)

const LayoutIndexRoute = LayoutIndexImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutUsersRoute = LayoutUsersImport.update({
  id: '/users',
  path: '/users',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutScanRoute = LayoutScanImport.update({
  id: '/scan',
  path: '/scan',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutRulesRoute = LayoutRulesImport.update({
  id: '/rules',
  path: '/rules',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutQrcodeRoute = LayoutQrcodeImport.update({
  id: '/qrcode',
  path: '/qrcode',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutEnveloppesRoute = LayoutEnveloppesImport.update({
  id: '/enveloppes',
  path: '/enveloppes',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutControlRoute = LayoutControlImport.update({
  id: '/control',
  path: '/control',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutChangelogRoute = LayoutChangelogImport.update({
  id: '/changelog',
  path: '/changelog',
  getParentRoute: () => LayoutRoute,
} as any)

const LayoutCasiersRoute = LayoutCasiersImport.update({
  id: '/casiers',
  path: '/casiers',
  getParentRoute: () => LayoutRoute,
} as any)

const ExternalMobileRoute = ExternalMobileImport.update({
  id: '/mobile',
  path: '/mobile',
  getParentRoute: () => ExternalRoute,
} as any)

const LayoutLotsExpediteursIndexRoute = LayoutLotsExpediteursIndexImport.update(
  {
    id: '/lots-expediteurs/',
    path: '/lots-expediteurs/',
    getParentRoute: () => LayoutRoute,
  } as any,
)

const LayoutLotsExpediteursLotExpediteurIdRoute =
  LayoutLotsExpediteursLotExpediteurIdImport.update({
    id: '/lots-expediteurs/$lotExpediteurId',
    path: '/lots-expediteurs/$lotExpediteurId',
    getParentRoute: () => LayoutRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_external': {
      id: '/_external'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof ExternalImport
      parentRoute: typeof rootRoute
    }
    '/_layout': {
      id: '/_layout'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof LayoutImport
      parentRoute: typeof rootRoute
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginImport
      parentRoute: typeof rootRoute
    }
    '/login-callback': {
      id: '/login-callback'
      path: '/login-callback'
      fullPath: '/login-callback'
      preLoaderRoute: typeof LoginCallbackImport
      parentRoute: typeof rootRoute
    }
    '/login-external': {
      id: '/login-external'
      path: '/login-external'
      fullPath: '/login-external'
      preLoaderRoute: typeof LoginExternalImport
      parentRoute: typeof rootRoute
    }
    '/_external/mobile': {
      id: '/_external/mobile'
      path: '/mobile'
      fullPath: '/mobile'
      preLoaderRoute: typeof ExternalMobileImport
      parentRoute: typeof ExternalImport
    }
    '/_layout/casiers': {
      id: '/_layout/casiers'
      path: '/casiers'
      fullPath: '/casiers'
      preLoaderRoute: typeof LayoutCasiersImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/changelog': {
      id: '/_layout/changelog'
      path: '/changelog'
      fullPath: '/changelog'
      preLoaderRoute: typeof LayoutChangelogImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/control': {
      id: '/_layout/control'
      path: '/control'
      fullPath: '/control'
      preLoaderRoute: typeof LayoutControlImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/enveloppes': {
      id: '/_layout/enveloppes'
      path: '/enveloppes'
      fullPath: '/enveloppes'
      preLoaderRoute: typeof LayoutEnveloppesImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/qrcode': {
      id: '/_layout/qrcode'
      path: '/qrcode'
      fullPath: '/qrcode'
      preLoaderRoute: typeof LayoutQrcodeImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/rules': {
      id: '/_layout/rules'
      path: '/rules'
      fullPath: '/rules'
      preLoaderRoute: typeof LayoutRulesImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/scan': {
      id: '/_layout/scan'
      path: '/scan'
      fullPath: '/scan'
      preLoaderRoute: typeof LayoutScanImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/users': {
      id: '/_layout/users'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof LayoutUsersImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/': {
      id: '/_layout/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof LayoutIndexImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/lots-expediteurs/$lotExpediteurId': {
      id: '/_layout/lots-expediteurs/$lotExpediteurId'
      path: '/lots-expediteurs/$lotExpediteurId'
      fullPath: '/lots-expediteurs/$lotExpediteurId'
      preLoaderRoute: typeof LayoutLotsExpediteursLotExpediteurIdImport
      parentRoute: typeof LayoutImport
    }
    '/_layout/lots-expediteurs/': {
      id: '/_layout/lots-expediteurs/'
      path: '/lots-expediteurs'
      fullPath: '/lots-expediteurs'
      preLoaderRoute: typeof LayoutLotsExpediteursIndexImport
      parentRoute: typeof LayoutImport
    }
  }
}

// Create and export the route tree

interface ExternalRouteChildren {
  ExternalMobileRoute: typeof ExternalMobileRoute
}

const ExternalRouteChildren: ExternalRouteChildren = {
  ExternalMobileRoute: ExternalMobileRoute,
}

const ExternalRouteWithChildren = ExternalRoute._addFileChildren(
  ExternalRouteChildren,
)

interface LayoutRouteChildren {
  LayoutCasiersRoute: typeof LayoutCasiersRoute
  LayoutChangelogRoute: typeof LayoutChangelogRoute
  LayoutControlRoute: typeof LayoutControlRoute
  LayoutEnveloppesRoute: typeof LayoutEnveloppesRoute
  LayoutQrcodeRoute: typeof LayoutQrcodeRoute
  LayoutRulesRoute: typeof LayoutRulesRoute
  LayoutScanRoute: typeof LayoutScanRoute
  LayoutUsersRoute: typeof LayoutUsersRoute
  LayoutIndexRoute: typeof LayoutIndexRoute
  LayoutLotsExpediteursLotExpediteurIdRoute: typeof LayoutLotsExpediteursLotExpediteurIdRoute
  LayoutLotsExpediteursIndexRoute: typeof LayoutLotsExpediteursIndexRoute
}

const LayoutRouteChildren: LayoutRouteChildren = {
  LayoutCasiersRoute: LayoutCasiersRoute,
  LayoutChangelogRoute: LayoutChangelogRoute,
  LayoutControlRoute: LayoutControlRoute,
  LayoutEnveloppesRoute: LayoutEnveloppesRoute,
  LayoutQrcodeRoute: LayoutQrcodeRoute,
  LayoutRulesRoute: LayoutRulesRoute,
  LayoutScanRoute: LayoutScanRoute,
  LayoutUsersRoute: LayoutUsersRoute,
  LayoutIndexRoute: LayoutIndexRoute,
  LayoutLotsExpediteursLotExpediteurIdRoute:
    LayoutLotsExpediteursLotExpediteurIdRoute,
  LayoutLotsExpediteursIndexRoute: LayoutLotsExpediteursIndexRoute,
}

const LayoutRouteWithChildren =
  LayoutRoute._addFileChildren(LayoutRouteChildren)

export interface FileRoutesByFullPath {
  '': typeof LayoutRouteWithChildren
  '/login': typeof LoginRoute
  '/login-callback': typeof LoginCallbackRoute
  '/login-external': typeof LoginExternalRoute
  '/mobile': typeof ExternalMobileRoute
  '/casiers': typeof LayoutCasiersRoute
  '/changelog': typeof LayoutChangelogRoute
  '/control': typeof LayoutControlRoute
  '/enveloppes': typeof LayoutEnveloppesRoute
  '/qrcode': typeof LayoutQrcodeRoute
  '/rules': typeof LayoutRulesRoute
  '/scan': typeof LayoutScanRoute
  '/users': typeof LayoutUsersRoute
  '/': typeof LayoutIndexRoute
  '/lots-expediteurs/$lotExpediteurId': typeof LayoutLotsExpediteursLotExpediteurIdRoute
  '/lots-expediteurs': typeof LayoutLotsExpediteursIndexRoute
}

export interface FileRoutesByTo {
  '': typeof ExternalRouteWithChildren
  '/login': typeof LoginRoute
  '/login-callback': typeof LoginCallbackRoute
  '/login-external': typeof LoginExternalRoute
  '/mobile': typeof ExternalMobileRoute
  '/casiers': typeof LayoutCasiersRoute
  '/changelog': typeof LayoutChangelogRoute
  '/control': typeof LayoutControlRoute
  '/enveloppes': typeof LayoutEnveloppesRoute
  '/qrcode': typeof LayoutQrcodeRoute
  '/rules': typeof LayoutRulesRoute
  '/scan': typeof LayoutScanRoute
  '/users': typeof LayoutUsersRoute
  '/': typeof LayoutIndexRoute
  '/lots-expediteurs/$lotExpediteurId': typeof LayoutLotsExpediteursLotExpediteurIdRoute
  '/lots-expediteurs': typeof LayoutLotsExpediteursIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_external': typeof ExternalRouteWithChildren
  '/_layout': typeof LayoutRouteWithChildren
  '/login': typeof LoginRoute
  '/login-callback': typeof LoginCallbackRoute
  '/login-external': typeof LoginExternalRoute
  '/_external/mobile': typeof ExternalMobileRoute
  '/_layout/casiers': typeof LayoutCasiersRoute
  '/_layout/changelog': typeof LayoutChangelogRoute
  '/_layout/control': typeof LayoutControlRoute
  '/_layout/enveloppes': typeof LayoutEnveloppesRoute
  '/_layout/qrcode': typeof LayoutQrcodeRoute
  '/_layout/rules': typeof LayoutRulesRoute
  '/_layout/scan': typeof LayoutScanRoute
  '/_layout/users': typeof LayoutUsersRoute
  '/_layout/': typeof LayoutIndexRoute
  '/_layout/lots-expediteurs/$lotExpediteurId': typeof LayoutLotsExpediteursLotExpediteurIdRoute
  '/_layout/lots-expediteurs/': typeof LayoutLotsExpediteursIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/login'
    | '/login-callback'
    | '/login-external'
    | '/mobile'
    | '/casiers'
    | '/changelog'
    | '/control'
    | '/enveloppes'
    | '/qrcode'
    | '/rules'
    | '/scan'
    | '/users'
    | '/'
    | '/lots-expediteurs/$lotExpediteurId'
    | '/lots-expediteurs'
  fileRoutesByTo: FileRoutesByTo
  to:
    | ''
    | '/login'
    | '/login-callback'
    | '/login-external'
    | '/mobile'
    | '/casiers'
    | '/changelog'
    | '/control'
    | '/enveloppes'
    | '/qrcode'
    | '/rules'
    | '/scan'
    | '/users'
    | '/'
    | '/lots-expediteurs/$lotExpediteurId'
    | '/lots-expediteurs'
  id:
    | '__root__'
    | '/_external'
    | '/_layout'
    | '/login'
    | '/login-callback'
    | '/login-external'
    | '/_external/mobile'
    | '/_layout/casiers'
    | '/_layout/changelog'
    | '/_layout/control'
    | '/_layout/enveloppes'
    | '/_layout/qrcode'
    | '/_layout/rules'
    | '/_layout/scan'
    | '/_layout/users'
    | '/_layout/'
    | '/_layout/lots-expediteurs/$lotExpediteurId'
    | '/_layout/lots-expediteurs/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  ExternalRoute: typeof ExternalRouteWithChildren
  LayoutRoute: typeof LayoutRouteWithChildren
  LoginRoute: typeof LoginRoute
  LoginCallbackRoute: typeof LoginCallbackRoute
  LoginExternalRoute: typeof LoginExternalRoute
}

const rootRouteChildren: RootRouteChildren = {
  ExternalRoute: ExternalRouteWithChildren,
  LayoutRoute: LayoutRouteWithChildren,
  LoginRoute: LoginRoute,
  LoginCallbackRoute: LoginCallbackRoute,
  LoginExternalRoute: LoginExternalRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_external",
        "/_layout",
        "/login",
        "/login-callback",
        "/login-external"
      ]
    },
    "/_external": {
      "filePath": "_external.tsx",
      "children": [
        "/_external/mobile"
      ]
    },
    "/_layout": {
      "filePath": "_layout.tsx",
      "children": [
        "/_layout/casiers",
        "/_layout/changelog",
        "/_layout/control",
        "/_layout/enveloppes",
        "/_layout/qrcode",
        "/_layout/rules",
        "/_layout/scan",
        "/_layout/users",
        "/_layout/",
        "/_layout/lots-expediteurs/$lotExpediteurId",
        "/_layout/lots-expediteurs/"
      ]
    },
    "/login": {
      "filePath": "login.tsx"
    },
    "/login-callback": {
      "filePath": "login-callback.tsx"
    },
    "/login-external": {
      "filePath": "login-external.tsx"
    },
    "/_external/mobile": {
      "filePath": "_external/mobile.tsx",
      "parent": "/_external"
    },
    "/_layout/casiers": {
      "filePath": "_layout/casiers.tsx",
      "parent": "/_layout"
    },
    "/_layout/changelog": {
      "filePath": "_layout/changelog.tsx",
      "parent": "/_layout"
    },
    "/_layout/control": {
      "filePath": "_layout/control.tsx",
      "parent": "/_layout"
    },
    "/_layout/enveloppes": {
      "filePath": "_layout/enveloppes.tsx",
      "parent": "/_layout"
    },
    "/_layout/qrcode": {
      "filePath": "_layout/qrcode.tsx",
      "parent": "/_layout"
    },
    "/_layout/rules": {
      "filePath": "_layout/rules.tsx",
      "parent": "/_layout"
    },
    "/_layout/scan": {
      "filePath": "_layout/scan.tsx",
      "parent": "/_layout"
    },
    "/_layout/users": {
      "filePath": "_layout/users.tsx",
      "parent": "/_layout"
    },
    "/_layout/": {
      "filePath": "_layout/index.tsx",
      "parent": "/_layout"
    },
    "/_layout/lots-expediteurs/$lotExpediteurId": {
      "filePath": "_layout/lots-expediteurs/$lotExpediteurId.tsx",
      "parent": "/_layout"
    },
    "/_layout/lots-expediteurs/": {
      "filePath": "_layout/lots-expediteurs/index.tsx",
      "parent": "/_layout"
    }
  }
}
ROUTE_MANIFEST_END */
