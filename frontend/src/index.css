@import 'tailwindcss';
@plugin "@headlessui/tailwindcss";

@theme {
  /* Fonts & Breakpoints */
  --font-display: '<PERSON>shi', 'sans-serif';
  --breakpoint-3xl: 1920px;

  /* Primary (La Poste Blue) */
  --color-primary-alpha: rgba(0, 61, 165, 0.1);
  --color-primary-lightest: #e6eaf7; /* lightest tint */
  --color-primary-lighter: #ccd6ef; /* lighter tint */
  --color-primary-light: #6699d2; /* light tint */
  --color-primary: #003da5; /* La Poste official blue */
  --color-primary-dark: #003184; /* darker shade */
  --color-primary-darker: #002563; /* darkest shade */

  /* Secondary (Yellow) */
  --color-secondary-alpha: rgba(255, 214, 0, 0.1);
  --color-secondary-lightest: #fff9df;
  --color-secondary-lighter: #ffeea3;
  --color-secondary-light: #ffe34e;
  --color-secondary: #ffd600;
  --color-secondary-dark: #ccab00;
  --color-secondary-darker: #998000;

  /* Tertiary (Example Accent) */
  --color-tertiary-alpha: rgba(161, 168, 206, 0.1);
  --color-tertiary: #a1a8ce;
  --color-tertiary-dark: #5b67a9;

  /* Neutrals & Utilities */
  --color-black: #000;
  --color-white: #fff;
  --color-gray-50: #f6f6fa;
  --color-gray-100: #f0f0f6;
  --color-gray-200: #e3e4eb;
  --color-gray-250: #dbdce5;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #5f6369;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #2a2e34;
  --color-gray-950: #202020;

  /* Alerts, Success, etc. */
  --color-warning-alpha: rgba(211, 131, 22, 0.1);
  --color-warning-light: #fcc8a1;
  --color-warning: #d38316;
  --color-warning-dark: #b47013;

  --color-danger-alpha: rgba(234, 97, 97, 0.1);
  --color-danger-lighter: #fef7f7;
  --color-danger-light: #ffbaba;
  --color-danger: #ea6161;
  --color-danger-dark: #c74f4f;

  --color-success-alpha: rgba(82, 209, 131, 0.1);
  --color-success-lighter: #f5fdf9;
  --color-success-light: #c1f1d9;
  --color-success: #34d183;
  --color-success-dark: #2db471;

  /* Additional Examples: Purple, Pink, etc. */
  --color-purple-alpha: rgba(149, 24, 240, 0.1);
  --color-purple-light: #d0d0ff;
  --color-purple: #9518f0;
  --color-purple-dark: #7f14cc;

  --color-pink-alpha: rgba(222, 88, 191, 0.1);
  --color-pink-light: #ffc1f1;
  --color-pink: #de58bf;
  --color-pink-dark: #bd4ba3;

  /* Easing Variables (Optional) */
  --ease-fluid: cubic-bezier(0.3, 0, 0, 1);
  --ease-snappy: cubic-bezier(0.2, 0, 0, 1);
}

.animate-fade-in-delayed {
  opacity: 0;
  animation: fade-in 0.5s ease-in-out 1s forwards;
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.animate-fade-in-top {
  opacity: 0;
  animation: fade-in-top 0.75s ease-in-out forwards;
}

@keyframes fade-in-top {
  0% {
    opacity: 0;
    transform: translateY(-25px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.unflipped {
  transform: none !important;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
textarea:-webkit-autofill,
textarea:-webkit-autofill:hover,
textarea:-webkit-autofill:focus,
select:-webkit-autofill,
select:-webkit-autofill:hover,
select:-webkit-autofill:focus {
  border: 1px solid oklch(0.623 0.214 259.815);
  -webkit-text-fill-color: oklch(0.488 0.243 264.376);
  -webkit-box-shadow: 0 0 0px 1000px oklch(0.97 0.014 254.604) inset;
  transition: background-color 5000s ease-in-out 0s;
}
