import React from 'react'

import { Destination } from '@/models/destination'
import { Enveloppe } from '@/models/enveloppe'
import { Nature } from '@/models/nature'
import { Sender } from '@/models/sender'
import { ValidationResponse } from '@/models/affranchissement'

type ControlContextType = {
  isFinished: boolean
  setIsFinished: (value: boolean) => void
  currentEnveloppe: Enveloppe | undefined
  selectedSender: Sender | undefined
  setSelectedSender: (value: Sender | undefined) => void
  selectedDestination: Destination | undefined
  setSelectedDestination: (value: Destination | undefined) => void
  weight: number | undefined
  setWeight: (value: number | undefined) => void
  isOverWeight: boolean
  setIsOverWeight: (value: boolean) => void
  isOverSized: boolean
  setIsOverSized: (value: boolean) => void
  handleIsOverSizedChange: () => void
  handleTerminerEnveloppe: () => Promise<void>
  resetAll: () => void

  naturesData: Nature[] | undefined
  scannedCodes: Set<string>
  scanResults: Map<string, ValidationResponse>
  setScannedCodes: React.Dispatch<React.SetStateAction<Set<string>>>
  setScanResults: (results: Map<string, ValidationResponse>) => void
  enveloppeKey: number | 'edition'
  setEnveloppeKey: (key: number | 'edition') => void

  handleNouvelleEnveloppe: () => void

  destinationsData: string[] | undefined
  selectedDestinationEnveloppe: string | undefined
  setSelectedDestinationEnveloppe: (value: string | undefined) => void
}

export const ControlContext = React.createContext<ControlContextType>({
  isFinished: false,
  setIsFinished: () => {},
  currentEnveloppe: undefined,
  selectedSender: undefined,
  setSelectedSender: () => {},
  selectedDestination: undefined,
  setSelectedDestination: () => {},
  weight: undefined,
  setWeight: () => {},
  isOverWeight: false,
  setIsOverWeight: () => {},
  isOverSized: false,
  setIsOverSized: () => {},
  handleIsOverSizedChange: () => {},
  handleTerminerEnveloppe: async () => {},
  resetAll: () => {},
  naturesData: undefined,
  scannedCodes: new Set(),
  scanResults: new Map(),
  setScannedCodes: () => {},
  setScanResults: () => {},
  enveloppeKey: 'edition',
  setEnveloppeKey: () => {},
  handleNouvelleEnveloppe: () => {},
  destinationsData: undefined,
  selectedDestinationEnveloppe: undefined,
  setSelectedDestinationEnveloppe: () => {},
})
