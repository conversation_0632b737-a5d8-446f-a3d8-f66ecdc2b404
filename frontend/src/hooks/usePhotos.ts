// hooks/usePhotos.ts
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import toast from 'react-hot-toast'
import uniqid from 'uniqid'
import { EnveloppeService } from '@/services/enveloppeService'
import { ApiError } from '@/core/ApiError'
import { Enveloppe, PhotoEnveloppe } from '@/models/enveloppe'
import { ControlContext } from '@/contexts/Control'
import { useContext } from 'react'

type UsePhotosProps = {
  currentEnveloppe: any
  refreshInterval?: number
}

export function usePhotos({ currentEnveloppe, refreshInterval = 3000 }: UsePhotosProps) {
  const queryClient = useQueryClient()
  const { enveloppeKey } = useContext(ControlContext)

  // Requête pour surveiller uniquement les photos de l'enveloppe
  useQuery({
    queryKey: ['enveloppePhotos', enveloppeKey],
    queryFn: async () => {
      if (!currentEnveloppe?.id) return null
      const photos = await EnveloppeService.getEnveloppePhotos(currentEnveloppe.id)
      // Mettre à jour uniquement les photos dans les données de l'enveloppe
      queryClient.setQueryData(['enveloppe', enveloppeKey], (oldData: any) => {
        if (!oldData) return oldData
        return { ...oldData, photos: photos }
      })
      return photos
    },
    enabled: !!currentEnveloppe?.id,
    refetchInterval: refreshInterval,
    networkMode: 'always',
    refetchOnWindowFocus: true,
  })

  const addPhotoMutation = useMutation({
    mutationFn: async ({
      enveloppeId,
      formData,
    }: {
      enveloppeId: number
      formData: FormData
    }) => {
      return await EnveloppeService.postPhoto(enveloppeId, formData)
    },
    onSuccess: (data: Enveloppe) => {
      toast.success('Photo ajoutée')
      queryClient.setQueryData(['enveloppe', enveloppeKey], data)
    },
    onError: (err: ApiError) => {
      const errDetail = (err.body as any)?.detail
      toast.error(`Erreur lors de l'ajout de la photo. ${errDetail}`)
    },
  })

  const deletePhotoMutation = useMutation({
    mutationFn: async ({
      enveloppeId,
      photoId,
    }: {
      enveloppeId: number
      photoId: number
    }) => {
      return await EnveloppeService.deletePhoto(enveloppeId, photoId)
    },
    onSuccess: (data: Enveloppe) => {
      toast.success('Photo supprimée')
      queryClient.setQueryData(['enveloppe', enveloppeKey], data)
    },
    onError: (err: ApiError) => {
      const errDetail = (err.body as any)?.detail
      toast.error(`Erreur lors de la suppression de la photo. ${errDetail}`)
    },
  })

  const handleAddPhoto = async (image: string) => {
    if (!currentEnveloppe) return
    try {
      const res = await fetch(image)
      const blob = await res.blob()
      const formData = new FormData()
      formData.append('file', blob, `photo-${uniqid()}.jpg`)
      await addPhotoMutation.mutate({
        enveloppeId: currentEnveloppe.id,
        formData,
      })
    } catch (error) {
      console.error('Error adding photo', error)
    }
  }

  const handleDeletePhoto = async (publicUrl: string) => {
    if (!currentEnveloppe) return
    const photo = currentEnveloppe.photos?.find(
      (p: PhotoEnveloppe) => p.public_url === publicUrl
    )
    if (photo) {
      deletePhotoMutation.mutate({
        enveloppeId: currentEnveloppe.id,
        photoId: photo.id,
      })
    }
  }

  return {
    handleAddPhoto,
    handleDeletePhoto,
    addPhotoMutation,
    deletePhotoMutation,
  }
}
