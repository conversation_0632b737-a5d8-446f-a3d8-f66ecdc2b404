import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useNavigate } from '@tanstack/react-router'
import { useEffect, useState } from 'react'

import { AxiosError } from 'axios'

import { UsersService } from '@/services/usersService'
import { UserPublic } from '@/models/usersModel'
import { OpenAPI } from '@/core/OpenAPI'
import { tokenService } from '@/services/tokenService'

// Vérification si l'utilisateur est connecté via token externe
const isExternalLoggedIn = () => {
  return localStorage.getItem('external_access_token') !== null
}

const useExternalAuth = () => {
  const [error, setError] = useState<string | null>(null)
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  
  // Configuration du token pour les requêtes API
  useEffect(() => {
    const token = localStorage.getItem('external_access_token')
    if (token) {
      OpenAPI.TOKEN = token
    }
  }, [])
  
  const {
    data: user,
    isLoading,
    error: queryError,
  } = useQuery<UserPublic | null, AxiosError>({
    queryKey: ['currentExternalUser'],
    queryFn: UsersService.readUserMe,
    enabled: isExternalLoggedIn(),
    staleTime: 1000 * 60 * 5, // 5 minutes
  })

  useEffect(() => {
    if (queryError) {
      if (queryError?.status === 403 || queryError?.status === 404) {
        logout()
      } else {
        setError(queryError.message)
      }
    }
  }, [queryError])

  // Fonction d'authentification avec token externe
  const externalLogin = async (token: string) => {
    tokenService.remove()
    localStorage.setItem('external_access_token', token)
    OpenAPI.TOKEN = token
  }

  const logout = () => {
    localStorage.removeItem('external_access_token')
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    OpenAPI.TOKEN = undefined
    queryClient.removeQueries({ queryKey: ['currentExternalUser'] })
    navigate({ to: '/login' })
  }

  return {
    logout,
    user,
    isLoading,
    error,
    externalLogin,
    resetError: () => setError(null),
    isExternalLoggedIn,
  }
}

export { isExternalLoggedIn }
export default useExternalAuth 