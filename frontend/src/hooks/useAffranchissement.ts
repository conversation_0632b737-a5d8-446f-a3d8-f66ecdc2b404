// hooks/useAffranchissement.ts
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import toast from 'react-hot-toast'
import { EnveloppeService } from '@/services/enveloppeService'
import { affranchissementToJSON } from '@/utils/affranchissementHelpers'
import { ApiError } from '@/core/ApiError'
import {
  EnveloppeAffranchissement,
  Body_post_affranchissement,
  AffranchissementCategory,
  ValidationResponse,
} from '@/models/affranchissement'
import { EnveloppeFullData } from '@/models/othersModels'
import { isLoggedIn } from './useAuth'
import { EnumerationService } from '@/services/enumerationService'
import { AxiosError } from 'axios'
import { useContext, useEffect, useRef } from 'react'
import { ControlContext } from '@/contexts/Control'
import { AffranchissementService } from '@/services/affranchissementService'

export function useAffranchissement() {
  const {
    currentEnveloppe,
    setScanResults,
    scannedCodes,
    scanResults,
    enveloppeKey,
  } = useContext(ControlContext)
  const queryClient = useQueryClient()

  const scannedCodesRef = useRef(scannedCodes)
  const scanResultsRef = useRef(scanResults)

  useEffect(() => {
    scannedCodesRef.current = scannedCodes
  }, [scannedCodes])

  useEffect(() => {
    scanResultsRef.current = scanResults
  }, [scanResults])

  // Query for available affranchissement models
  const { data: modelesAffranchissements } = useQuery<
    AffranchissementCategory[],
    AxiosError
  >({
    queryKey: ['affranchissements'],
    queryFn: EnumerationService.getAffranchissements,
    enabled: isLoggedIn(),
    staleTime: 1000 * 60 * 5,
  })

  // Mutation to add a new affranchissement to the envelope
  const addAffranchissementMutation = useMutation({
    mutationFn: async ({ body }: { body: Body_post_affranchissement }) => {
      return await EnveloppeService.postAffranchissement(
        currentEnveloppe!.id,
        body
      )
    },
    onSuccess: (data: EnveloppeFullData) => {
      queryClient.setQueryData(['enveloppe', enveloppeKey], data.enveloppe)
    },
    onError: (err: ApiError) => {
      const errDetail = (err.body as any)?.detail
      toast.error(
        `Une erreur est survenue lors de l'ajout de l'affranchissement. ${errDetail}`
      )
    },
  })

  // Mutation to update an existing affranchissement
  const updateAffranchissementMutation = useMutation({
    mutationFn: async ({
      enveloppeId,
      affranchissementId,
      payload,
    }: {
      enveloppeId: number
      affranchissementId: number
      payload: Partial<EnveloppeAffranchissement>
    }) => {
      return await EnveloppeService.updateAffranchissement(
        enveloppeId,
        affranchissementId,
        payload
      )
    },
    onSuccess: (data: EnveloppeFullData) => {
      queryClient.setQueryData(['enveloppe', enveloppeKey], data.enveloppe)
      toast.success('Affranchissement mis à jour')
    },
    onError: (err: ApiError) => {
      const errDetail = (err.body as any)?.detail
      toast.error(`Erreur lors de la mise à jour. ${errDetail}`)
    },
  })

  // Mutation to delete an existing affranchissement
  const deleteAffranchissementMutation = useMutation({
    mutationFn: async ({
      enveloppeId,
      affranchissementId,
    }: {
      enveloppeId: number
      affranchissementId: number
    }) => {
      return await EnveloppeService.deleteAffranchissement(
        enveloppeId,
        affranchissementId
      )
    },
    onSuccess: (data: EnveloppeFullData) => {
      queryClient.setQueryData(['enveloppe', enveloppeKey], data.enveloppe)
      toast.success('Affranchissement supprimé')
    },
    onError: (err: ApiError) => {
      const errDetail = (err.body as any)?.detail
      toast.error(`Erreur lors de la suppression. ${errDetail}`)
    },
  })

  // Helper: Returns an existing affranchissement from currentEnveloppe if found
  const findExistingAffranchissement = (
    affranchissement: EnveloppeAffranchissement
  ): EnveloppeAffranchissement | null => {
    if (!currentEnveloppe?.affranchissements) return null
    return (
      currentEnveloppe.affranchissements.find(
        (af: EnveloppeAffranchissement) =>
          af.categorie === affranchissement.categorie &&
          af.type === affranchissement.type &&
          af.prix_unite_devise === affranchissement.prix_unite_devise &&
          af.statut === affranchissement.statut &&
          af.devise === affranchissement.devise &&
          af.code === affranchissement.code
      ) || null
    )
  }

  // Helper: Checks if a given code already exists in the envelope's affranchissements
  const findExistingCodeInAffranchissements = (code: string) => {
    if (!currentEnveloppe?.affranchissements) return null
    return currentEnveloppe.affranchissements.find(
      (af: EnveloppeAffranchissement) => af.code === code
    )
  }

  const handleUpdateAffranchissement = async (
    data: EnveloppeAffranchissement
  ) => {
    if (!currentEnveloppe || !data) return
    await updateAffranchissementMutation.mutate({
      enveloppeId: currentEnveloppe.id,
      affranchissementId: data.id as number,
      payload: affranchissementToJSON(data),
    })
  }

  const handleAddAffranchissement = async (
    affranchissement: EnveloppeAffranchissement
  ) => {
    if (!currentEnveloppe) return

    const existing = findExistingAffranchissement(affranchissement)

    if (existing) {
      updateAffranchissementMutation.mutate({
        enveloppeId: currentEnveloppe.id,
        affranchissementId: existing.id!,
        payload: { quantite: existing.quantite + affranchissement.quantite },
      })
      return
    }
    await addAffranchissementMutation.mutateAsync({
      body: affranchissementToJSON(affranchissement),
    })

    toast.success('Affranchissement ajouté avec succès')
  }

  // Mutation to verify a scanned affranchissement code
  const verifyAffranchissementMutation = useMutation({
    mutationFn: async (code: string) =>
      await AffranchissementService.verifierAffranchissement({ code }),
    onSuccess: (response: ValidationResponse, code: string) => {
      // Store the response in scanResults
      setScanResults(new Map([[code, response]]))
      return response
    },
    onError: () => {
      toast.error("Erreur lors de la vérification de l'affranchissement")
    },
  })

  // New getValidationInfo function: returns a string to be displayed in the bubble tooltip.
  const getValidationInfo = async (
    code: string
  ): Promise<ValidationResponse | null> => {
    const result = scanResultsRef.current.get(code)

    if (!result) {
      const result = await verifyAffranchissementMutation.mutateAsync(code)
      return result as ValidationResponse
    }
    return result
  }

  const handleChangeLegitStatus = async (
    affranchissement: EnveloppeAffranchissement
  ) => {
    if (!currentEnveloppe) return
    updateAffranchissementMutation.mutate({
      enveloppeId: currentEnveloppe.id,
      affranchissementId: affranchissement.id as number,
      payload: {
        statut: affranchissement.statut === 'VALIDE' ? 'INVALIDE' : 'VALIDE',
      },
    })
  }

  const handleChangeQuantity = async (
    affranchissement: EnveloppeAffranchissement,
    quantity: number
  ) => {
    if (!currentEnveloppe) return
    if (quantity > 0) {
      updateAffranchissementMutation.mutate({
        enveloppeId: currentEnveloppe.id,
        affranchissementId: affranchissement.id as number,
        payload: { quantite: quantity },
      })
    } else {
      deleteAffranchissementMutation.mutate({
        enveloppeId: currentEnveloppe.id,
        affranchissementId: affranchissement.id as number,
      })
    }
  }

  return {
    handleAddAffranchissement,
    addAffranchissementMutation,
    updateAffranchissementMutation,
    deleteAffranchissementMutation,

    getValidationInfo,
    handleChangeLegitStatus,
    handleChangeQuantity,
    modelesAffranchissements,
    handleUpdateAffranchissement,
    findExistingCodeInAffranchissements,
  }
}
