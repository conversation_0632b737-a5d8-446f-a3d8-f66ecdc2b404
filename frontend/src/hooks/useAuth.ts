import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import { AxiosError } from "axios";
import { UsersService } from '@/services/usersService'
import { UserPublic } from '@/models/usersModel'
import { tokenService } from '@/services/tokenService'

// Hook principal
const useAuth = () => {
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  // Requête utilisateur
  const {
    data: user,
    isLoading,
    error: queryError,
  } = useQuery<UserPublic | null, AxiosError>({
    queryKey: ["currentUser"],
    queryFn: UsersService.readUserMe
  });

  // Gestion des erreurs de requête
  useEffect(() => {
    if (queryError) {
      logout()
    }
  }, [queryError]);
    

  // Déconnexion
  const logout = (): void => {
    tokenService.remove();
        
    navigate({ to: "/login" });
  };

  return {
    logout,
    user,
    isLoading,
    error,
    resetError: () => setError(null),
    isTokenExpired: tokenService.isExpired,
  };
};

// Export de la fonction isLoggedIn pour compatibilité
export const isLoggedIn = tokenService.isStored;
export default useAuth;
