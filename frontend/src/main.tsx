import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { createRouter, RouterProvider } from '@tanstack/react-router'
import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'

import { OpenAPI } from '@/core/OpenAPI'
import './index.css'
import { routeTree } from './routeTree.gen'
import { setupAuthInterceptors } from './core/ApiAuthentificationInterceptors'

const container = document.getElementById('root') as HTMLDivElement
const root = createRoot(container)

OpenAPI.BASE = import.meta.env.VITE_API_URL

OpenAPI.TOKEN = async () => {
  return localStorage.getItem('access_token') || localStorage.getItem('external_access_token') ||''
}

setupAuthInterceptors();

const queryClient = new QueryClient()

const router = createRouter({ routeTree })
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

root.render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <RouterProvider router={router} />
    </QueryClientProvider>
  </StrictMode>
)
