/* eslint-disable react-hooks/exhaustive-deps */
import { useEffect, useRef } from 'react'
import {
  // Core
  DataCaptureContext,
  Camera,
  DataCaptureView,
  RectangularViewfinder,
  FrameSourceState,
  CameraSwitchControl,
  configure,
  Anchor,
  PointWithUnit,
  NumberWithUnit,
  MeasureUnit,
} from '@scandit/web-datacapture-core'

import {
  // Single barcode mode
  BarcodeCapture,
  BarcodeCaptureSettings,
  BarcodeCaptureOverlay,

  // Batch scanning mode (MatrixScan)
  BarcodeBatch,
  BarcodeBatchSettings,

  // Common:
  Symbology,
  barcodeCaptureLoader,
  BarcodeBatchAdvancedOverlay,
  TrackedBarcodeView,
} from '@scandit/web-datacapture-barcode'

interface ScannerProps {
  /** Callback that fires whenever a barcode is detected. */
  onDetected: (data: string) => void

  /** Whether to use single-barcode capture or batch (MatrixScan) mode. */
  batchMode?: boolean
}

/**
 * InlineScanner can switch between 'single capture mode' and 'batch (MatrixScan) mode'
 * by checking the `batchMode` prop.
 */
export default function InlineScanner({
  onDetected,
  batchMode = false,
}: ScannerProps) {
  const scannerContainerRef = useRef<HTMLDivElement>(null)
  const contextRef = useRef<DataCaptureContext | null>(null)
  const captureModeRef = useRef<BarcodeCapture | BarcodeBatch | null>(null)

  /** Common cleanup: turn off the camera & dispose the context. */
  const stopScanner = () => {
    const context = contextRef.current
    if (context) {
      const camera = context.frameSource as Camera
      camera?.switchToDesiredState(FrameSourceState.Off)
      context.dispose()
      contextRef.current = null
      captureModeRef.current = null
    }
  }

  const onDetectedRef = useRef(onDetected)

  useEffect(() => {
    let isCancelled = false

    const initializeScanner = async () => {
      try {
        if (!scannerContainerRef.current) return

        // 1) Configure the library with your license key.
        //    The loader is mandatory for barcode functionality.
        await configure({
          licenseKey: import.meta.env.VITE_PUBLIC_SCANDIT_API_KEY!,
          moduleLoaders: [barcodeCaptureLoader()],
          libraryLocation:
            'https://cdn.jsdelivr.net/npm/@scandit/web-datacapture-barcode@7.1/sdc-lib',
        })
        if (isCancelled) return

        // 2) Create a new DataCaptureContext
        const context = await DataCaptureContext.create()
        if (isCancelled) {
          context.dispose()
          return
        }
        contextRef.current = context

        // 3) Initialize the camera
        const camera = Camera.default
        await context.setFrameSource(camera)
        if (isCancelled) return

        // 4) Pick camera settings based on the mode we’re using
        const cameraSettings = batchMode
          ? BarcodeBatch.recommendedCameraSettings // For MatrixScan
          : BarcodeCapture.recommendedCameraSettings // For single capture

        // Apply the camera settings
        camera.applySettings(cameraSettings)

        // 5) Prepare the DataCaptureView that will show the camera preview
        const view = await DataCaptureView.forContext(context)
        view.connectToElement(scannerContainerRef.current)

        // Optional: Add a switch-camera button in the UI
        view.addControl(new CameraSwitchControl())

        if (batchMode) {
          //
          // --- BATCH (MatrixScan) mode ---
          //
          // A) Create BarcodeBatchSettings
          const batchSettings = new BarcodeBatchSettings()
          batchSettings.enableSymbologies([
            Symbology.Code128,
            Symbology.Code39,
            Symbology.QR,
            Symbology.EAN8,
            Symbology.UPCE,
            Symbology.EAN13UPCA,
            Symbology.DataMatrix,
          ])

          // B) Create the BarcodeBatch instance
          const barcodeBatch = await BarcodeBatch.forContext(
            context,
            batchSettings
          )
          captureModeRef.current = barcodeBatch

          // C) (Optional) Register a listener for recognized barcodes
          //    Typically we handle "didUpdateSession" in batch scanning
          barcodeBatch.addListener({
            didUpdateSession: (_barcodeBatch, session) => {
              // For newly recognized barcodes in *this* frame:
              const newBarcodes = session.addedTrackedBarcodes ?? []
              if (newBarcodes.length) {
                newBarcodes.forEach((tracked) => {
                  const data = tracked.barcode.data ?? ''
                  if (data) {
                    onDetected(data)
                  }
                })
              }
            },
          })

          // // D) Add a "Batch Basic Overlay" so that we can visualize them
          // //    (Boxes around recognized barcodes, etc.)
          const overlay =
            await BarcodeBatchAdvancedOverlay.withBarcodeBatchForView(
              barcodeBatch,
              view
            )

          overlay.listener = {
            viewForTrackedBarcode: (_overlay, trackedBarcode) => {
              // Create and return the view you want to show for this tracked barcode. You can also return null, to have no view for this barcode.
              const element = document.createElement('span')
              element.innerText = trackedBarcode.barcode.data ?? ''
              element.style.backgroundColor = '#FFFFFFFF'
              return TrackedBarcodeView.withHTMLElement(element, null)
            },
            anchorForTrackedBarcode: () => {
              // As we want the view to be above the barcode, we anchor the view's center to the top-center of the barcode quadrilateral.
              // Use the function 'offsetForTrackedBarcode' below to adjust the position of the view by providing an offset.
              return Anchor.TopCenter
            },
            offsetForTrackedBarcode: () => {
              // This is the offset that will be applied to the view.
              // You can use .fraction to give a measure relative to the view itself, the sdk will take care of transforming this into pixel size.
              // We now center horizontally and move up the view to make sure it's centered and above the barcode quadrilateral by half of the view's height.
              return new PointWithUnit(
                new NumberWithUnit(0, MeasureUnit.Fraction),
                new NumberWithUnit(-1, MeasureUnit.Fraction)
              )
            },
          }
        } else {
          //
          // --- SINGLE-BARCODE mode ---
          //
          // A) Create BarcodeCaptureSettings
          const captureSettings = new BarcodeCaptureSettings()
          captureSettings.enableSymbologies([
            Symbology.Code128,
            Symbology.Code39,
            Symbology.QR,
            Symbology.EAN8,
            Symbology.UPCE,
            Symbology.EAN13UPCA,
            Symbology.DataMatrix,
          ])

          // B) Create the BarcodeCapture instance
          const barcodeCapture = await BarcodeCapture.forContext(
            context,
            captureSettings
          )
          captureModeRef.current = barcodeCapture

          // C) Register a listener for newly recognized barcodes
          barcodeCapture.addListener({
            didScan: (_capture, session) => {
              // In the new API, we have `newlyRecognizedBarcode` (singular).
              const recognizedBarcode = session.newlyRecognizedBarcode
              if (recognizedBarcode) {
                onDetectedRef.current(recognizedBarcode.data ?? '')
              }
            },
          })

          // D) Attach an overlay so we can visualize recognized frames
          const overlay = await BarcodeCaptureOverlay.withBarcodeCaptureForView(
            barcodeCapture,
            view
          )
          // In single-barcode mode, we *can* set the rectangular viewfinder directly on the overlay
          overlay.setViewfinder(new RectangularViewfinder())
        }

        // 6) Finally, turn the camera on:
        await camera.switchToDesiredState(FrameSourceState.On)
      } catch (err: any) {
        console.error(err)
        alert(err.message || 'An error occurred during scanning')
      }
    }

    // Initialize the scanner
    initializeScanner()

    // Cleanup on unmount or when batchMode / onDetected changes
    return () => {
      isCancelled = true
      stopScanner()
    }
  }, [batchMode]) // Re-run if batchMode changes

  return (
    <div
      ref={scannerContainerRef}
      className="relative aspect-video w-full overflow-hidden rounded-lg border border-gray-200 bg-gray-100"
    />
  )
}
