import {
  TbChevronLeft,
  TbChevronRight,
  TbChevronsLeft,
  TbChevronsRight,
} from 'react-icons/tb'

interface PaginationProps {
  currentPage: number
  setCurrentPage: (page: number) => void
  totalPages: number
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  setCurrentPage,
  totalPages,
}) => {
  const maxPagesToShow = 4 // Alter this to show more / fewer pages
  const pagesToShow = Math.min(maxPagesToShow, totalPages)
  const pagesBuffer = Math.floor(pagesToShow / 2)

  const startPage = Math.max(currentPage - pagesBuffer, 1)
  const endPage = Math.min(startPage + pagesToShow - 1, totalPages)

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return
    setCurrentPage(page)
  }

  return (
    <nav className="isolate inline-flex space-x-1" aria-label="Pagination">
      {/* First */}
      <button
        onClick={() => handlePageChange(1)}
        disabled={currentPage === 1}
        className={`px-2 transition hover:bg-gray-100 ${
          currentPage === 1 ? 'cursor-not-allowed opacity-50' : ''
        }`}
      >
        <TbChevronsLeft className="h-5 w-5" aria-hidden="true" />
      </button>
      {/* Prev */}
      <button
        onClick={() => handlePageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className={`px-2 transition hover:bg-gray-100 ${
          currentPage === 1 ? 'cursor-not-allowed opacity-50' : ''
        }`}
      >
        <TbChevronLeft className="h-5 w-5" aria-hidden="true" />
      </button>
      {/* Numbers */}
      {Array.from(
        { length: endPage - startPage + 1 },
        (_, i) => i + startPage
      ).map((page) => (
        <button
          key={page}
          onClick={() => handlePageChange(page)}
          className={`relative inline-flex items-center px-3 py-2 text-sm font-semibold transition hover:bg-gray-100 focus:z-20 ${
            currentPage === page ? 'border-2 border-black' : ''
          }`}
          aria-current={currentPage === page ? 'page' : undefined}
        >
          {page}
        </button>
      ))}
      {/* Next */}
      <button
        onClick={() => handlePageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className={`px-2 transition hover:bg-gray-100 ${
          currentPage === totalPages ? 'cursor-not-allowed opacity-50' : ''
        }`}
      >
        <TbChevronRight className="h-5 w-5" aria-hidden="true" />
      </button>
      {/* Last */}
      <button
        onClick={() => handlePageChange(totalPages)}
        disabled={currentPage === totalPages}
        className={`px-2 transition hover:bg-gray-100 ${
          currentPage === totalPages ? 'cursor-not-allowed opacity-50' : ''
        }`}
      >
        <TbChevronsRight className="h-5 w-5" aria-hidden="true" />
      </button>
    </nav>
  )
}

export default Pagination
