/* -------------------------------------------------------------------------- */
/*                              FILTER DROPDOWN                              */

import { useEffect, useRef } from 'react'
import { FaTimes } from 'react-icons/fa'

/* -------------------------------------------------------------------------- */
interface FilterDropdownProps {
  isOpen: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
  hasActiveFilter?: boolean
}

export const FilterDropdown: React.FC<FilterDropdownProps> = ({
  isOpen,
  onClose,
  title,
  children,
}) => {
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  return (
    <div
      ref={dropdownRef}
      className="absolute top-full left-0 z-50 mt-1 min-w-[200px] rounded-lg border border-gray-200 bg-white shadow-lg"
    >
      <div className="flex items-center justify-between border-b border-gray-100 px-4 py-3">
        <h4 className="font-medium text-gray-900">{title}</h4>
        <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
          <FaTimes className="h-4 w-4" />
        </button>
      </div>
      <div className="p-4">{children}</div>
    </div>
  )
}
