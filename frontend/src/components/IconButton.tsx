import clsx from 'clsx'
import React from 'react'
import { IconType } from 'react-icons'

interface IconButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  icon: IconType
  ariaLabel: string
  isRound?: boolean
  colorScheme?:
    | 'primary'
    | 'secondary'
    | 'tertiary'
    | 'warning'
    | 'danger'
    | 'success'
    | 'purple'
    | 'pink'
  size?: 'sm' | 'md' | 'lg'
  variant?: 'solid' | 'ghost' | 'outline'
}

const colorStyles: Record<string, Record<string, string>> = {
  primary: {
    solid:
      'bg-primary text-white hover:bg-primary-dark focus:ring-primary-light',
    outline:
      'border border-primary text-primary hover:bg-primary-alpha focus:ring-primary-light',
    ghost: 'text-primary hover:bg-primary-alpha focus:ring-primary-light',
  },
  secondary: {
    solid:
      'bg-secondary text-white hover:bg-secondary-dark focus:ring-secondary-light',
    outline:
      'border border-secondary text-secondary hover:bg-secondary-alpha focus:ring-secondary-light',
    ghost: 'text-secondary hover:bg-secondary-alpha focus:ring-secondary-light',
  },
  danger: {
    solid: 'bg-danger text-white hover:bg-danger-dark focus:ring-danger-light',
    outline:
      'border border-danger text-danger hover:bg-danger-alpha focus:ring-danger-light',
    ghost: 'text-danger hover:bg-danger-alpha focus:ring-danger-light',
  },
  // ... Repeat for "tertiary", "warning", "danger", "success", "purple", "pink"
}

const IconButton: React.FC<IconButtonProps> = ({
  icon: Icon,
  ariaLabel,
  isRound = true,
  colorScheme = 'primary',
  size = 'md',
  variant = 'solid',
  ...buttonProps
}) => {
  const baseStyles =
    'flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-offset-2 cursor-pointer'
  const roundStyles = isRound ? 'rounded-full' : 'rounded-md'

  const sizeStyles = {
    sm: 'p-1 text-sm',
    md: 'p-2 text-base',
    lg: 'p-3 text-lg',
  }

  // Grab the correct classes
  const variantClasses =
    colorStyles[colorScheme]?.[variant] ?? colorStyles.primary.solid

  return (
    <button
      aria-label={ariaLabel}
      {...buttonProps}
      className={clsx(
        baseStyles,
        roundStyles,
        sizeStyles[size],
        variantClasses,
        buttonProps.className
      )}
    >
      <Icon />
    </button>
  )
}

export default IconButton
