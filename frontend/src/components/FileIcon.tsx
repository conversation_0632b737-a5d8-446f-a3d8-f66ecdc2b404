// FileIcon.tsx
import { getFileIcon } from '@/utils/fileIcons'
import React from 'react'

interface FileIconProps {
  fileName: string // e.g. "report.pdf"
  size?: number // optional icon size in px (if you want)
}

const FileIcon: React.FC<FileIconProps> = ({ fileName, size = 24 }) => {
  const { icon: IconComponent, colorClass } = getFileIcon(fileName)

  return (
    <div className={colorClass}>
      <IconComponent size={size} />
    </div>
  )
}

export default FileIcon
