'use client'

import { Fragment } from 'react'
import {
  Menu,
  MenuButton,
  MenuItem,
  MenuItems,
  Transition,
} from '@headlessui/react'
import clsx from 'clsx'

export type Action = {
  label: string
  onClick: () => void
  icon?: React.ReactNode
}

export type DropdownProps = {
  actions: Action[]
  children?: React.ReactNode
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'
  disabled?: boolean
}

export default function Dropdown({
  actions,
  children,
  position = 'bottom-left',
  disabled = false,
}: DropdownProps) {
  const positionClass = {
    'top-left': 'bottom-full right-0',
    'top-right': 'bottom-full left-0',
    'bottom-left': 'top-full right-0',
    'bottom-right': 'top-full left-0',
  } as const

  return (
    <Menu as="div" className="relative inline-block text-left">
      <div>
        <MenuButton
          disabled={disabled}
          onClick={(e) => e.stopPropagation()}
          className={clsx(
            'flex items-center rounded-full bg-gray-100 p-1 text-gray-400 transition-colors duration-150',
            !disabled &&
              'hover:text-gray-600 focus-visible:ring-2 focus-visible:ring-indigo-500 focus-visible:ring-offset-2 focus-visible:ring-offset-gray-100'
          )}
        >
          <span className="sr-only">Open options</span>
          {children}
        </MenuButton>
      </div>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <MenuItems
          className={clsx(
            'ring-opacity-5 absolute z-50 mt-2 min-w-[10rem] origin-top-right rounded-xl bg-white p-1 ring-1 shadow-xl ring-black focus:outline-none',
            positionClass[position]
          )}
          style={{ overflow: 'visible' }}
        >
          {actions.map((action) => (
            <MenuItem key={action.label}>
              {({ focus }) => (
                <button
                  onClick={(e) => {
                    e.stopPropagation()
                    action.onClick()
                  }}
                  className={clsx(
                    'flex w-full items-center gap-2 rounded-lg px-4 py-2 text-sm transition-colors duration-100',
                    focus
                      ? 'bg-gray-100 text-gray-900'
                      : 'text-gray-700 hover:bg-gray-50'
                  )}
                >
                  <span className="whitespace-nowrap">{action.label}</span>
                  {action.icon && (
                    <span className="flex-shrink-0 text-gray-400">
                      {action.icon}
                    </span>
                  )}
                </button>
              )}
            </MenuItem>
          ))}
        </MenuItems>
      </Transition>
    </Menu>
  )
}
