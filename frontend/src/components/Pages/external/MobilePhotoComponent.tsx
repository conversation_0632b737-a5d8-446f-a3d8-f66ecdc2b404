import { ControlContext } from '@/contexts/Control'
import { useAffranchissement } from '@/hooks/useAffranchissement'
import { useContext, useState } from 'react'
import { usePhotos } from '@/hooks/usePhotos'
import toast from 'react-hot-toast'
import { EnveloppeAffranchissement } from '@/models/affranchissement'
import { useDisclosure } from '@/hooks/useDisclosure'
import ControlScanner from '../control/scandit/ControlScanner'
import { ControlAffranchissementModal } from '../control/ControlAffranchissementModal'
import { CodeInputWithScanner } from '../control/CodeInputWithScanner'
import { TbScan, TbX, TbCheck, TbTrash } from 'react-icons/tb'
import { Button } from '@/components/Forms/Button'
import { AffranchissementEnveloppeItem } from '../control/EnveloppeAffranchissementItem'
import { EnveloppeService } from '@/services/enveloppeService'
import { useQueryClient } from '@tanstack/react-query'
import { FaImage } from 'react-icons/fa'

const MobilePhotoComponent = () => {
  const {
    isOpen: isAffranchissementModalOpen,
    onOpen: onAffranchissementModalOpen,
    onClose: onAffranchissementModalClose,
  } = useDisclosure()

  const queryClient = useQueryClient()
  const { currentEnveloppe, enveloppeKey, handleTerminerEnveloppe } =
    useContext(ControlContext)

  const {
    addAffranchissementMutation,
    getValidationInfo,
    findExistingCodeInAffranchissements,
    handleChangeQuantity,
    handleChangeLegitStatus,
  } = useAffranchissement()

  const { handleAddPhoto } = usePhotos({ currentEnveloppe })

  const [selectedAffranchissement, setSelectedAffranchissement] =
    useState<EnveloppeAffranchissement | null>(null)

  const [isFullscreen, setIsFullscreen] = useState(false)

  const handleAddAffranchissementCode = async (code: string) => {
    if (!currentEnveloppe) return

    const result = await addAffranchissementMutation.mutateAsync({
      body: { code },
    })

    if (findExistingCodeInAffranchissements(code)) {
      toast('Ce code est déjà présent dans les affranchissements')
      return
    }

    if (
      result.affranchissement &&
      result.affranchissement.informations.champs_requis.length > 0
    ) {
      setSelectedAffranchissement(result.affranchissement)
      onAffranchissementModalOpen()
    } else {
      toast.success('Affranchissement ajouté avec succès')
    }
  }

  const handleAffranchissementModalClose = () => {
    setSelectedAffranchissement(null)
    onAffranchissementModalClose()
  }

  const toggleFullscreenMode = () => {
    setIsFullscreen((prev) => !prev)
  }

  const handleOpenUpdateModal = (
    affranchissement: EnveloppeAffranchissement
  ) => {
    setSelectedAffranchissement(affranchissement)
    onAffranchissementModalOpen()
  }

  const handleDeleteAllAffranchissements = async () => {
    if (currentEnveloppe?.id) {
      const response = await EnveloppeService.deleteAllAffranchissements(
        currentEnveloppe.id
      )
      queryClient.setQueryData(['enveloppe', enveloppeKey], response.enveloppe)
    }
  }

  // Calculate totals
  const totalAffranchissements =
    currentEnveloppe?.affranchissements?.reduce(
      (acc, affranchissement) => acc + affranchissement.quantite,
      0
    ) || 0

  const totalPrice =
    currentEnveloppe?.informations.prix_affranchissements_valide.toFixed(2)

  if (!currentEnveloppe) return <div>Chargement...</div>

  return (
    <>
      {/* Scanner section */}
      <div
        className={`flex w-full flex-col ${
          isFullscreen ? 'fixed inset-0 z-50 h-screen bg-black' : ''
        }`}
      >
        {/* Scanner component - only shown in fullscreen mode */}
        {isFullscreen ? (
          <div className="relative h-full w-full">
            <ControlScanner
              onAddAffranchissement={handleAddAffranchissementCode}
              getValidationInfo={getValidationInfo}
              onCapturePhoto={handleAddPhoto}
              isFullscreen={true}
            />

            {/* Exit fullscreen button */}
            <button
              onClick={toggleFullscreenMode}
              className="absolute top-4 left-4 z-30 rounded-full bg-white/80 p-3 text-gray-800 shadow-lg backdrop-blur-sm"
              aria-label="Fermer le scanner"
            >
              <TbX className="h-6 w-6" />
            </button>
          </div>
        ) : (
          // Non-fullscreen mode with code input and affranchissement list
          <div className="flex h-full flex-col gap-8 px-4">
            {/* Code input with scanner button */}
            <div className="mt-4">
              <CodeInputWithScanner
                onSubmit={handleAddAffranchissementCode}
                placeholder="SmartData, S10, Timbre numérique"
              />

              {/* Button to activate fullscreen scanner */}
              <button
                onClick={toggleFullscreenMode}
                className="mt-4 flex w-full items-center justify-center gap-2 rounded-lg bg-blue-600 px-4 py-3 text-white shadow-md hover:bg-blue-700"
              >
                <TbScan className="h-5 w-5" />
                <span>Activer le scanner</span>
              </button>
            </div>

            <div className="rounded-lg border border-gray-200 p-2">
              {/* Mobile Affranchissement Panel */}
              <div className="mt-4 flex flex-col rounded-lg border border-gray-200">
                {/* List of affranchissements */}
                <div className="max-h-[50vh] overflow-y-auto select-none">
                  {currentEnveloppe?.affranchissements
                    ?.sort((a, b) => (a.id || 0) - (b.id || 0))
                    .map((affranchissement, index) => (
                      <AffranchissementEnveloppeItem
                        key={`mobile-affranchissement-${index}`}
                        affranchissement={affranchissement}
                        onOpenUpdateModal={handleOpenUpdateModal}
                        onChangeQuantity={handleChangeQuantity}
                        onChangeLegitStatus={handleChangeLegitStatus}
                      />
                    ))}

                  {(!currentEnveloppe?.affranchissements ||
                    currentEnveloppe.affranchissements.length === 0) && (
                    <div className="flex items-center justify-center p-6 text-gray-500">
                      Aucun affranchissement
                    </div>
                  )}
                </div>

                {/* Totals & delete action */}
                <div className="flex min-h-12 w-full flex-row items-center border-t border-gray-200 px-3 py-1.5 text-sm text-gray-600">
                  <div className="flex flex-grow flex-col">
                    <span>
                      <b>{totalAffranchissements}</b> Élément(s)
                      d'affranchissement
                    </span>
                    <span className="text-gray-400">
                      Total : <b>{totalPrice}€</b>
                    </span>
                  </div>
                  <TbTrash
                    className="ml-2 h-6 w-6 cursor-pointer text-gray-300 transition-all duration-150 hover:text-red-700"
                    onClick={handleDeleteAllAffranchissements}
                  />
                </div>
              </div>

              {/* Validate button */}
              <div className="flex w-full justify-end">
                <div className="mt-3 mb-6 flex items-center justify-center gap-2">
                  <span className="flex shrink-0 items-center gap-2 text-sm text-blue-400">
                    <b>{currentEnveloppe?.photos?.length ?? 0} / 1</b>{' '}
                    <FaImage />
                  </span>
                  {currentEnveloppe?.informations?.modifiable && (
                    <Button
                      variant="primary"
                      className="flex items-center justify-center pl-2"
                      disabled={!currentEnveloppe?.informations.complet}
                      onClick={() => {
                        if (currentEnveloppe?.informations.complet) {
                          handleTerminerEnveloppe()
                        }
                      }}
                    >
                      <TbCheck className="mr-2 h-5 w-5" />
                      Valider
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Modals */}
      {selectedAffranchissement && (
        <ControlAffranchissementModal
          initialAffranchissement={selectedAffranchissement}
          onClose={handleAffranchissementModalClose}
          isOpen={isAffranchissementModalOpen}
        />
      )}
    </>
  )
}

export default MobilePhotoComponent
