import React, { useState, useEffect } from 'react'
import { Modal } from '@/components/Modal'
import { Button } from '@/components/Forms/Button'
import { Role } from '@/models/usersModel'
import { Site } from '@/models/site'
import {
  FaUser,
  FaEnvelope,
  FaUserTag,
  FaBuilding,
  FaLock,
  FaUserPlus,
} from 'react-icons/fa'

interface SiteSelectorProps {
  sites: Site[]
  selectedSiteId: number
  onChange: (siteId: number) => void
  error?: string
}

const SiteSelector = ({
  sites,
  selectedSiteId,
  onChange,
  error,
}: SiteSelectorProps) => (
  <div>
    <label className="mb-2 block text-sm font-medium text-gray-700">
      <FaBuilding className="mr-2 inline h-4 w-4 text-gray-400" />
      Site <span className="text-red-500">*</span>
    </label>
    <select
      className={`mt-1 block w-full rounded-md px-3 py-2 shadow-sm ${
        error
          ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
          : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
      }`}
      value={selectedSiteId || ''}
      onChange={(e) => onChange(e.target.value ? parseInt(e.target.value) : 0)}
    >
      <option value="">Sélectionner un site</option>
      {sites.map((site: Site) => (
        <option key={site.id} value={site.id}>
          {site.nom || 'Nom de site indisponible'}
        </option>
      ))}
    </select>
    {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
  </div>
)

interface CreateUserData {
  email: string
  password: string
  first_name: string
  last_name: string
  role: Role
  site_id: number
}

interface CreateUserModalProps {
  sites: Site[]
  isOpen: boolean
  onClose: () => void
  onSave: (userData: CreateUserData) => void
  isLoading?: boolean
}

export const CreateUserModal: React.FC<CreateUserModalProps> = ({
  sites,
  isOpen,
  onClose,
  onSave,
  isLoading = false,
}) => {
  const [newUser, setNewUser] = useState<CreateUserData>({
    email: '',
    password: '',
    first_name: '',
    last_name: '',
    role: 'user' as Role,
    site_id: 0,
  })

  const [errors, setErrors] = useState<
    Partial<Record<keyof CreateUserData, string>>
  >({})

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setNewUser({
        email: '',
        password: '',
        first_name: '',
        last_name: '',
        role: 'user' as Role,
        site_id: 0,
      })
      setErrors({})
    }
  }, [isOpen])

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof CreateUserData, string>> = {}

    if (!newUser.email.trim()) {
      newErrors.email = "L'email est requis"
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newUser.email)) {
      newErrors.email = "Format d'email invalide"
    }

    if (!newUser.password.trim()) {
      newErrors.password = 'Le mot de passe est requis'
    } else if (newUser.password.length < 6) {
      newErrors.password = 'Le mot de passe doit contenir au moins 6 caractères'
    }

    if (!newUser.first_name.trim()) {
      newErrors.first_name = 'Le prénom est requis'
    }

    if (!newUser.last_name.trim()) {
      newErrors.last_name = 'Le nom est requis'
    }

    if (!newUser.site_id) {
      newErrors.site_id = "La sélection d'un site est requise"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateForm()) {
      onSave(newUser)
    }
  }

  const handleClose = () => {
    setNewUser({
      email: '',
      password: '',
      first_name: '',
      last_name: '',
      role: 'user' as Role,
      site_id: 0,
    })
    setErrors({})
    onClose()
  }

  const getRoleLabel = (role: string) => {
    const roleLabels = {
      guest: 'Invité',
      user: 'Utilisateur',
      manager: 'Manager',
      admin: 'Administrateur',
    }
    return roleLabels[role as keyof typeof roleLabels] || role
  }

  const getRoleDescription = (role: string) => {
    const descriptions = {
      guest: 'Accès en lecture seule',
      user: 'Accès standard aux fonctionnalités',
      manager: "Gestion d'équipe et rapports",
      admin: 'Accès complet à toutes les fonctionnalités',
    }
    return descriptions[role as keyof typeof descriptions] || ''
  }

  return (
    <Modal
      open={isOpen}
      onClose={handleClose}
      maxWidth="5xl"
      title="Créer un nouvel utilisateur"
    >
      <div className="p-6">
        {/* Header */}
        <div className="mb-6 rounded-lg bg-blue-50 p-4">
          <div className="flex items-center space-x-3">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
              <FaUserPlus className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Nouveau compte utilisateur
              </h3>
              <p className="text-sm text-gray-600">
                Remplissez les informations ci-dessous pour créer un nouveau
                compte
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSave} className="space-y-6" autoComplete="off">
          {/* Personal Information Section */}
          <div className="rounded-lg border border-gray-200 p-4">
            <div className="mb-4 flex items-center">
              <FaUser className="mr-2 h-5 w-5 text-blue-600" />
              <h4 className="text-lg font-medium text-gray-900">
                Informations personnelles
              </h4>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Prénom <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  required
                  className={`mt-1 block w-full rounded-md px-3 py-2 shadow-sm ${
                    errors.first_name
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
                  }`}
                  value={newUser.first_name}
                  onChange={(e) =>
                    setNewUser({ ...newUser, first_name: e.target.value })
                  }
                  placeholder="Jean"
                />
                {errors.first_name && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.first_name}
                  </p>
                )}
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Nom <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  required
                  className={`mt-1 block w-full rounded-md px-3 py-2 shadow-sm ${
                    errors.last_name
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
                  }`}
                  value={newUser.last_name}
                  onChange={(e) =>
                    setNewUser({ ...newUser, last_name: e.target.value })
                  }
                  placeholder="Dupont"
                />
                {errors.last_name && (
                  <p className="mt-1 text-sm text-red-600">
                    {errors.last_name}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Account Information Section */}
          <div className="rounded-lg border border-gray-200 p-4">
            <div className="mb-4 flex items-center">
              <FaEnvelope className="mr-2 h-5 w-5 text-green-600" />
              <h4 className="text-lg font-medium text-gray-900">
                Informations de connexion
              </h4>
            </div>

            <div className="space-y-4">
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Email <span className="text-red-500">*</span>
                </label>
                <input
                  type="email"
                  required
                  autoComplete="new-email"
                  autoCorrect="off"
                  autoCapitalize="off"
                  data-form-type="other"
                  name="create-user-email"
                  className={`mt-1 block w-full rounded-md px-3 py-2 shadow-sm ${
                    errors.email
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
                  }`}
                  value={newUser.email}
                  onChange={(e) =>
                    setNewUser({ ...newUser, email: e.target.value })
                  }
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-600">{errors.email}</p>
                )}
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  <FaLock className="mr-1 inline h-3 w-3" />
                  Mot de passe temporaire{' '}
                  <span className="text-red-500">*</span>
                </label>
                <input
                  type="password"
                  autoComplete="new-password"
                  autoCorrect="off"
                  autoCapitalize="off"
                  spellCheck="false"
                  name="create-user-password"
                  data-form-type="other"
                  required
                  className={`mt-1 block w-full rounded-md px-3 py-2 shadow-sm ${
                    errors.password
                      ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                      : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
                  }`}
                  value={newUser.password}
                  onChange={(e) =>
                    setNewUser({ ...newUser, password: e.target.value })
                  }
                  placeholder="Minimum 6 caractères"
                />
                {errors.password && (
                  <p className="mt-1 text-sm text-red-600">{errors.password}</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  L'utilisateur devra changer ce mot de passe lors de sa
                  première connexion
                </p>
              </div>
            </div>
          </div>

          {/* Role Section */}
          <div className="rounded-lg border border-gray-200 p-4">
            <div className="mb-4 flex items-center">
              <FaUserTag className="mr-2 h-5 w-5 text-purple-600" />
              <h4 className="text-lg font-medium text-gray-900">
                Rôle et permissions
              </h4>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Rôle <span className="text-red-500">*</span>
              </label>
              <select
                className="mt-1 block w-full rounded-md border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                value={newUser.role}
                onChange={(e) =>
                  setNewUser({ ...newUser, role: e.target.value as Role })
                }
              >
                <option value="guest">Invité</option>
                <option value="user">Utilisateur</option>
                <option value="manager">Manager</option>
                <option value="admin">Administrateur</option>
              </select>
              <p className="mt-1 text-sm text-gray-600">
                <strong>{getRoleLabel(newUser.role)}:</strong>{' '}
                {getRoleDescription(newUser.role)}
              </p>
            </div>
          </div>

          {/* Site Section */}
          <div className="rounded-lg border border-gray-200 p-4">
            <div className="mb-4 flex items-center">
              <FaBuilding className="mr-2 h-5 w-5 text-orange-600" />
              <h4 className="text-lg font-medium text-gray-900">Affectation</h4>
            </div>

            <SiteSelector
              sites={sites}
              selectedSiteId={newUser.site_id}
              onChange={(siteId) => setNewUser({ ...newUser, site_id: siteId })}
              error={errors.site_id}
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 border-t border-gray-200 pt-6">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
            >
              Annuler
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Création...' : "Créer l'utilisateur"}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  )
}
