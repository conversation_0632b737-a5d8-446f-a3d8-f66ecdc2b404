import React, { useState, useEffect } from 'react'
import { Modal } from '@/components/Modal'
import { Button } from '@/components/Forms/Button'
import { Role, UserPublic } from '@/models/usersModel'
import { Site } from '@/models/site'
import {
  FaUser,
  FaEnvelope,
  FaUserTag,
  FaBuilding,
  FaToggleOn,
  FaToggleOff,
} from 'react-icons/fa'

interface SiteSelectorProps {
  sites: Site[]
  selectedSiteId: number
  onChange: (siteId: number) => void
}

const SiteSelector = ({
  sites,
  selectedSiteId,
  onChange,
}: SiteSelectorProps) => (
  <div>
    <label className="mb-2 block text-sm font-medium text-gray-700">
      <FaBuilding className="mr-2 inline h-4 w-4 text-gray-400" />
      Site
    </label>
    <select
      className="mt-1 block w-full rounded-md border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500"
      value={selectedSiteId || ''}
      onChange={(e) => onChange(e.target.value ? parseInt(e.target.value) : 0)}
    >
      <option value="">Non attribué</option>
      {sites.map((site: Site) => (
        <option key={site.id} value={site.id}>
          {site.nom || 'Nom de site indisponible'}
        </option>
      ))}
    </select>
  </div>
)

interface EditUserModalProps {
  user: UserPublic | null
  sites: Site[]
  isOpen: boolean
  onClose: () => void
  onSave: (userData: {
    role: Role
    site_id: number
    is_active?: boolean
  }) => void
  isLoading?: boolean
}

export const EditUserModal: React.FC<EditUserModalProps> = ({
  user,
  sites,
  isOpen,
  onClose,
  onSave,
  isLoading = false,
}) => {
  const [editingUser, setEditingUser] = useState<UserPublic | null>(null)

  // Reset form when user changes or modal opens
  useEffect(() => {
    if (user && isOpen) {
      setEditingUser({ ...user })
    }
  }, [user, isOpen])

  const handleSave = () => {
    if (editingUser) {
      onSave({
        role: editingUser.role,
        site_id: editingUser.site_id,
        is_active: editingUser.is_active,
      })
    }
  }

  const handleClose = () => {
    setEditingUser(null)
    onClose()
  }

  const getRoleLabel = (role: string) => {
    const roleLabels = {
      guest: 'Invité',
      user: 'Utilisateur',
      manager: 'Manager',
      admin: 'Administrateur',
    }
    return roleLabels[role as keyof typeof roleLabels] || role
  }

  const getSiteName = (siteId: number) => {
    const site = sites.find((s) => s.id === siteId)
    return site?.nom || 'Non attribué'
  }

  if (!editingUser) return null

  return (
    <Modal
      open={isOpen}
      onClose={handleClose}
      maxWidth="xl"
      title={`Modifier l'utilisateur`}
    >
      <div className="p-6">
        {/* User Info Header */}
        <div className="mb-6 rounded-lg bg-gray-50 p-4">
          <div className="flex items-center space-x-3">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
              <FaUser className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {editingUser.full_name || 'Nom non renseigné'}
              </h3>
              <div className="flex items-center text-sm text-gray-600">
                <FaEnvelope className="mr-1 h-3 w-3" />
                {editingUser.email}
              </div>
            </div>
          </div>
        </div>

        {/* Form Fields */}
        <div className="space-y-6">
          {/* Role Section */}
          <div className="rounded-lg border border-gray-200 p-4">
            <div className="mb-4 flex items-center">
              <FaUserTag className="mr-2 h-5 w-5 text-blue-600" />
              <h4 className="text-lg font-medium text-gray-900">
                Rôle et permissions
              </h4>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700">
                Rôle actuel:{' '}
                <span className="font-semibold">
                  {getRoleLabel(editingUser.role)}
                </span>
              </label>
              <select
                className="mt-1 block w-full rounded-md border-gray-300 px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                value={editingUser.role}
                onChange={(e) =>
                  setEditingUser({
                    ...editingUser,
                    role: e.target.value as Role,
                  })
                }
              >
                <option value="guest">Invité</option>
                <option value="user">Utilisateur</option>
                <option value="manager">Manager</option>
                <option value="admin">Administrateur</option>
              </select>
            </div>
          </div>

          {/* Site Section */}
          <div className="rounded-lg border border-gray-200 p-4">
            <div className="mb-4 flex items-center">
              <FaBuilding className="mr-2 h-5 w-5 text-green-600" />
              <h4 className="text-lg font-medium text-gray-900">Affectation</h4>
            </div>

            <div>
              <p className="mb-2 text-sm text-gray-600">
                Site actuel:{' '}
                <span className="font-semibold">
                  {getSiteName(editingUser.site_id)}
                </span>
              </p>
              <SiteSelector
                sites={sites}
                selectedSiteId={editingUser.site_id}
                onChange={(siteId) =>
                  setEditingUser({ ...editingUser, site_id: siteId })
                }
              />
            </div>
          </div>

          {/* Status Section */}
          <div className="rounded-lg border border-gray-200 p-4">
            <div className="mb-4 flex items-center">
              {editingUser.is_active ? (
                <FaToggleOn className="mr-2 h-5 w-5 text-green-600" />
              ) : (
                <FaToggleOff className="mr-2 h-5 w-5 text-red-600" />
              )}
              <h4 className="text-lg font-medium text-gray-900">
                Statut du compte
              </h4>
            </div>

            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={editingUser.is_active}
                  onChange={(e) =>
                    setEditingUser({
                      ...editingUser,
                      is_active: e.target.checked,
                    })
                  }
                  className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-700">Compte actif</span>
              </label>
              <span
                className={`inline-flex items-center rounded-full px-3 py-1 text-xs font-semibold ${
                  editingUser.is_active
                    ? 'border-green-200 bg-green-100 text-green-800'
                    : 'border-red-200 bg-red-100 text-red-800'
                } border`}
              >
                {editingUser.is_active ? 'Actif' : 'Inactif'}
              </span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="mt-8 flex justify-end space-x-3 border-t border-gray-200 pt-6">
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            Annuler
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? 'Mise à jour...' : 'Mettre à jour'}
          </Button>
        </div>
      </div>
    </Modal>
  )
}
