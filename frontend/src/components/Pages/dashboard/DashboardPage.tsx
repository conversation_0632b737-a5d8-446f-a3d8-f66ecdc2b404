import { useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { useQuery } from '@tanstack/react-query'
import { MetricsService } from '@/services/metricsService'
import { DashboardMetricsResponse } from '@/models/metrics'
import { But<PERSON> } from '@/components/Forms/Button'
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts'
import clsx from 'clsx'
import { TbLoader, TbCalendarTime, TbDownload } from 'react-icons/tb'
import { SiteService } from '@/services/siteService'
import useAuth from '@/hooks/useAuth'

/**
 * Tableau de bord – métriques /api/v1/dashboard/metrics (100 % Tailwind)
 */
export default function DashboardPage() {
  const { user } = useAuth();
  
  const formatDateTimeLocal = (date: Date): string => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  // Fonctions pour les raccourcis temporels
  const getDernierJourOuvrable = (): { start: Date, end: Date } => {
    const now = new Date();
    const jourSemaine = now.getDay(); // 0 = dimanche, 6 = samedi
    let dernierJourOuvre = new Date(now);
    
    // Déterminer le dernier jour ouvré (lundi à vendredi)
    if (jourSemaine === 0) { // Dimanche
      dernierJourOuvre.setDate(now.getDate() - 2); // Vendredi
    } else if (jourSemaine === 6) { // Samedi
      dernierJourOuvre.setDate(now.getDate() - 1); // Vendredi
    } else if (jourSemaine === 1) { // Lundi
      dernierJourOuvre.setDate(now.getDate() - 3); // Vendredi précédent
    }
    // Sinon on est déjà sur un jour ouvré
    
    // Début du jour à 00:00
    const start = new Date(dernierJourOuvre);
    start.setHours(0, 0, 0, 0);
    
    // Fin du jour à 23:59:59
    const end = new Date(dernierJourOuvre);
    end.setHours(23, 59, 59, 999);
    
    return { start, end };
  };
  
  const getDerniereSemaine = (): { start: Date, end: Date } => {
    const now = new Date();
    const jourSemaine = now.getDay(); // 0 = dimanche, 1 = lundi, ..., 6 = samedi
    
    // Calculer le lundi de la dernière semaine complète
    const start = new Date(now);
    
    if (jourSemaine === 0) {
      start.setDate(now.getDate() - 6);
    } 
    else if(jourSemaine === 6){
      start.setDate(now.getDate() - (jourSemaine - 1) -7);
    }
    else {
      start.setDate(now.getDate() - (jourSemaine - 1) -7);
    }

    start.setHours(0, 0, 0, 0);
    
    // Calculer le vendredi de cette semaine
    const end = new Date(start);
    end.setDate(start.getDate() + 4); // +4 jours = vendredi
    end.setHours(23, 59, 59, 999);
    
    return { start, end };
  };
  
  // const getDernierMois = (): { start: Date, end: Date } => {
  //   const now = new Date();
    
  //   // Premier jour du mois précédent
  //   const start = new Date(now.getFullYear(), now.getMonth() - 1, 1);
  //   start.setHours(0, 0, 0, 0);
    
  //   // Dernier jour du mois précédent
  //   const end = new Date(now.getFullYear(), now.getMonth(), 0);
  //   end.setHours(23, 59, 59, 999);
    
  //   return { start, end };
  // };
  
  const getAnneeEnCours = (): { start: Date, end: Date } => {
    const now = new Date();
    
    // Premier jour de l'année en cours
    const start = new Date(now.getFullYear(), 0, 1);
    start.setHours(0, 0, 0, 0);
    
    // Dernier jour de l'année en cours
    const end = new Date(now.getFullYear(), 11, 31);
    end.setHours(23, 59, 59, 999);
    
    return { start, end };
  };

  const getMoisEnCours = (): { start: Date, end: Date } => {
    const now = new Date();
    
    // Premier jour du mois en cours
    const start = new Date(now.getFullYear(), now.getMonth(), 1);
    start.setHours(0, 0, 0, 0);
    
    // Dernier jour du mois en cours (jour 0 du mois suivant = dernier jour du mois courant)
    const end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    end.setHours(23, 59, 59, 999);
    
    return { start, end };
  };

  /* ————————————— 1. Sélecteur de période ————————————— */
  const {
    register,
    handleSubmit,
    formState: { isSubmitting },
    watch,
    setValue,
  } = useForm<{ startDateTime: string; endDateTime: string; siteId: string }>({
    defaultValues: {
      startDateTime: formatDateTimeLocal(new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)),
      endDateTime: formatDateTimeLocal(new Date()),
      siteId: user?.site_id ? user.site_id.toString() : "",
    },
  })

  const { startDateTime, endDateTime, siteId } = watch();

  // Gestionnaires pour les raccourcis temporels
  const appliquerPeriode = (periode: { start: Date, end: Date }) => {
    setValue('startDateTime', formatDateTimeLocal(periode.start));
    setValue('endDateTime', formatDateTimeLocal(periode.end));
    refetch();
  };

  /* ————————————— 1.1 Récupération des sites ————————————— */
  const { data: sites = [] } = useQuery({
    queryKey: ['sites'],
    queryFn: () => SiteService.getSites(),
    staleTime: 5 * 60 * 1000,
  })

  /* ————————————— 2. Récupération des données ————————————— */
  const {
    data: metrics,
    isLoading,
    refetch,
  } = useQuery<DashboardMetricsResponse>({
    queryKey: ['metrics', startDateTime, endDateTime, siteId],
    queryFn: () => MetricsService.getMetrics({ 
      startDateTime: new Date(startDateTime).toISOString(), 
      endDateTime: new Date(endDateTime).toISOString(),
      siteId: siteId ? parseInt(siteId) : undefined 
    }) as any,
    staleTime: 3 * 60 * 1000,
  })

  const minutesParJour = 7 * 60

  /* ————————————— 3. Agrégats rapides ————————————— */
  const kpis = useMemo(() => {
    if (!metrics) return null
    const totalSousAff = metrics.expediteurs_sous_affranchissement.reduce(
      (sum, e) => sum + (e.sous_aff ?? 0),
      0
    )
    const totalPlis = metrics.enveloppes_par_jour.reduce(
      (sum, d) => sum + d.nombre,
      0
    )
    const utilisateursActifs = metrics.temps_moyen_utilisateur.length
    
    // Calcul du temps moyen global
    const tempsMoyenGlobal = metrics.temps_moyen_utilisateur.length > 0 
      ?  metrics.temps_moyen_utilisateur.reduce((sum, u) => sum + ( minutesParJour / u.moyenne_plis_par_jour), 0) / utilisateursActifs
      : 0
    
    // Calcul du ratio temps/argent (minutes par euro)
    const tempsMoyenParEuro = totalSousAff > 0 
      ? tempsMoyenGlobal / (totalSousAff / totalPlis)
      : 0
    
    return { totalSousAff, totalPlis, utilisateursActifs, tempsMoyenGlobal, tempsMoyenParEuro }
  }, [metrics])

  /* ————————————— 4. Helpers d’affichage ————————————— */
  const KpiCard = ({ label, value }: { label: string; value: string }) => (
    <div className="animate-fadeIn w-full rounded-lg border border-gray-200 bg-white p-4 shadow-sm">
      <span className="text-xs font-medium text-gray-500">{label}</span>
      <span className="mt-1 block text-2xl font-semibold text-blue-700">
        {value}
      </span>
    </div>
  )

  const Section = ({
    title,
    children,
  }: {
    title: string
    children: React.ReactNode
  }) => (
    <div className="animate-fadeIn rounded-lg border border-gray-200 bg-white shadow-sm">
      <div className="border-b border-gray-200 px-4 py-3">
        <h2 className="text-base font-medium">{title}</h2>
      </div>
      <div className="p-4">{children}</div>
    </div>
  )

  /* ————————————— Fonction d'export graphique ————————————— */
  const exportDashboardAsImage = async () => {
    if (!metrics) return;
    
    try {
      // Importation dynamique de html2canvas
      const html2canvas = (await import('html2canvas')).default;
      
      // Référence à l'élément du dashboard
      const dashboardElement = document.getElementById('dashboard-content');
      
      if (!dashboardElement) return;
      
      // Afficher un indicateur de chargement
      const loadingElement = document.createElement('div');
      loadingElement.className = 'fixed inset-0 bg-black/50 flex items-center justify-center z-50';
      loadingElement.innerHTML = '<div class="bg-white p-4 rounded-lg shadow-lg">Génération de l\'image en cours...</div>';
      document.body.appendChild(loadingElement);
      
      // Créer une copie du contenu dans un nouvel élément avec des styles simplifiés
      const clonedElement = dashboardElement.cloneNode(true) as HTMLElement;
      clonedElement.style.position = 'absolute';
      clonedElement.style.left = '-9999px';
      clonedElement.style.top = '-9999px';
      
      // Appliquer des styles de base pour éviter les problèmes avec oklch
      const applyBasicStyles = (element: HTMLElement) => {
        // Remplacer les couleurs potentiellement problématiques
        element.style.color = '#000000';
        element.style.backgroundColor = '#ffffff';
        
        // Appliquer récursivement aux enfants
        Array.from(element.children).forEach(child => {
          if (child instanceof HTMLElement) {
            applyBasicStyles(child);
          }
        });
      };
      
      applyBasicStyles(clonedElement);
      document.body.appendChild(clonedElement);
      
      // Capture de l'écran avec l'élément cloné
      const canvas = await html2canvas(clonedElement, {
        scale: 2,
        useCORS: true,
        logging: false,
        allowTaint: true,
        backgroundColor: '#ffffff',
        onclone: (documentClone) => {
          // Fonction appelée après le clonage du document
          // On peut faire des ajustements supplémentaires ici si nécessaire
          const clonedDashboard = documentClone.getElementById('dashboard-content');
          if (clonedDashboard) {
            // Préserver les dimensions
            clonedDashboard.style.width = `${dashboardElement.offsetWidth}px`;
          }
        }
      });
      
      // Supprimer l'élément cloné
      document.body.removeChild(clonedElement);
      
      // Conversion en PNG
      const imageUrl = canvas.toDataURL('image/png');
      
      // Formatage des dates pour le nom de fichier
      const startDate = new Date(startDateTime);
      const endDate = new Date(endDateTime);
      
      // Format: YYYYMMDD_HHMM
      const formatDateForFilename = (date: Date) => {
        return date.toISOString().replace(/[-:T.Z]/g, '').substring(0, 12);
      };
      
      // Obtenir le nom du site sélectionné
      const siteName = siteId 
        ? (sites.find(s => s.id.toString() === siteId)?.nom || 'tous-sites').replace(/\s+/g, '-').toLowerCase()
        : 'tous-sites';
      
      // Créer le nom du fichier
      const fileName = `tableau-de-bord_${siteName}_${formatDateForFilename(startDate)}-${formatDateForFilename(endDate)}.png`;
      
      // Téléchargement
      const link = document.createElement('a');
      link.href = imageUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      // Suppression de l'indicateur de chargement
      document.body.removeChild(loadingElement);
    } catch (error) {
      console.error('Erreur lors de l\'export du tableau de bord:', error);
      alert('Une erreur est survenue lors de l\'export du tableau de bord.');
    }
  };

  /* ————————————— 5. UI ————————————— */
  return (
    <div className="mx-auto flex flex-col gap-8 p-6 lg:p-10">
      {/* Titre et boutons d'export */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-blue-800">Tableau de bord</h1>
        {!isLoading && metrics && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={exportDashboardAsImage}
              title="Exporter en PNG"
            >
              <TbDownload className="h-4 w-4" />
              Exporter
            </Button>
          </div>
        )}
      </div>

      {/* Contenu avec ID pour l'export */}
      <div id="dashboard-content-main">
        {/* Sélecteur de dates */}
        <form
          onSubmit={handleSubmit(() => refetch())}
          className="flex flex-col gap-4 mb-8"
        >
          <div className="flex flex-wrap items-end gap-3">
            <div className="flex flex-col gap-1">
              <label htmlFor="startDateTime" className="text-xs text-gray-600">
                Du
              </label>
              <input
                id="startDateTime"
                type="datetime-local"
                className="rounded-md border border-gray-300 px-2 py-1 text-sm"
                {...register('startDateTime')}
              />
            </div>
            <div className="flex flex-col gap-1">
              <label htmlFor="endDateTime" className="text-xs text-gray-600">
                Au
              </label>
              <input
                id="endDateTime"
                type="datetime-local"
                className="rounded-md border border-gray-300 px-2 py-1 text-sm"
                {...register('endDateTime')}
              />
            </div>
            <div className="flex flex-col gap-1">
              <label htmlFor="siteId" className="text-xs text-gray-600">
                Site
              </label>
              <select
                id="siteId"
                className="rounded-md border border-gray-300 px-2 py-1 text-sm"
                {...register('siteId')}
                value={siteId}
              >
                <option value="">Tous les sites</option>
                {sites.map((site) => (
                  <option key={site.id} value={site.id.toString()}>
                    {site.nom}
                  </option>
                ))}
              </select>
            </div>
            <Button
              type="submit"
              variant="primary"
              className="h-9 px-4"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <TbLoader className="h-4 w-4 animate-spin" />
              ) : (
                'Appliquer'
              )}
            </Button>
          </div>
          
          {/* Raccourcis temporels */}
          <div className="flex flex-wrap gap-2">
            <Button
              type="button"
              variant="outline"
              className="flex items-center text-xs"
              onClick={() => appliquerPeriode(getDernierJourOuvrable())}
            >
              <TbCalendarTime className="mr-1 h-4 w-4" />
              Dernier jour ouvrable
            </Button>
            <Button
              type="button"
              variant="outline"
              className="flex items-center text-xs"
              onClick={() => appliquerPeriode(getDerniereSemaine())}
            >
              <TbCalendarTime className="mr-1 h-4 w-4" />
              Dernière semaine
            </Button>
            <Button
              type="button"
              variant="outline"
              className="flex items-center text-xs"
              onClick={() => appliquerPeriode(getMoisEnCours())}
            >
              <TbCalendarTime className="mr-1 h-4 w-4" />
              Mois en cours
            </Button>
            <Button
              type="button"
              variant="outline"
              className="flex items-center text-xs"
              onClick={() => appliquerPeriode(getAnneeEnCours())}
            >
              <TbCalendarTime className="mr-1 h-4 w-4" />
              Année en cours
            </Button>
          </div>
        </form>

        {/* Loader */}
        {isLoading && (
          <div className="flex w-full items-center justify-center py-20">
            <TbLoader className="h-8 w-8 animate-spin text-blue-600" />
          </div>
        )}

        {/* Contenu */}
        {!isLoading && metrics && (
          <div id="dashboard-content">
            {/* Période sélectionnée */}
            <div className="mb-6 text-center">
              <p className="text-sm text-gray-600">
                Période du <span className="font-medium">{new Date(startDateTime).toLocaleDateString('fr-FR', {day: '2-digit', month: '2-digit', year: 'numeric'})} à {new Date(startDateTime).toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'})}</span> au <span className="font-medium">{new Date(endDateTime).toLocaleDateString('fr-FR', {day: '2-digit', month: '2-digit', year: 'numeric'})} à {new Date(endDateTime).toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'})}</span>
                {siteId && sites.length > 0 && (
                  <> - Site : <span className="font-medium">{sites.find(s => s.id.toString() === siteId)?.nom || 'Tous les sites'}</span></>
                )}
              </p>
            </div>
            
            {/* KPI */}
            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4 mb-8">
              <KpiCard
                label="Sous-affranchissement total (€)"
                value={kpis!.totalSousAff.toLocaleString('fr-FR', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}
              />
              <KpiCard label="Plis terminés" value={kpis!.totalPlis.toString()} />
              <KpiCard
                label="Utilisateurs actifs"
                value={kpis!.utilisateursActifs.toString()}
              />
              <KpiCard
                label="Temps moyen par pli (min)"
                value={kpis!.tempsMoyenGlobal.toLocaleString('fr-FR', {
                  minimumFractionDigits: 1,
                  maximumFractionDigits: 1,
                })}
              />
            </div>

            {/* Graphique */}
            <Section title="Plis terminés par jour">
              <div className="h-72">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={metrics.enveloppes_par_jour}
                    margin={{ right: 20 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="jour" tick={{ fontSize: 12 }} />
                    <YAxis />
                    <Tooltip formatter={(v: any) => v.toLocaleString()} />
                    <Line
                      type="monotone"
                      dataKey="nombre"
                      strokeWidth={2}
                      dot={false}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </Section>

            {/* Top expéditeurs */}
            <Section title="Top 10 expéditeurs (gains potentiels)">
              <div className="max-h-[50vh] overflow-y-auto">
                <table className="min-w-full text-sm">
                  <thead className="sticky top-0 bg-gray-50 text-left">
                    <tr>
                      <th className="px-4 py-2 font-medium">Expéditeur</th>
                      <th className="px-4 py-2 text-right font-medium">Volume</th>
                      <th className="px-4 py-2 text-right font-medium">Sous-affranchissement €</th>
                      <th className="px-4 py-2 text-right font-medium">Moyenne Cas C €</th>
                      <th className="px-4 py-2 text-right font-medium">Cas C total €</th>
                    </tr>
                  </thead>
                  <tbody>
                    {metrics.top10_expediteurs.map((e, idx) => (
                      <tr
                        key={idx}
                        className={clsx(
                          idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                        )}
                      >
                        <td className="px-4 py-2 font-medium whitespace-nowrap">
                          {e.nom}
                        </td>
                        <td className="px-4 py-2 text-right whitespace-nowrap">
                          {e.volume?.toLocaleString('fr-FR') ?? '—'}
                        </td>
                        <td className="px-4 py-2 text-right whitespace-nowrap">
                          {e.sous_aff?.toLocaleString('fr-FR', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          }) ?? '—'}
                        </td>
                        <td className="px-4 py-2 text-right whitespace-nowrap">
                          {e.moyenne_cas_c?.toLocaleString('fr-FR', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          }) ?? '—'}
                        </td>
                        <td className="px-4 py-2 text-right whitespace-nowrap">
                          {e.cas_c_shipping_ttc?.toLocaleString('fr-FR', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          }) ?? '—'}
                        </td>                        
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Section>

            {/* Gains par site */}
            <Section title="Gains par site">
              <div className="max-h-[50vh] overflow-y-auto">
                <table className="min-w-full text-sm">
                  <thead className="sticky top-0 bg-gray-50 text-left">
                    <tr>
                      <th className="px-4 py-2 font-medium">Site</th>
                      <th className="px-4 py-2 text-right font-medium">Volume</th>
                      <th className="px-4 py-2 text-right font-medium">Sous-aff €</th>
                      <th className="px-4 py-2 text-right font-medium">Moyenne sous-aff €</th>
                      <th className="px-4 py-2 text-right font-medium">Cas C €</th>
                    </tr>
                  </thead>
                  <tbody>
                    {metrics.gains_par_site.map((g, idx) => (
                      <tr
                        key={idx}
                        className={clsx(
                          idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                        )}
                      >
                        <td className="px-4 py-2 whitespace-nowrap">{g.site}</td>
                        <td className="px-4 py-2 text-right whitespace-nowrap">
                          {g.volume?.toLocaleString('fr-FR') ?? '—'}
                        </td>
                        <td className="px-4 py-2 text-right whitespace-nowrap">
                          {g.sous_aff?.toLocaleString('fr-FR', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          }) ?? '—'}
                        </td>
                        <td className="px-4 py-2 text-right whitespace-nowrap">
                          {g.moyenne_sous_aff?.toLocaleString('fr-FR', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          }) ?? '—'}
                        </td>
                        <td className="px-4 py-2 text-right whitespace-nowrap">
                          {g.cas_c_shipping_ttc?.toLocaleString('fr-FR', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          }) ?? '—'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Section>

            <Section title="Données de production">
              <div className="max-h-[50vh] overflow-y-auto">
                <table className="min-w-full text-sm">
                  <thead className="sticky top-0 bg-gray-50 text-left">
                    <tr>
                      <th className="px-4 py-2 font-medium">Email</th>
                      <th className="px-4 py-2 text-right font-medium">Nombre de plis</th>
                      <th className="px-4 py-2 text-right font-medium">Plis par jour</th>
                      <th className="px-4 py-2 text-right font-medium">Temps moyen par pli en minutes</th>
                    </tr>
                  </thead>
                  <tbody>
                    {metrics.temps_moyen_utilisateur.map((u, idx) => (
                      <tr
                        key={idx}
                        className={clsx(
                          idx % 2 === 0 ? 'bg-white' : 'bg-gray-50'
                        )}
                      >
                        <td className="px-4 py-2 font-medium whitespace-nowrap">
                          {u.user_email}
                        </td>
                        <td className="px-4 py-2 text-right whitespace-nowrap">
                          {u.nombre_plis}
                        </td>
                        <td className="px-4 py-2 text-right whitespace-nowrap">
                          {u.moyenne_plis_par_jour.toLocaleString('fr-FR', {
                            minimumFractionDigits: 1,
                            maximumFractionDigits: 1,
                          })}
                        </td>
                        <td className="px-4 py-2 text-right whitespace-nowrap">
                         { (minutesParJour  / u.moyenne_plis_par_jour).toLocaleString('fr-FR', {
                            minimumFractionDigits: 1,
                            maximumFractionDigits: 1,
                          })}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Section>
          </div>
        )}
      </div>
    </div>
  )
}
