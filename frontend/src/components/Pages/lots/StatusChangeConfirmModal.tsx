import { Button } from '@/components/Forms/Button'
import Modal from '@/components/Forms/Modal'
import { TbAlertTriangle } from 'react-icons/tb'
import { LotExpediteur } from '@/models/lotExpediteurs'

type Props = {
  isOpen: boolean
  onClose: () => void
  selectedLot: LotExpediteur | null
  newStatus: string
  onConfirm: () => void
  isLoading?: boolean
}

export const StatusChangeConfirmModal = ({
  isOpen,
  onClose,
  selectedLot,
  newStatus,
  onConfirm,
  isLoading = false,
}: Props) => {
  return (
    <Modal
      title="Confirmation de changement de statut"
      isShown={isOpen}
      closeModal={onClose}
    >
      <div className="py-4">
        <div className="flex items-center rounded-lg bg-amber-50 p-4">
          <div className="flex-shrink-0">
            <TbAlertTriangle className="h-5 w-5 text-amber-500" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-amber-800">
              Changement de statut
            </h3>
            <div className="mt-2 text-sm text-amber-700">
              <p>
                Êtes-vous sûr de vouloir changer le statut du lot{' '}
                <span className="font-semibold">#{selectedLot?.id}</span> (
                {selectedLot?.expediteur.nom}) de{' '}
                <span className="font-semibold">"{selectedLot?.statut}"</span> à{' '}
                <span className="font-semibold">"{newStatus}"</span> ?
              </p>
            </div>
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Annuler
          </Button>
          <Button
            onClick={onConfirm}
            color="blue"
            disabled={isLoading}
            className="flex items-center"
          >
            {isLoading && (
              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
            )}
            Confirmer le changement
          </Button>
        </div>
      </div>
    </Modal>
  )
}
