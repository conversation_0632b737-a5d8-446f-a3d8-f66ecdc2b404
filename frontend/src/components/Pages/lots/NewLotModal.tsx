import { Button } from '@/components/Forms/Button'
import Modal from '@/components/Forms/Modal'
import { SearchSelector } from '@/components/Forms/SearchSelector'
import { Sender } from '@/models/sender'
import { SenderService } from '@/services/senderService'

import { TbInfoCircle, TbPlus } from 'react-icons/tb'

type Props = {
  isSelectSenderModalOpen: boolean
  closeModal: () => void
  selectedSender: Sender | undefined
  handleCreateLot: (senderId: number) => void
  setSelectedSender: (sender: Sender | undefined) => void
}
export const NewLotModal = ({
  isSelectSenderModalOpen,
  closeModal,
  selectedSender,
  handleCreateLot,
  setSelectedSender,
}: Props) => {
  return (
    <Modal
      title="Créer un nouveau lot expéditeur"
      isShown={isSelectSenderModalOpen}
      closeModal={closeModal}
    >
      <div className="space-y-4">
        <div className="rounded-lg bg-blue-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <TbInfoCircle className="text-blue-500" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Sélection d'expéditeur
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  Recherchez et sélectionnez un expéditeur pour créer un nouveau
                  lot. Un casier sera automatiquement attribué au lot.
                </p>
              </div>
            </div>
          </div>
        </div>

        <SearchSelector
          className="w-full"
          placeholder="Rechercher un expéditeur par nom, SIRET ou COCLICO..."
          selectedValue={selectedSender}
          setSelectedValue={(sender) => setSelectedSender(sender)}
          searchResultPromise={SenderService.getSenders}
          selectedItemLabel={(item: Sender) => item.nom}
          resultItem={(item: Sender) => (
            <div className="p-2">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-gray-900">
                      {item.nom}
                    </span>
                    {item.siret && (
                      <span className="inline-flex items-center rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800">
                        SIRET: {item.siret}
                      </span>
                    )}
                    {item.coclico && (
                      <span className="inline-flex items-center rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800">
                        COCLICO: {item.coclico}
                      </span>
                    )}
                  </div>
                  <div className="mt-1 text-sm text-gray-600">
                    {item.adresse && `${item.adresse}, `}
                    {item.code_postal} {item.ville}
                    {item.pays && `, ${item.pays}`}
                  </div>
                </div>
              </div>
            </div>
          )}
        />

        <div className="flex items-center justify-between pt-4">
          <Button variant="outline" onClick={closeModal}>
            Annuler
          </Button>
          <Button
            onClick={() => selectedSender && handleCreateLot(selectedSender.id)}
            disabled={!selectedSender}
            className="flex items-center"
          >
            <TbPlus className="mr-2 h-4 w-4" />
            Créer le lot
          </Button>
        </div>
      </div>
    </Modal>
  )
}
