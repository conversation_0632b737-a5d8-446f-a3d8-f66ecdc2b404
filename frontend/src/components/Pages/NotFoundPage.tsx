import { Link } from '@tanstack/react-router'

const NotFoundPage = () => {
  return (
    <div className="mx-auto flex h-screen max-w-sm flex-col items-center justify-center text-center">
      <h1 className="mb-4 text-8xl leading-none font-bold text-gray-800">
        404
      </h1>
      <p className="text-md">Oops!</p>
      <p className="text-md">Page not found.</p>
      <Link
        to="/"
        className="mt-4 border border-gray-800 px-4 py-2 text-gray-800 transition-colors hover:bg-gray-800 hover:text-white"
      >
        Go back
      </Link>
    </div>
  )
}

export default NotFoundPage
