import Dropdown from '@/components/DropDown'
import { RegleMetier } from '@/models/reglesMetier'

import { TbDotsVertical, TbPencil, TbTrash } from 'react-icons/tb'

interface RuleActionsMenuProps {
  rule: RegleMetier
  onEdit: (rule: RegleMetier) => void
  onDelete: (rule: RegleMetier) => void
  disabled?: boolean
}

const RuleActionsMenu: React.FC<RuleActionsMenuProps> = ({
  rule,
  onEdit,
  onDelete,
  disabled = false,
}) => (
  <Dropdown
    position="top-left"
    actions={[
      {
        label: 'Éditer',
        onClick: () => onEdit(rule),
        icon: <TbPencil className="h-4 w-4" />,
      },
      {
        label: 'Supprimer',
        onClick: () => onDelete(rule),
        icon: <TbTrash className="h-4 w-4" />,
      },
    ]}
    disabled={disabled}
  >
    <TbDotsVertical
      className="h-5 w-5 cursor-pointer text-gray-400"
      aria-hidden="true"
    />
  </Dropdown>
)

export default RuleActionsMenu
