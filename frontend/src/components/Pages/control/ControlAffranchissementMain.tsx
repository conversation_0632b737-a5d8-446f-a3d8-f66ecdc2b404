// src/components/ControlAffranchissementMain.tsx
import { useContext, useEffect, useState } from 'react'

import { Divider } from '@/components/Forms/Divider'
import { ControlContext } from '@/contexts/Control'

import { ControlAffranchissementModal } from './ControlAffranchissementModal'
import { EnveloppeManager } from './EnveloppeManager'
import { SectionCategorieAffranchissement } from './SectionCategorieAffranchissement'
import { EnveloppeAffranchissement } from '@/models/affranchissement'

import { usePhotos } from '@/hooks/usePhotos'
import { useAffranchissement } from '@/hooks/useAffranchissement'
import { CodeInputWithScanner } from './CodeInputWithScanner'
import { useDisclosure } from '@/hooks/useDisclosure'

import { PhotoManagerModal } from './PhotoManagerModal' // Import the new modal component
import { AffranchissementPanel } from './AffranchissementPanel'
import ControlScanner from './scandit/ControlScanner'
import toast from 'react-hot-toast'

export const ControlAffranchissementMain = () => {
  const { currentEnveloppe } = useContext(ControlContext)

  const {
    addAffranchissementMutation,
    getValidationInfo,
    findExistingCodeInAffranchissements,
  } = useAffranchissement()

  const {
    isOpen: isAffranchissementModalOpen,
    onOpen: onAffranchissementModalOpen,
    onClose: onAffranchissementModalClose,
  } = useDisclosure()

  const {
    isOpen: isPhotoModalOpen,
    onOpen: onPhotoModalOpen,
    onClose: onPhotoModalClose,
  } = useDisclosure()

  const [selectedAffranchissement, setSelectedAffranchissement] =
    useState<EnveloppeAffranchissement | null>(null)

  const [currentScrollOffset, setCurrentScrollOffset] = useState(0)
  const [scrollTop, setScrollTop] = useState(0)
  const [matches, setMatches] = useState(
    window.matchMedia('(min-width: 1024px)').matches
  )

  const { handleAddPhoto, handleDeletePhoto } = usePhotos({ currentEnveloppe })

  useEffect(() => {
    setCurrentScrollOffset(scrollTop + (matches ? 0 : 72))
  }, [matches, scrollTop])

  useEffect(() => {
    const mainContainer = document.getElementById('main-container')
    if (mainContainer) {
      const onScroll = (e: Event) =>
        setScrollTop((e.target as HTMLDivElement).scrollTop)
      mainContainer.addEventListener('scroll', onScroll)
      window
        .matchMedia('(min-width: 1024px)')
        .addEventListener('change', (e) => {
          setMatches(e.matches)
        })
      return () => {
        mainContainer.removeEventListener('scroll', onScroll)
        window
          .matchMedia('(min-width: 1024px)')
          .removeEventListener('change', (e) => {
            setMatches(e.matches)
          })
      }
    }
  }, [])

  const handleOpenUpdateModal = (
    affranchissement: EnveloppeAffranchissement
  ) => {
    setSelectedAffranchissement(affranchissement)
    onAffranchissementModalOpen()
  }

  const handleAffranchissementModalClose = () => {
    setSelectedAffranchissement(null)
    onAffranchissementModalClose()
  }

  const handleAddAffranchissementCode = async (code: string) => {
    if (!currentEnveloppe) return
    if (findExistingCodeInAffranchissements(code)) {
      toast.error('Ce code est déjà présent dans les affranchissements')
      return
    }
    const result = await addAffranchissementMutation.mutateAsync({
      body: { code },
    })

    if (
      result.affranchissement &&
      result.affranchissement.informations.champs_requis.length > 0
    ) {
      setSelectedAffranchissement(result.affranchissement)
      onAffranchissementModalOpen()
    } else {
      toast.success('Affranchissement ajouté avec succès')
    }
  }

  if (!currentEnveloppe) return <div>Chargement...</div>

  return (
    <>
      {/* Section for package information */}
      <EnveloppeManager />

      <Divider className="mt-5 mb-5" />

      {/* Affranchissement section */}
      <div
        className={`flex flex-row gap-5 ${
          !currentEnveloppe ? 'pointer-events-none opacity-10' : ''
        }`}
      >
        <div className="flex w-full max-w-[50vw] flex-col gap-5">
          {/* Code input and scanner */}
          <CodeInputWithScanner
            onSubmit={handleAddAffranchissementCode}
            placeholder="SmartData, S10, Timbre numérique"
          />

          <ControlScanner
            onAddAffranchissement={handleAddAffranchissementCode}
            getValidationInfo={getValidationInfo}
            onCapturePhoto={handleAddPhoto}
          />

          <SectionCategorieAffranchissement />
        </div>

        {/* Affranchissement list panel */}
        <AffranchissementPanel
          onPhotoModalOpen={onPhotoModalOpen}
          currentScrollOffset={currentScrollOffset}
          onOpenUpdateModal={handleOpenUpdateModal}
        />
      </div>

      {/* Modals */}
      {selectedAffranchissement && (
        <ControlAffranchissementModal
          initialAffranchissement={selectedAffranchissement}
          onClose={handleAffranchissementModalClose}
          isOpen={isAffranchissementModalOpen}
        />
      )}

      {/* Photo Manager Modal */}
      {isPhotoModalOpen && (
        <PhotoManagerModal
          photos={
            Array.isArray(currentEnveloppe?.photos)
              ? currentEnveloppe.photos.map((photo) => photo.public_url)
              : []
          }
          onClose={onPhotoModalClose}
          onDelete={async (photoUrl) => {
            await handleDeletePhoto(photoUrl)
            // If needed, add any logic here to update state or re-fetch data
          }}
        />
      )}
    </>
  )
}
