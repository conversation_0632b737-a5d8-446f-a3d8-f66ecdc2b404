// ControlAffranchissementModal.tsx
'use client'

import React, { useContext, useEffect } from 'react'
import { Tb<PERSON><PERSON><PERSON>, TbChevronLeft } from 'react-icons/tb'
import { Button } from '@/components/Forms/Button'
import { Divider } from '@/components/Forms/Divider'

import { Input } from '@/components/Forms/Input'
import { Select } from '@/components/Forms/Select'
import { ControlContext } from '@/contexts/Control'
import {
  EnveloppeAffranchissement
} from '@/models/affranchissement'
import {
  getAffranchissementLabel,
  getAffranchissementColors,
} from '@/utils/controlHelpers'
import { getChampsManquants } from '@/utils/affranchissementHelpers'
import { useAffranchissement } from '@/hooks/useAffranchissement'
import { useForm, Controller } from 'react-hook-form'
import { Modal } from '@/components/Modal'

interface ControlAffranchissementModalProps {
  initialAffranchissement: EnveloppeAffranchissement
  onClose: () => void
  isOpen: boolean
}

export const ControlAffranchissementModal: React.FC<
  ControlAffranchissementModalProps
> = ({ initialAffranchissement, onClose, isOpen }) => {
  const { naturesData } = useContext(ControlContext)
  const { handleAddAffranchissement, handleUpdateAffranchissement } =
    useAffranchissement()
  
  // Transformation du modèle si nécessaire.
  const defaultAff: EnveloppeAffranchissement = initialAffranchissement

  const { informations } = defaultAff;
  const requiredFields = informations?.champs_requis || [];

  // Initialisation de react-hook-form à partir des valeurs par défaut
  const {
    register,
    handleSubmit,
    control,
    setValue,
    watch,
    formState: { errors },
  } = useForm<EnveloppeAffranchissement>({
    defaultValues: defaultAff,
  })
  
  // Observer la valeur actuelle de nature
  const watchedNature = watch('nature');
  
  const hasPrixUniteDevise = requiredFields.includes('prix_unite_devise');
  const hasNatureAndIsIndetermine = requiredFields.includes('nature') && watchedNature === 'NON_DETERMINE';

  const isDisabledValueInput = !(hasPrixUniteDevise || hasNatureAndIsIndetermine);

  // Effet pour mettre le focus sur le champ de valeur quand la modale s'ouvre
  useEffect(() => {
    if (isOpen && !isDisabledValueInput) {
      // Délai plus long pour s'assurer que la modale est complètement rendue
      setTimeout(() => {
        const inputElement = document.querySelector('input[name="prix_unite_devise"]') as HTMLInputElement;
        if (inputElement) {
          inputElement.focus();
        }
      }, 300)
    }
  }, [isOpen, isDisabledValueInput])

  // On observe les valeurs du formulaire afin de recalculer les champs manquants.
  const watchedData = watch()
  const champsManquants = getChampsManquants(watchedData)

  // Gestion de la soumission : si l'id est null, on ajoute ; sinon, on met à jour.
  const onSubmit = async (data: EnveloppeAffranchissement) => {
    try {
      if (data.id === null || data.id === undefined) {
        await handleAddAffranchissement(data)
      } else {
        await handleUpdateAffranchissement(data)
      }

      onClose() // Ne ferme la modale qu'après succès.
    } catch (error) {
      console.error('Erreur lors de la soumission du formulaire:', error)
      // Vous pouvez ajouter ici une gestion spécifique de l'erreur.
    }
  }

  // Si le type est CODE, désactive la modification de la quantité.
  const disabledChangeQuantity = watchedData.type === 'CODE'

  // Supprimer l'effet qui tente de récupérer la référence du champ après le rendu
  // car il n'est pas nécessaire avec l'approche ci-dessus

  return (
    <Modal
      open={isOpen}
      onClose={onClose}
      title={
        <div className="flex flex-row py-1">
          <div className="flex flex-grow">
            <div>
              <span
                className={
                  'rounded-lg px-2 py-1 ' +
                  getAffranchissementColors(defaultAff.categorie).text +
                  ' ' +
                  getAffranchissementColors(defaultAff.categorie).bg
                }
              >
                {defaultAff.categorie}
              </span>{' '}
              - {getAffranchissementLabel(defaultAff)}
            </div>
          </div>
          {defaultAff.code && (
            <div className="flex items-center justify-center">
              <div className="rounded-lg bg-gray-50 px-2 py-1 text-sm text-gray-600">
                {defaultAff.code}
              </div>
            </div>
          )}
        </div>
      }
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        {/* Affichage en lecture seule des données si disponibles */}
        {defaultAff.donnees?.content && (
          <>
            <div className="mt-2 flex flex-col">
              <label className="mb-2 text-lg">Informations :</label>
              <div className="mb-2 grid grid-cols-3 gap-2">
                {Object.entries(defaultAff.donnees.content).map(
                  ([key, value], index) =>
                    value ? (
                      <div
                        key={index}
                        className="flex flex-col rounded-lg bg-blue-50 p-2 text-sm"
                      >
                        <div className="flex-grow text-xs text-gray-600">
                          {key}
                        </div>
                        <div className="font-semibold text-blue-600">
                          {value as string}
                        </div>
                      </div>
                    ) : null
                )}
              </div>
            </div>
            <Divider className="mt-3 mb-2" />
          </>
        )}

        {((defaultAff as EnveloppeAffranchissement)?.verifications?.length ??
          0) > 0 && (
          <div className="mt-4 flex flex-col">
            <label className="mb-2 text-lg">Vérifications :</label>
            <div className="mt-2 mb-4 flex flex-col gap-2">
              {(defaultAff as EnveloppeAffranchissement)?.verifications.map(
                (verification) => (
                  <div key={verification.id} className="flex flex-row">
                    <div className="flex-grow text-sm text-gray-600 italic">
                      {verification.message}
                    </div>
                    <div
                      className={`rounded-lg px-2 py-0.5 text-sm ${
                        verification.statut === 'NON_DETERMINE'
                          ? 'bg-gray-50 text-gray-500'
                          : verification.statut === 'VALIDE'
                            ? 'bg-green-50 text-green-500'
                            : 'bg-red-50 text-red-500'
                      }`}
                    >
                      {verification.statut === 'VALIDE' && 'Valide'}
                      {verification.statut === 'INVALIDE' && 'Invalide'}
                      {verification.statut === 'NON_DETERMINE' &&
                        'Non déterminé'}
                    </div>
                  </div>
                )
              )}
            </div>
            <Divider className="mt-0 mb-2" />
          </div>
        )}

        {/* Sélection de la nature si nécessaire */}
        {defaultAff.informations.champs_requis.includes('nature') && (
          <>
            <label className="mt-2 mb-2 text-lg">
              Nature<span className="text-red-600">&nbsp;*</span>
            </label>
            <Controller
              control={control}
              name="nature"
              rules={{ required: true }}
              render={({ field }) => (
                <Select
                  className="mb-4"
                  options={(naturesData || []).map((nature) => ({
                    value: nature.id,
                    label: nature.lib,
                  }))}
                  value={
                    (naturesData || [])
                      .map((nature) => ({
                        value: nature.id,
                        label: nature.lib,
                      }))
                      .find((opt) => opt.value === field.value) || null
                  }
                  onChange={(option) => {
                    field.onChange(option.value);
                    // Trouver la nature sélectionnée dans naturesData
                    const selectedNature = naturesData?.find(nature => nature.id === option.value);
                    // Si la nature a un prix_unite, mettre à jour le prix_unite_devise
                    if (selectedNature?.prix_unite) {
                      setValue('prix_unite_devise', selectedNature.prix_unite);
                      setValue('devise', 'EURO'); // Définir la devise en EURO
                    }
                  }}
                />
              )}
            />
          </>
        )}

        <div className="mt-2 flex flex-row gap-3">
          <div className="flex flex-grow flex-col">
            <label className="text-lg">
              Valeur<span className="text-red-600">&nbsp;*</span>
            </label>
            <Input
              className="w-full"
              type="number"
              placeholder="Valeur"
              autoComplete="off"
              disabled={isDisabledValueInput}
              step="0.01"
              {...register('prix_unite_devise', {
                required: true,
                min: 0,
                valueAsNumber: true,
              })}
            />

            {errors.prix_unite_devise && (
              <span className="text-xs text-red-500">Valeur invalide</span>
            )}
          </div>

          <div className="flex flex-grow flex-col">
            <label className="text-lg">
              Quantité<span className="text-red-600">&nbsp;*</span>
            </label>
            <Input
              className="w-full"
              type="number"
              placeholder="Quantité"
              autoComplete="off"
              {...register('quantite', { required: true, min: 1 })}
              disabled={disabledChangeQuantity}
            />
            {errors.quantite && (
              <span className="text-xs text-red-500">Quantité invalide</span>
            )}
          </div>
        </div>

        <label className="mt-6 mb-2 text-lg">
          Décision finale (modifiable par l'utilisateur)
        </label>
        <div className="flex flex-row gap-3">
          <Button
            variant={watchedData.statut === 'VALIDE' ? 'light' : 'outline'}
            color="green"
            className="min-h-16 flex-grow"
            type="button"
            onClick={() => setValue('statut', 'VALIDE')}
          >
            Vrai
          </Button>
          <Button
            variant={watchedData.statut === 'INVALIDE' ? 'light' : 'outline'}
            color="red"
            className="min-h-16 flex-grow"
            type="button"
            onClick={() => setValue('statut', 'INVALIDE')}
          >
            Faux
          </Button>
        </div>

        <div className="mt-6 flex flex-row items-center justify-end">
          <Button
            variant="outline"
            type="button"
            onClick={onClose}
            className="mr-3 flex flex-row items-center !pl-1"
          >
            <TbChevronLeft className="mr-2 h-5 w-5" />
            Annuler
          </Button>
          <Button
            variant="primary"
            type="submit"
            className="flex flex-row items-center !pl-2"
            disabled={champsManquants.length > 0}
            onClick={() => console.log('Tentative de validation - Champs manquants:', champsManquants)}
          >
            <TbCheck className="mr-2 ml-1 h-5 w-5" />
            Valider {champsManquants.length > 0 ? `(${champsManquants.join(', ')})` : ''}
          </Button>
        </div>
      </form>
    </Modal>
  )
}
