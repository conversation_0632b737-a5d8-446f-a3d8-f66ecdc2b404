import React, { useContext } from 'react'
import { TbC<PERSON>ck, TbTrash } from 'react-icons/tb'
import { Button } from '@/components/Forms/Button'
import { EnveloppeAffranchissement } from '@/models/affranchissement'
import { AffranchissementEnveloppeItem } from './EnveloppeAffranchissementItem'
import { EnveloppeService } from '@/services/enveloppeService'
import { useQueryClient } from '@tanstack/react-query'
import { useAffranchissement } from '@/hooks/useAffranchissement'
import { ControlContext } from '@/contexts/Control'

interface AffranchissementPanelProps {
  onPhotoModalOpen: () => void

  currentScrollOffset: number
  onOpenUpdateModal: (affranchissement: EnveloppeAffranchissement) => void
}

export const AffranchissementPanel: React.FC<AffranchissementPanelProps> = ({
  onPhotoModalOpen,
  currentScrollOffset,
  onOpenUpdateModal,
}) => {
  const queryClient = useQueryClient()
  const { handleChangeQuantity, handleChangeLegitStatus } =
    useAffranchissement()

  const {
    enveloppeKey,
    currentEnveloppe,
    handleTerminerEnveloppe,
    isOverSized,
    isOverWeight,
    selectedDestination,
    weight,
  } = useContext(ControlContext)

  // Calculate totals - moved inside the component
  const totalAffranchissements =
    currentEnveloppe?.affranchissements?.reduce(
      (acc: any, affranchissement: { quantite: any }) =>
        acc + affranchissement.quantite,
      0
    ) || 0

  const totalPrice =
    currentEnveloppe?.informations.prix_affranchissements_valide.toFixed(2)

  // Function moved inside the component
  const handleDeleteAllAffranchissements = async () => {
    if (currentEnveloppe?.id) {
      const response = await EnveloppeService.deleteAllAffranchissements(
        currentEnveloppe.id
      )
      queryClient.setQueryData(['enveloppe', enveloppeKey], response.enveloppe)
    }
  }

  return (
    <div
      className="transition-height sticky top-4 mb-4 flex-grow flex-col duration-300"
      style={{
        height: `calc(90svh - 2rem - 92px - 70px + ${currentScrollOffset}px)`,
        maxHeight: `calc(90svh )`,
        minHeight: `350px`,
      }}
    >
      <div
        className="flex w-full flex-grow flex-col rounded-lg border border-gray-200"
        style={{
          height: `calc(100% - 54px - 54px)`,
        }}
      >
        {/* List of affranchissements */}
        <div className="flex flex-grow flex-col overflow-y-auto select-none">
          {currentEnveloppe?.affranchissements
            ?.sort(
              (a: EnveloppeAffranchissement, b: EnveloppeAffranchissement) =>
                (a.id || 0) - (b.id || 0)
            )
            .map(
              (affranchissement: EnveloppeAffranchissement, index: number) => (
                <AffranchissementEnveloppeItem
                  key={`selected-affranchissement-${index}`}
                  affranchissement={affranchissement}
                  onOpenUpdateModal={onOpenUpdateModal}
                  onChangeQuantity={handleChangeQuantity}
                  onChangeLegitStatus={handleChangeLegitStatus}
                />
              )
            )}
        </div>

        {/* Totals & delete action */}
        <div className="flex min-h-12 w-full flex-row items-center border-t border-gray-200 px-3 py-1.5 text-sm text-gray-600">
          <div className="flex flex-grow flex-col">
            <span>
              <b>{totalAffranchissements}</b> Élément(s) d'affranchissement
            </span>
            <span className="text-gray-400">
              Total : <b>{totalPrice}€</b>
            </span>
          </div>
          <TbTrash
            className="ml-2 h-6 w-6 cursor-pointer text-gray-300 transition-all duration-150 hover:text-red-700"
            onClick={handleDeleteAllAffranchissements}
          />
        </div>
      </div>

      {/* Action buttons */}
      <Button
        variant="light"
        color={
          (currentEnveloppe?.photos?.length ?? 0) >= 3 ? 'green' : 'yellow'
        }
        className="mt-3 flex w-full flex-row justify-start !px-3 text-sm"
        onClick={onPhotoModalOpen}
      >
        <span>
          <b>{currentEnveloppe?.photos?.length ?? 0} / 1</b> Photos
        </span>
      </Button>
      <div className="mt-3 flex flex-row">
        {currentEnveloppe?.informations?.modifiable && (
          <Button
            variant="primary"
            className="flex flex-grow flex-row items-center justify-center pl-2"
            disabled={
              !currentEnveloppe?.informations.complet ||
              ((isOverSized || isOverWeight) && !selectedDestination) ||
              (weight != undefined && weight < 1) ||
              !weight
            }
            onClick={() => {
              if (currentEnveloppe?.informations.complet) {
                handleTerminerEnveloppe()
              }
            }}
          >
            <TbCheck className="mr-2 h-5 w-5" />
            Valider
          </Button>
        )}
      </div>
    </div>
  )
}
