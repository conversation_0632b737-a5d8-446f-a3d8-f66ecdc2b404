import { useEffect, useRef } from 'react'
import {
  DataCaptureContext,
  Camera,
  DataCaptureView,
  FrameSourceState,
  CameraSwitchControl,
  configure,
} from '@scandit/web-datacapture-core'

import {
  BarcodeBatch,
  BarcodeBatchSettings,
  Symbology,
  barcodeCaptureLoader,
} from '@scandit/web-datacapture-barcode'
import { ValidationResponse } from '@/models/affranchissement'

interface ScannerProps {
  /** Callback appelé quand un code-barres est détecté */
  onDetected: (data: string) => void

  /** Utiliser le mode batch (MatrixScan) ou non */
  batchMode?: boolean
  onCapturePhoto?: (imageDataUrl: string) => void

  getValidationInfo?: (scannedCode: string) => ValidationResponse | null
}

export default function ClassicScanner({ onDetected }: ScannerProps) {
  const scannerContainerRef = useRef<HTMLDivElement>(null)
  const contextRef = useRef<DataCaptureContext | null>(null)
  const captureModeRef = useRef<BarcodeBatch | null>(null)

  const stopScanner = () => {
    const context = contextRef.current
    if (context) {
      const camera = context.frameSource as Camera
      camera?.switchToDesiredState(FrameSourceState.Off)
      context.dispose()
      contextRef.current = null
      captureModeRef.current = null
    }
  }

  const onDetectedRef = useRef(onDetected)

  useEffect(() => {
    let isCancelled = false

    const initializeScanner = async () => {
      try {
        if (!scannerContainerRef.current) return

        await configure({
          licenseKey: import.meta.env.VITE_PUBLIC_SCANDIT_API_KEY!,
          moduleLoaders: [barcodeCaptureLoader()],
          libraryLocation:
            'https://cdn.jsdelivr.net/npm/@scandit/web-datacapture-barcode@7.1/sdc-lib',
        })
        if (isCancelled) return

        // Création du contexte de capture
        const context = await DataCaptureContext.create()
        if (isCancelled) {
          context.dispose()
          return
        }
        contextRef.current = context

        // Initialisation de la caméra
        const camera = Camera.default
        await context.setFrameSource(camera)
        if (isCancelled) return

        // Choix des paramètres caméra en fonction du mode
        const cameraSettings = BarcodeBatch.recommendedCameraSettings // Mode MatrixScan

        if (cameraSettings) {
          await camera.applySettings(cameraSettings)
        }

        // Préparation de la vue de capture pour afficher l'aperçu de la caméra
        const view = await DataCaptureView.forContext(context)
        view.connectToElement(scannerContainerRef.current)
        view.addControl(new CameraSwitchControl())

        // --- Mode BATCH (MatrixScan) avec overlay avancé ---
        const batchSettings = new BarcodeBatchSettings()
        batchSettings.enableSymbologies([
          Symbology.Code128,
          Symbology.Code39,
          Symbology.QR,
          Symbology.EAN8,
          Symbology.UPCE,
          Symbology.EAN13UPCA,
          Symbology.DataMatrix,
        ])

        const barcodeBatch = await BarcodeBatch.forContext(
          context,
          batchSettings
        )
        captureModeRef.current = barcodeBatch

        // Écouter la détection des codes-barres et appeler le callback.
        barcodeBatch.addListener({
          didUpdateSession: (_barcodeBatch, session) => {
            const newBarcodes = session.addedTrackedBarcodes ?? []
            if (newBarcodes.length) {
              newBarcodes.forEach((tracked) => {
                const data = tracked.barcode.data ?? ''
                if (data) {
                  onDetectedRef.current(data)
                }
              })
            }
          },
        })

        await camera.switchToDesiredState(FrameSourceState.On)
      } catch (err: any) {
        console.error(err)
        alert(err.message || 'Une erreur est survenue lors du scan')
      }
    }

    initializeScanner()

    return () => {
      isCancelled = true
      stopScanner()
    }
  }, [])

  return (
    <div className="relative">
      {/* The Scandit scanner container */}
      <div
        ref={scannerContainerRef}
        className="relative aspect-video w-full overflow-hidden rounded-lg border border-gray-200 bg-gray-100"
      />
    </div>
  )
}
