import { useCallback, useEffect, useRef, useState } from 'react'
import {
  DataCaptureContext,
  Camera,
  DataCaptureView,
  FrameSourceState,
  CameraSwitchControl,
  configure,
  Anchor,
  Color,
  Brush,
  ScanditIconBuilder,
  ScanditIconType,
  ScanditIconShape,
  FrameData,
} from '@scandit/web-datacapture-core'
import {
  type Barcode,
  BarcodeBatch,
  Symbology,
  barcodeCaptureLoader,
  BarcodeCheckSettings,
  BarcodeCheck,
  BarcodeCheckView,
  BarcodeCheckHighlight,
  BarcodeCheckCircleHighlight,
  BarcodeCheckCircleHighlightPreset,
  BarcodeCheckAnnotation,
  BarcodeCheckInfoAnnotationAnchor,
  BarcodeCheckInfoAnnotation,
  BarcodeCheckInfoAnnotationHeader,
  BarcodeCheckInfoAnnotationBodyComponent,
} from '@scandit/web-datacapture-barcode'
import { ValidationResponse } from '@/models/affranchissement'
import { TbCamera } from 'react-icons/tb'
import { CgSpinner } from 'react-icons/cg'
import { formatCodeWithEllipsis } from '@/utils/textUtils'

interface ControlScannerProps {
  /** Callback appelé quand un code-barres est détecté */
  onAddAffranchissement: (data: string) => Promise<void>
  onCapturePhoto?: (imageDataUrl: string) => void
  getValidationInfo?: (
    scannedCode: string
  ) => Promise<ValidationResponse | null>
  isFullscreen?: boolean // New prop
}

export default function ControlScanner({
  onAddAffranchissement,
  getValidationInfo,
  onCapturePhoto,
  isFullscreen = false,
}: ControlScannerProps) {
  const scannerContainerRef = useRef<HTMLDivElement>(null)
  const contextRef = useRef<DataCaptureContext | null>(null)
  const captureModeRef = useRef<BarcodeBatch | null>(null)
  const latestFrameDataRef = useRef<FrameData | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [photoLoading, setPhotoLoading] = useState(false)

  const stopScanner = () => {
    const context = contextRef.current
    if (context) {
      const camera = context.frameSource as Camera
      camera?.switchToDesiredState(FrameSourceState.Off)
      context.dispose()
      contextRef.current = null
      captureModeRef.current = null
      latestFrameDataRef.current = null
    }
  }

  const onAddAffranchisssementRef = useRef(onAddAffranchissement)

  useEffect(() => {
    let isCancelled = false

    const initializeScanner = async () => {
      try {
        if (!scannerContainerRef.current) return

        await configure({
          licenseKey: import.meta.env.VITE_PUBLIC_SCANDIT_API_KEY!,
          moduleLoaders: [
            barcodeCaptureLoader({ highEndBlurryRecognition: false }),
          ],
          libraryLocation:
            'https://cdn.jsdelivr.net/npm/@scandit/web-datacapture-barcode@7.1/sdc-lib',
        })

        if (isCancelled) return

        const view: DataCaptureView = new DataCaptureView()
        view.connectToElement(scannerContainerRef.current)
        view.logoAnchor = Anchor.BottomRight

        // Create the data capture context.
        const context: DataCaptureContext = await DataCaptureContext.create()
        if (isCancelled) {
          context.dispose()
          return
        }
        contextRef.current = context

        const settings: BarcodeCheckSettings = new BarcodeCheckSettings()
        const enabledSymbologies = [
          Symbology.Code128,
          Symbology.Code39,
          Symbology.QR,
          Symbology.EAN8,
          Symbology.UPCE,
          Symbology.EAN13UPCA,
          Symbology.DataMatrix,
        ]
        const enabledSymbologiesSet = new Set(enabledSymbologies)
        settings.enableSymbologies(enabledSymbologies)

        // Create a new barcode tracking mode with the settings from above.
        const barcodeCheck = await BarcodeCheck.forContext(context, settings)
        view.addControl(new CameraSwitchControl())

        // Create the view
        const barcodeCheckView = await BarcodeCheckView.create(
          view,
          context,
          barcodeCheck
        )

        // Store the latest frame data for photo capture
        barcodeCheck.addListener({
          didUpdateSession: (_barcodeCheck, _session, frameData) => {
            // Store the latest frame data for later use
            if (frameData) {
              latestFrameDataRef.current = frameData
            }
          },
        })

        // Create the highlight provider and configure an highlight for the barcode
        barcodeCheckView.highlightProvider = {
          async highlightForBarcode(
            barcode: Barcode,
            callback: (highlight: BarcodeCheckHighlight) => void
          ): Promise<void> {
            if (enabledSymbologiesSet.has(barcode.symbology)) {
              const highlight = BarcodeCheckCircleHighlight.create(
                barcode,
                BarcodeCheckCircleHighlightPreset.Dot
              )
              const white = Color.fromRGBA(255, 255, 143)
              highlight.brush = new Brush(white, white, 0)
              callback(highlight)
            }
          },
        }

        function createListenerFromOriginalText(code: string) {
          return {
            onInfoAnnotationTapped: (
              annotation: BarcodeCheckInfoAnnotation
            ) => {
              annotation.body[0].text = 'Ajout en cours'
              onAddAffranchisssementRef.current(code).then(() => {
                annotation.body[0].text = code
              })
            },
          }
        }

        barcodeCheckView.annotationProvider = {
          async annotationForBarcode(
            barcode: Barcode,
            callback: (highlight: BarcodeCheckAnnotation) => void
          ): Promise<void> {
            if (getValidationInfo && barcode.data) {
              try {
                // Get validation info
                const infoAffranchissement = await getValidationInfo(
                  barcode.data
                )
                // Create body components - one for each verification
                const bodyComponents: BarcodeCheckInfoAnnotationBodyComponent[] =
                  []
                const header = BarcodeCheckInfoAnnotationHeader.create()
                header.textColor = Color.fromRGBA(255, 255, 255)

                if (
                  infoAffranchissement &&
                  infoAffranchissement.affranchissement
                ) {
                  header.backgroundColor = infoAffranchissement?.is_valid
                    ? Color.fromRGBA(0, 170, 0) // Green for valid
                    : Color.fromRGBA(255, 0, 0) // Red for invalid

                  // Set the header text to show the code
                  header.text = infoAffranchissement.is_valid
                    ? 'VALIDE'
                    : 'INVALIDE'

                  const codeComponent =
                    BarcodeCheckInfoAnnotationBodyComponent.create()
                  codeComponent.text = `${
                    formatCodeWithEllipsis(
                      infoAffranchissement.affranchissement.code,
                      15
                    ) || 'Non spécifié'
                  }`
                  codeComponent.textColor = Color.fromRGBA(100, 100, 100)
                  codeComponent.textAlignment = 'center'
                  const iconAdd = await new ScanditIconBuilder()
                    .withIcon(ScanditIconType.ChevronRight)
                    .withBackgroundColor(Color.fromRGBA(82, 163, 255))
                    .withBackgroundShape(ScanditIconShape.Circle)
                    .build()
                  codeComponent.rightIcon = iconAdd
                  bodyComponents.push(codeComponent)

                  // Add verification components
                  if (
                    infoAffranchissement.affranchissement.verifications &&
                    infoAffranchissement.affranchissement.verifications.length >
                      0
                  ) {
                    infoAffranchissement.affranchissement.verifications
                      .filter((v) => v.statut === 'INVALIDE')
                      .forEach((verification) => {
                        const verificationComponent =
                          BarcodeCheckInfoAnnotationBodyComponent.create()
                        verificationComponent.text = verification.message
                        verificationComponent.textColor =
                          verification.statut === 'VALIDE'
                            ? Color.fromRGBA(0, 255, 0)
                            : Color.fromRGBA(255, 0, 0)
                        verificationComponent.textAlignment = 'center'
                        bodyComponents.push(verificationComponent)
                      })
                  }
                } else {
                  // If no affranchissement data is available
                  const noDataComponent =
                    BarcodeCheckInfoAnnotationBodyComponent.create()
                  noDataComponent.text = 'Aucune information disponible'
                  noDataComponent.textColor = Color.fromRGBA(100, 100, 100) // Gray
                  header.text = 'Aucune information disponible'
                  bodyComponents.push(noDataComponent)
                }

                // Create the annotation
                const infoAnnotation =
                  BarcodeCheckInfoAnnotation.create(barcode)
                infoAnnotation.isEntireAnnotationTappable = true
                infoAnnotation.anchor = BarcodeCheckInfoAnnotationAnchor.Bottom
                infoAnnotation.body = bodyComponents
                infoAnnotation.header = header
                infoAnnotation.hasTip = true // Add a tip/arrow pointing to the barcode
                infoAnnotation.listener = createListenerFromOriginalText(
                  barcode.data
                )
                callback(infoAnnotation)
              } catch (error) {
                console.error('Error creating barcode annotation:', error)
                // Create a simple error annotation
                const header = BarcodeCheckInfoAnnotationHeader.create()
                header.text = 'Erreur'
                header.backgroundColor = Color.fromRGBA(255, 0, 0) // Red
                const body = BarcodeCheckInfoAnnotationBodyComponent.create()
                body.text = 'Erreur lors de la vérification du code'
                const infoAnnotation =
                  BarcodeCheckInfoAnnotation.create(barcode)
                infoAnnotation.isEntireAnnotationTappable = true
                infoAnnotation.anchor = BarcodeCheckInfoAnnotationAnchor.Bottom
                infoAnnotation.body = [body]
                infoAnnotation.header = header
                callback(infoAnnotation)
              }
            }
          },
        }

        await barcodeCheckView.start()
        setIsInitialized(true)
      } catch (err: any) {
        console.error(err)
        alert(err.message || 'Une erreur est survenue lors du scan')
      }
    }

    initializeScanner()

    return () => {
      isCancelled = true
      stopScanner()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleCapturePhoto = useCallback(async () => {
    if (!onCapturePhoto) return

    setPhotoLoading(true)

    try {
      // First, try to capture using the frame data from getData()
      if (latestFrameDataRef.current) {
        try {
          const frameData = await latestFrameDataRef.current.getData()
          const { width, height } = latestFrameDataRef.current
          if (frameData) {
            const canvas = document.createElement('canvas')
            canvas.width = width
            canvas.height = height
            const ctx = canvas.getContext('2d')

            if (ctx) {
              // Check if the frame data is in full RGBA format...
              if (frameData.length === width * height * 4) {
                // Use directly if already RGBA.
                const imageData = new ImageData(frameData, width, height)
                ctx.putImageData(imageData, 0, 0)
              }
              // ...otherwise, if it's grayscale, convert it to RGBA.
              else if (frameData.length === width * height) {
                const rgbaData = new Uint8ClampedArray(width * height * 4)
                for (let i = 0; i < frameData.length; i++) {
                  const gray = frameData[i]
                  rgbaData[i * 4] = gray // Red
                  rgbaData[i * 4 + 1] = gray // Green
                  rgbaData[i * 4 + 2] = gray // Blue
                  rgbaData[i * 4 + 3] = 255 // Alpha
                }
                const imageData = new ImageData(rgbaData, width, height)
                ctx.putImageData(imageData, 0, 0)
              } else {
                console.error(
                  `Unexpected frame data length: ${frameData.length} (expected either ${width * height} or ${width * height * 4})`
                )
                throw new Error('Frame data has unexpected size.')
              }

              const imageDataUrl = canvas.toDataURL('image/jpeg', 0.9)
              await onCapturePhoto(imageDataUrl)
              setPhotoLoading(false)
              return
            }
          }
        } catch (frameError) {
          console.error('Error processing frame data:', frameError)
          // Fall through to try using toBlob() below.
        }
      }

      // Final fallback to capture the scanner view using html2canvas.
      if (scannerContainerRef.current) {
        const html2canvas = (await import('html2canvas')).default
        const canvas = await html2canvas(scannerContainerRef.current, {
          useCORS: true,
        })
        const imageDataUrl = canvas.toDataURL('image/jpeg')
        await onCapturePhoto(imageDataUrl)
        setPhotoLoading(false)
      }
    } catch (error) {
      console.error('Error capturing photo:', error)
      setPhotoLoading(false)
    }
  }, [onCapturePhoto])

  return (
    <div className={`relative ${isFullscreen ? 'h-full' : ''}`}>
      {/* The Scandit scanner container */}
      <div
        ref={scannerContainerRef}
        className={`relative ${
          isFullscreen ? 'h-full' : 'aspect-video'
        } w-full overflow-hidden rounded-lg border border-gray-200 bg-gray-100`}
      />

      {/* Overlay a button for taking a photo */}
      {isInitialized && onCapturePhoto && (
        <button
          onClick={handleCapturePhoto}
          className={`absolute ${
            isFullscreen
              ? 'right-6 bottom-6'
              : 'top-2 right-2 sm:top-6 sm:right-6'
          } z-30 flex cursor-pointer items-center gap-2 rounded-full bg-white/80 px-4 py-3 text-sm font-medium text-blue-800 shadow-lg backdrop-blur-sm transition hover:bg-white/90 disabled:opacity-70`}
          title="Capturer une photo"
          disabled={photoLoading}
        >
          {photoLoading ? (
            <>
              <CgSpinner className="h-6 w-6 animate-spin" />
              <span className="hidden sm:block">Traitement...</span>
            </>
          ) : (
            <>
              <TbCamera className="h-6 w-6" />
              <span className="hidden sm:block">Prendre une photo</span>
            </>
          )}
        </button>
      )}
    </div>
  )
}
