import { useCallback, useEffect, useRef, useState } from 'react'
import Webcam from 'react-webcam'

import { Button } from '@/components/Forms/Button'
import { TbX } from 'react-icons/tb'

type WebcamViewProps = {
  alreadyUploadedImages?: string[]
  submit: (images: string[], publicUrlsToDelete: string[]) => Promise<void>
  onClose: () => void
}

const videoConstraints = {
  width: 1920,
  height: 1080,
}

export const WebcamView = ({
  alreadyUploadedImages,
  submit,
  onClose,
}: WebcamViewProps) => {
  const webcamRef = useRef(null)
  const [currentImage, setCurrentImage] = useState<string | null>(null)
  const [images, setImages] = useState<{ public_url: string; isUploaded: boolean }[]>(
    []
  )
  const [showPreview, setShowPreview] = useState(false)
  const [publicUrlsToDelete, setPublicUrlsToDelete] = useState<string[]>([])

  const capture = useCallback(() => {
    if (webcamRef.current) {
      const imageSrc = (webcamRef.current as any).getScreenshot()
      setCurrentImage(imageSrc)
      setImages([...images, { public_url: imageSrc, isUploaded: false }])
      setShowPreview(true)
    }
  }, [webcamRef, images])

  const handleDeleteImage = (index: number) => {
    setPublicUrlsToDelete([...publicUrlsToDelete, images[index].public_url])
    setImages(images.filter((_, i) => i !== index))
    if (images.filter((_, i) => i !== index).length > 0) {
      setCurrentImage(images[0].public_url)
    } else {
      setCurrentImage(null)
      setShowPreview(false)
    }
  }

  const handleSubmit = () => {
    submit(
      images
        .filter((image) => image.isUploaded === false)
        .map((image) => image.public_url),
      publicUrlsToDelete
    )
  }

  useEffect(() => {
    setImages(
      alreadyUploadedImages?.map((image) => ({
        public_url: image,
        isUploaded: true,
      })) ?? []
    )
  }, [alreadyUploadedImages])

  return (
    <div className="fixed top-0 left-0 z-20 flex h-screen w-screen flex-col items-center justify-center overflow-hidden bg-black">
      {showPreview && currentImage ? (
        <>
          <img
            src={currentImage}
            alt="Captured"
            className="max-h-full w-full object-cover"
          />
          <div className="absolute bottom-0 left-0 flex h-40 w-full items-center justify-center bg-black/50 px-6">
            <div className="mr-6 flex flex-grow flex-row items-center gap-4 overflow-x-auto py-8">
              {images.map((image, index) => (
                <div className="relative ml-2 rounded-xl bg-black">
                  <img
                    onClick={() => setCurrentImage(image.public_url)}
                    key={`image-${index}`}
                    src={image.public_url}
                    alt="Captured"
                    className={`h-28 min-w-28 cursor-pointer rounded-lg object-cover ${
                      currentImage === image.public_url
                        ? 'opacity-100 outline-2 outline-offset-3 outline-white/85'
                        : 'opacity-75 hover:opacity-100'
                    }`}
                  />
                  <Button
                    variant="primary"
                    color="red"
                    onClick={() => handleDeleteImage(index)}
                    className="absolute -top-3 -right-3 z-40 flex h-6 !min-h-6 w-6 items-center justify-center !rounded-full !p-0"
                  >
                    <TbX className="h-4 w-4 text-white" />
                  </Button>
                </div>
              ))}
            </div>
            <div className="flex flex-col">
              <Button
                variant="light"
                onClick={() => setShowPreview(false)}
                className="rounded-lg px-8 py-2 text-lg"
              >
                Prendre une photo
              </Button>
              <div className="mt-3 flex flex-row gap-3">
                <Button
                  variant="light"
                  color="gray"
                  onClick={() => onClose()}
                  className="rounded-lg px-8 py-2 text-lg"
                >
                  Annuler
                </Button>
                <Button
                  variant="primary"
                  disabled={images.length === 0 && publicUrlsToDelete.length === 0}
                  onClick={handleSubmit}
                  className="rounded-lg px-8 py-2 text-lg"
                >
                  Valider
                </Button>
              </div>
            </div>
          </div>
        </>
      ) : (
        <>
          <Webcam
            audio={false}
            height={1080}
            ref={webcamRef}
            screenshotFormat="image/jpeg"
            width={1920}
            videoConstraints={videoConstraints}
            className="w-full"
          />
          <div className="absolute bottom-0 left-0 flex h-40 w-full items-center justify-center bg-black/50 px-6">
            <div className="relative mr-6 flex h-full w-[calc(50%-40px)] flex-grow flex-row items-center justify-start">
              {images[images.length - 1] && (
                <div className="relative">
                  <img
                    onClick={() => {
                      setCurrentImage(images[images.length - 1].public_url)
                      setShowPreview(true)
                    }}
                    src={images[images.length - 1].public_url}
                    alt="Captured"
                    className="h-28 cursor-pointer rounded-lg object-cover opacity-75 hover:opacity-100"
                  />

                  <Button
                    variant="primary"
                    color="red"
                    className="absolute -top-3 -right-3 z-40 flex h-6 !min-h-6 w-6 items-center justify-center !rounded-full !p-0"
                  >
                    {images.length}
                  </Button>
                </div>
              )}
            </div>

            <button
              className="h-20 w-20 cursor-pointer rounded-full bg-white shadow-2xl outline-4 outline-offset-4 outline-white hover:bg-white/75 active:bg-white/50"
              onClick={capture}
            ></button>

            <div className="mt-3 ml-6 flex w-[calc(50%-40px)] flex-grow flex-row items-center justify-end gap-3">
              <Button
                variant="light"
                color="gray"
                onClick={() => onClose()}
                className="rounded-lg px-8 py-2 text-lg"
              >
                Annuler
              </Button>
              <Button
                variant="primary"
                disabled={images.length === 0 && publicUrlsToDelete.length === 0}
                onClick={handleSubmit}
                className="rounded-lg px-8 py-2 text-lg"
              >
                Valider
              </Button>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
