import { useContext } from 'react'
import { motion } from 'framer-motion'

import { But<PERSON> } from '@/components/Forms/Button'
import { ControlContext } from '@/contexts/Control'
import {
  TbChevronRight,
  TbCircleCheck,
  TbInbox,
  TbArrowBigRightLines,
} from 'react-icons/tb'
import { SenderInfo } from './SenderInfo'
import { Sender } from '@/models/sender'

export const ControlTerminer = () => {
  const { handleNouvelleEnveloppe, currentEnveloppe } =
    useContext(ControlContext)
  const casier = currentEnveloppe?.casier
  const hasCasier = Boolean(casier)

  // ---------------------------------------------------------------------------
  // Vue "Casier" : enveloppe à gauche → flèche animée → casier à droite
  // ---------------------------------------------------------------------------
  if (hasCasier) {
    const numero = casier!.numero

    return (
      <div className="flex w-full flex-col items-center justify-center px-4">
        {/* Ligne principale */}
        <div className="mt-24 flex flex-row items-center gap-8">
          {/* Enveloppe animée (léger flottement vertical infini) */}

          <SenderInfo
            expediteur={currentEnveloppe!.expediteur as Sender}
            className="max-w-xs"
          />

          {/* Flèche animée (va‑et‑vient horizontal + fade) */}
          <motion.div
            animate={{ x: [0, 12, 0], opacity: [1, 0.4, 1] }}
            transition={{ duration: 1.5, repeat: Infinity, ease: 'easeInOut' }}
            className="flex"
          >
            <TbArrowBigRightLines className="h-28 w-28 text-blue-500" />
          </motion.div>

          {/* Casier visuel */}
          <div className="relative">
            <div className="flex h-48 w-48 items-center justify-center rounded-2xl border-4 border-blue-500 bg-blue-50 shadow-xl">
              <span className="text-4xl font-bold text-blue-700">{numero}</span>
              <TbInbox className="absolute -right-6 -bottom-6 h-12 w-12 text-blue-400" />
            </div>
          </div>
        </div>

        {/* Instruction texte */}
        <p className="mt-10 mb-12 text-center text-xl font-light">
          Faites glisser le pli dans le casier&nbsp;
          <span className="font-semibold">#{numero}</span>.
        </p>

        <Button
          variant="primary"
          onClick={handleNouvelleEnveloppe}
          className="mb-16 flex flex-row items-center pr-4 pl-8 text-lg"
        >
          Commencer un autre pli <TbChevronRight className="ml-2 h-6 w-6" />
        </Button>
      </div>
    )
  }

  // ---------------------------------------------------------------------------
  // Vue "Succès" (aucun casier)
  // ---------------------------------------------------------------------------
  return (
    <div className="mb-2 flex w-full flex-col items-center justify-center">
      <TbCircleCheck className="animate-fade-in-top mt-32 h-72 w-72 text-green-300" />
      <h2 className="mt-2 mb-8 text-3xl font-extralight">
        Le pli a été terminé avec succès.
      </h2>

      <Button
        variant="primary"
        onClick={handleNouvelleEnveloppe}
        className="mb-16 flex flex-row items-center pr-4 pl-8 text-lg"
      >
        Commencer un autre pli <TbChevronRight className="ml-2 h-6 w-6" />
      </Button>
    </div>
  )
}
