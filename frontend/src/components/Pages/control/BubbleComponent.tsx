/**
 * BubbleComponent.ts
 */
import { ValidationResponse } from '@/models/affranchissement'

/**
 * Creates a bubble-like HTMLElement that shows the `code` text
 * and a tooltip with validation info from `useAffranchissement`.
 *
 * @param {string} code - The code to validate and display
 * @returns {HTMLElement} A styled bubble element
 */
export function BubbleComponent(data: {
  code: string
  validationInfo?: ValidationResponse | null
}): HTMLElement {
  const { code } = data

  // Create the container
  const container = document.createElement('div')

  container.style.backgroundColor = 'rgba(255, 255, 255, 0.2)'

  // Ensure the container can display the tooltip
  container.style.position = 'relative'
  container.style.overflow = 'visible' // Important for tooltip
  container.style.zIndex = '1' // Or higher if needed

  container.style.width = `${200 * window.devicePixelRatio}px`
  container.style.height = `${50 * window.devicePixelRatio}px`
  container.style.display = 'flex'
  container.style.justifyContent = 'center'
  container.style.alignItems = 'center'
  container.style.borderRadius = `${(50 / 2) * window.devicePixelRatio}px`
  container.style.cursor = 'default'

  // Visible text: the code
  const textEl = document.createElement('div')
  textEl.style.fontFamily = 'Helvetica, sans-serif'
  textEl.style.fontSize = `${12 * window.devicePixelRatio}px`
  textEl.style.fontWeight = 'bold'
  textEl.style.textAlign = 'center'
  textEl.style.color = 'black'
  textEl.textContent = code
  container.appendChild(textEl)

  return container
}
