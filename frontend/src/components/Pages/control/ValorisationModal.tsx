import React from 'react'
import { Modal } from '@/components/Modal'
import { Button } from '@/components/Forms/Button'
import type { Valorisation } from '@/models/enveloppe'
import { TbMail, TbTruckDelivery, TbPackage, TbSend } from 'react-icons/tb'

// Mapping des clés vers des labels plus descriptifs, organisé par section
const LABEL_MAPPING: Record<string, Record<string, string>> = {
  postage: {
    cout_enveloppe: "Valorisation théorique",
    cout_affranchissements_valide: "Valeur des affranchissements valides",
    // nb_affranchissements_invalides: "Nombre d'affranchissements invalides",
    montant_sous_affranchissement: "Sous-affranchissement",
    // presence_affranchissements_invalide: "Présence d'affranchissements invalides",
    // presence_taxe: "Présence de taxe",
    // coeff_taxe_livraison: "Coefficient de taxe de livraison",
  },
  livraison: {
    taxe_livraison_a_recuperer: "Taxe de livraison à récupérer",
    taxe_livraison_fixe: "Taxe de livraison fixe",
    taxe_livraison_totale: "Taxe de livraison totale",
    cout_total: "Coût total livraison",
  },
  collecte: {
    frais_collecte_ht: "Frais de collecte HT",
    coeff_collecte_tva: "Coefficient TVA collecte",
    cout_ht: "Coût collecte HT",
    cout_tva: "TVA collecte",
    cout_ttc: "Coût collecte TTC",
  },
  expédition: {
    frais_expédition: "Frais d'expédition ( non soumis tva )",
    cout_ht: "Frais d'expedition + Traitement forfaitaire par pli",
    cout_tva: "TVA sur forfait",
    cout_ttc: "Coût expédition TTC",
  }
}

interface SectionProps {
  title: string
  icon: React.ReactNode
  data: Record<string, number | boolean>
  color: string
  section: string // Ajout du nom de la section
}

const ValueRow = ({
  label,
  value,
}: {
  label: string
  value: number | boolean | string
}) => {
  // Format boolean values
  if (typeof value === 'boolean') {
    return (
      <div className="flex justify-between py-2 text-sm">
        <span className="text-gray-600">{label}</span>
        <span
          className={`font-medium ${value ? 'text-green-600' : 'text-gray-500'}`}
        >
          {value ? 'Oui' : 'Non'}
        </span>
      </div>
    )
  }

  // Format number values as currency
  if (typeof value === 'number') {
    return (
      <div className="flex justify-between py-2 text-sm">
        <span className="text-gray-600">{label}</span>
        <span className="font-medium">{value.toFixed(2)} €</span>
      </div>
    )
  }

  // Default case
  return (
    <div className="flex justify-between py-2 text-sm">
      <span className="text-gray-600">{label}</span>
      <span className="font-medium">{value}</span>
    </div>
  )
}

const ValorisationSection = ({ title, icon, data, color, section }: SectionProps) => {
  // Calculate section total if applicable
  const hasTotal = 'cout_total' in data || 'cout_ttc' in data
  const totalValue = (data.cout_total || data.cout_ttc || 0) as number

  return (
    <div className="mb-6 overflow-hidden rounded-lg border border-gray-200">
      <div className={`flex items-center gap-3 px-4 py-3 ${color} text-white`}>
        {icon}
        <h3 className="text-lg font-medium">{title}</h3>
      </div>

      <div className="bg-white p-4">
        {Object.entries(data)
          .filter(([key]) => LABEL_MAPPING[section]?.[key] !== undefined)
          .map(([key, value]) => (
            <ValueRow key={key} label={LABEL_MAPPING[section][key]} value={value} />
          ))}

        {hasTotal && (
          <div className="mt-2 flex justify-between border-t border-gray-200 pt-2 font-bold">
            <span>Total</span>
            <span>{totalValue.toFixed(2)} €</span>
          </div>
        )}
      </div>
    </div>
  )
}
interface ValorisationModalProps {
  valorisation: Valorisation | null
  isOpen: boolean
  onClose: () => void
}

export const ValorisationModal: React.FC<ValorisationModalProps> = ({
  valorisation,
  isOpen,
  onClose,
}) => {
  if (!valorisation) return null

  const contientFaux = valorisation.postage.presence_affranchissements_invalide

  return (
    <Modal
      open={isOpen}
      onClose={onClose}
      maxWidth="2xl"
      title="Détails de valorisation"
    >
      <div className="p-6">
        <div className="max-h-[60vh] overflow-y-auto pr-2">
          {/* Postage Section */}
          <ValorisationSection
            title="Affranchissement"
            icon={<TbMail className="h-6 w-6" />}
            data={valorisation.postage}
            color="bg-blue-600"
            section="postage"
          />

          {/* Delivery Section */}
          {!contientFaux && <ValorisationSection
            title="Livraison"
            icon={<TbTruckDelivery className="h-6 w-6" />}
            data={valorisation.livraison}
            color="bg-green-600"
            section="livraison"
          />
          }

          {/* Collection Section */}
          {!contientFaux && <ValorisationSection
            title="Collecte"
            icon={<TbPackage className="h-6 w-6" />}
            data={valorisation.collecte}
            color="bg-purple-600"
            section="collecte"
          />}

          {/* Shipping Section */}
          <ValorisationSection
            title="Retour expéditeur"
            icon={<TbSend className="h-6 w-6" />}
            data={valorisation.expédition}
            color="bg-orange-600"
            section="expédition"
          />
        </div>

        <div className="mt-6 flex justify-end border-t border-gray-200 pt-4">
          <Button variant="primary" onClick={onClose}>
            Fermer
          </Button>
        </div>
      </div>
    </Modal>
  )
}
