import React, { useState } from 'react'
import { AffranchissementCategory } from '@/models/affranchissement'
import { useAffranchissement } from '@/hooks/useAffranchissement'
import { useDisclosure } from '@/hooks/useDisclosure'
import { CategoryAffranchissementModal } from './CategoryAffranchissementModal'
import { getAffranchissementColors } from '@/utils/controlHelpers'

// React Icons (Font Awesome)
import { FaBarcode, FaStamp, FaCog, FaEnvelope, FaTag } from 'react-icons/fa'

import { GiPostStamp } from 'react-icons/gi'

// Icon mapping function
function getCategoryIcon(categoryId: string) {
  switch (categoryId) {
    case 'CODE':
      return <FaBarcode className="h-5 w-5" />
    case 'MARI':
      return <GiPostStamp className="h-5 w-5" />
    case 'BEAU':
      return <GiPostStamp className="h-5 w-5" />
    case 'MAFF':
      return <FaCog className="h-5 w-5" />
    case 'T':
      return <FaEnvelope className="h-5 w-5" />
    case 'VIGN':
      return <FaTag className="h-5 w-5" />
    default:
      return <FaStamp className="h-5 w-5" />
  }
}

export const SectionCategorieAffranchissement: React.FC = () => {
  const { modelesAffranchissements, handleAddAffranchissement } =
    useAffranchissement()
  const [selectedCategory, setSelectedCategory] =
    useState<AffranchissementCategory | null>(null)
  const { isOpen, onOpen, onClose } = useDisclosure()

  const handleCategoryClick = (category: AffranchissementCategory) => {
    setSelectedCategory(category)
    onOpen()
  }

  // SD87 par exemple pas de valeur à rentrer

  return (
    <>
      <div className="mb-8">
        {/* Use a grid layout for the category buttons */}
        <div className="grid grid-cols-4 gap-4 lg:grid-cols-7">
          {modelesAffranchissements?.map((category) => {
            // Pull your custom text/background colors
            const colors = getAffranchissementColors(category.display1)

            return (
              <button
                key={category.id}
                onClick={() => handleCategoryClick(category)}
                className="flex transform cursor-pointer flex-col items-center justify-center rounded-lg border border-gray-200 p-2 shadow-sm transition-transform hover:scale-105 hover:bg-gray-50"
              >
                {/* Icon circle */}
                <span
                  className={`mb-1 flex items-center justify-center rounded-full p-2 ${colors.text} ${colors.bg} `}
                >
                  {getCategoryIcon(category.id)}
                </span>
                {/* Text label */}
                <span className="text-center text-[10px] leading-tight font-medium">
                  {category.description}
                </span>
              </button>
            )
          })}
        </div>
      </div>

      {isOpen && selectedCategory && (
        <CategoryAffranchissementModal
          category={selectedCategory}
          onAdd={handleAddAffranchissement}
          onClose={onClose}
          open={isOpen}
        />
      )}
    </>
  )
}
