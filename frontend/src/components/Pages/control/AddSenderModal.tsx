import { Button } from '@/components/Forms/Button'
import { Divider } from '@/components/Forms/Divider'
import { Input } from '@/components/Forms/Input'
import Modal from '@/components/Forms/Modal'
import { ControlContext } from '@/contexts/Control'
import { Body_post_senders } from '@/models/sender'
import { SenderService } from '@/services/senderService'
import { useContext } from 'react'
import { useForm } from 'react-hook-form'
import { TbChevronLeft, TbPlus } from 'react-icons/tb'
import { Sender } from '@/models/sender'
import {
  AddressAutocomplete,
  AddressData,
} from '@/components/Forms/AddressAutocomplete'
import { useEffect, useState } from 'react'
import { PappersService } from '@/services/pappersService'
import { useDebounce } from '@/hooks/useDebounce'
import { Spinner } from '@/components/Forms/Spinner'
import { PappersResult } from '@/models/pappersResults'

type AddSenderModalProps = {
  isModalShown: boolean
  setIsModalShown: (isModalShown: boolean) => void
  onSenderAdded: (sender: Sender) => void
  initialName?: string // Nouveau prop pour le nom initial
}

export const AddSenderModal = ({
  isModalShown,
  setIsModalShown,
  onSenderAdded,
  initialName = '', // Valeur par défaut vide
}: AddSenderModalProps) => {
  const { setSelectedSender } = useContext(ControlContext)

  const {
    register,
    handleSubmit,
    formState: { isSubmitting },
    setError: setFormError,
    setValue,
    watch,
  } = useForm<Body_post_senders>({
    mode: 'onSubmit',
    criteriaMode: 'all',
    defaultValues: {
      nom: initialName, // Utiliser le nom initial
      adresse: '',
      pays: 'France',
      ville: '',
      code_postal: '',
      siret: null,
      coclico: null,
    },
  })

  const [isCompanySearchLoading, setIsCompanySearchLoading] = useState(false)
  const [companyResults, setCompanyResults] = useState<PappersResult[]>([])
  const [showCompanyResults, setShowCompanyResults] = useState(false)
  const nomValue = watch('nom')
  const debouncedNomValue = useDebounce(nomValue, 300)

  useEffect(() => {
    const searchCompanies = async () => {
      if (debouncedNomValue && debouncedNomValue.length >= 3) {
        setIsCompanySearchLoading(true)
        try {
          const data = await PappersService.searchCompanies(debouncedNomValue)
          setCompanyResults(data.resultats || [])
          setShowCompanyResults(true)
        } catch (error) {
          console.error('Erreur lors de la recherche d\'entreprises', error)
        } finally {
          setIsCompanySearchLoading(false)
        }
      } else {
        setCompanyResults([])
        setShowCompanyResults(false)
      }
    }

    searchCompanies()
  }, [debouncedNomValue])

  const handleCompanySelect = (company: PappersResult) => {
    setValue('nom', company.nom_entreprise || company.nom_commercial || '')
    setValue('siret', company.siege.siret || '')
    setValue('adresse', company.siege.adresse_ligne_1 || '')
    setValue('ville', company.siege.ville || '')
    setValue('code_postal', company.siege.code_postal || '')
    setValue('pays', 'France')
    setShowCompanyResults(false)
  }

  // Mettre à jour le nom quand initialName change
  useEffect(() => {
    if (initialName) {
      setValue('nom', initialName)
    }
  }, [initialName, setValue])

  const adresseValue = watch('adresse')
  const villeValue = watch('ville')
  const codePostalValue = watch('code_postal')

  const [similarSenders, setSimilarSenders] = useState<Sender[]>([])
  const [isCheckingSimilar, setIsCheckingSimilar] = useState(false)

  useEffect(() => {
    const checkSimilarSenders = async () => {
      // Vérifier si tous les champs d'adresse sont remplis
      if (adresseValue && villeValue && codePostalValue) {
        setIsCheckingSimilar(true)
        try {
          const data = await SenderService.searchSimilarSenders({
            adresse: adresseValue,
            ville: villeValue,
            code_postal: codePostalValue
          })
          
          if (data.length > 0) {
            setSimilarSenders(data)
          } else {
            setSimilarSenders([])
          }
        } catch (error) {
          console.error('Erreur lors de la recherche d\'expéditeurs similaires', error)
        } finally {
          setIsCheckingSimilar(false)
        }
      }
    }

    // Utiliser un debounce pour ne pas faire trop de requêtes
    const timer = setTimeout(checkSimilarSenders, 500)
    return () => clearTimeout(timer)
  }, [adresseValue, villeValue, codePostalValue])

  const onSubmit = async (data: Body_post_senders) => {
    if (isSubmitting) return
    try {
      const sender = await SenderService.postSenders(data)
      setSelectedSender(sender)
      setIsModalShown(false)
      onSenderAdded(sender)
    } catch (err: any) {
      const errorMessage = err.body?.detail || err.message
      setFormError('root', {
        type: 'manual',
        message: errorMessage,
      })
    }
  }

  const handleAddressSelect = (addressData: AddressData) => {
    setValue('adresse', addressData.name || '')
    setValue('ville', addressData.city || '')
    setValue('code_postal', addressData.postcode || '')
    setValue('pays', 'France')
  }

  return (
    <Modal
      title="Créer un nouvel Expéditeur"
      isShown={isModalShown}
      closeModal={() => setIsModalShown(false)}
    >
      <form onSubmit={handleSubmit(onSubmit)} noValidate>
        <div className="flex flex-col">
          <label className="mt-2 mb-2 text-lg">
            Nom
            <span className="text-red-600">&nbsp;*</span>
          </label>
          <div className="relative">
            <Input
              type="text"
              placeholder="Nom"
              {...register('nom', {
                required: 'Nom requis.',
              })}
              className="w-full"
              onFocus={() => nomValue?.length >= 3 && setShowCompanyResults(true)}
            />
            {isCompanySearchLoading && (
              <div className="absolute right-3 top-2">
                <Spinner className="h-5 w-5" />
              </div>
            )}
            
            {showCompanyResults && companyResults.length > 0 && (
              <>
                <div
                  className="fixed inset-0 z-10"
                  onClick={() => setShowCompanyResults(false)}
                />
                <div className="absolute top-[42px] right-0 left-0 z-20 max-h-[300px] w-full overflow-y-auto rounded-lg border border-gray-200 bg-white p-3 shadow-lg">
                  {companyResults.map((company, index) => (
                    <div
                      key={index}
                      className="cursor-pointer rounded-md p-2 hover:bg-blue-50 hover:text-blue-500"
                      onClick={() => handleCompanySelect(company)}
                    >
                      <div className="text-sm font-medium">
                        {company.nom_entreprise || company.nom_commercial}
                      </div>
                      <div className="text-xs text-gray-500">
                        SIRET: {company.siege.siret}
                      </div>
                      <div className="text-xs text-gray-500">
                        {company.siege.adresse_ligne_1}, {company.siege.code_postal} {company.siege.ville}
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>

          <div className="mt-6 flex flex-row">
            <div className="mr-1.5 flex w-1/2 flex-grow flex-row gap-3">
              <div className="flex w-1/2 flex-grow flex-col">
                <label className="mb-2 text-lg">Ville</label>
                <Input
                  type="text"
                  placeholder="Ville"
                  {...register('ville')}
                  className="w-full"
                />
              </div>
              <div className="flex w-1/2 flex-grow flex-col">
                <label className="mb-2 text-lg">Code postal</label>
                <Input
                  type="text"
                  placeholder="Code postal"
                  {...register('code_postal')}
                  className="w-full"
                />
              </div>
            </div>

            <div className="ml-1.5 flex w-1/2 flex-grow flex-col">
              <label className="mb-2 text-lg">Pays</label>
              <Input
                type="text"
                placeholder="Pays"
                {...register('pays')}
                className="w-full"
              />
            </div>
          </div>

          <label className="mt-6 mb-2 text-lg">Adresse</label>
          <AddressAutocomplete
            value={adresseValue}
            onChange={(value) => setValue('adresse', value)}
            onSelect={handleAddressSelect}
            placeholder="Saisir une adresse"
            postcodeFilter={codePostalValue || undefined}
          />

          {similarSenders.length > 0 && (
            <div className="mt-4 rounded-md border border-yellow-300 bg-yellow-50 p-3">
              <h3 className="mb-2 font-semibold text-yellow-800">
                Expéditeurs similaires trouvés ({similarSenders.length})
              </h3>
              <div className="max-h-[150px] overflow-y-auto">
                {similarSenders.map((sender) => (
                  <div 
                    key={sender.id} 
                    className="mb-2 cursor-pointer rounded-md border border-gray-200 bg-white p-2 hover:bg-blue-50"
                    onClick={() => {
                      if (confirm("Voulez-vous utiliser cet expéditeur existant au lieu d'en créer un nouveau ?")) {
                        setSelectedSender(sender)
                        setIsModalShown(false)
                        onSenderAdded(sender)
                      }
                    }}
                  >
                    <div className="font-medium">{sender.nom}</div>
                    <div className="text-xs text-gray-600">
                      {sender.adresse}, {sender.code_postal} {sender.ville}, {sender.pays}
                    </div>
                    {sender.siret && (
                      <div className="mt-1 text-xs text-gray-500">
                        SIRET: {sender.siret}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {isCheckingSimilar && (
            <div className="mt-2 flex items-center text-sm text-gray-500">
              <Spinner className="mr-2 h-4 w-4" />
              Vérification des expéditeurs similaires...
            </div>
          )}

          <Divider className="mt-8" />
          <div className="mt-6 flex flex-row gap-3">
            <div className="flex flex-grow flex-col">
              <label className="mb-2 text-lg">Coclico</label>
              <Input
                type="text"
                placeholder="Coclico"
                {...register('coclico')}
              />
            </div>
            <div className="flex flex-grow flex-col">
              <label className="mb-2 text-lg">Siret</label>
              <Input type="text" placeholder="Siret" {...register('siret')} />
            </div>
          </div>
        </div>

        <div className="mt-6 flex flex-row items-center justify-end">
          <Button
            variant="outline"
            onClick={() => setIsModalShown(false)}
            type="button"
            className="mr-3 flex flex-row items-center !pl-1"
          >
            <TbChevronLeft className="mr-2 h-5 w-5" />
            Annuler
          </Button>
          <Button
            variant="primary"
            type="submit"
            className="flex flex-row items-center !pl-2"
          >
            <TbPlus className="mr-2 h-5 w-5" />
            Créer
          </Button>
        </div>
      </form>
    </Modal>
  )
}
