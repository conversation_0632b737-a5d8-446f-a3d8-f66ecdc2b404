import { Button } from '@/components/Forms/Button'
import { useEffect, useState } from 'react'
import { TbChevronLeft } from 'react-icons/tb'
import ClassicScanner from './scandit/ClassicScanner'

// IMPORTANT: Replace this import path with wherever you keep your Scandit-based scanner

type CodeScannerProps = {
  submit: (code: string) => void | Promise<void>
  onClose: () => void
}

export const CodeScanner = ({ submit, onClose }: CodeScannerProps) => {
  const [data, setData] = useState<string | null>(null)

  // Whenever we have a scanned code, call submit
  useEffect(() => {
    if (data) {
      submit(data)
    }
  }, [data, submit])

  return (
    <div className="fixed top-0 right-0 bottom-0 left-0 z-50 flex h-screen w-screen flex-col items-center justify-center bg-black">
      {/* Our Scandit-based Scanner */}
      <ClassicScanner
        onDetected={(scannedCode) => {
          if (scannedCode) {
            setData(scannedCode)
          }
        }}
      />

      {/* Bottom overlay with a "Retour" button and detection status */}
      <div className="absolute bottom-0 left-0 flex h-40 w-full items-center justify-center bg-black/50 px-6">
        <Button
          variant="light"
          color="gray"
          className="mr-4 flex flex-row items-center justify-center px-8 py-2 pl-4 text-2xl"
          onClick={onClose}
        >
          <TbChevronLeft className="mr-3 h-6 w-6" />
          Retour
        </Button>

        <div
          className={`flex flex-col items-center justify-center rounded-lg px-8 py-1.5 text-3xl ${
            data ? 'bg-yellow-200/50' : 'bg-white/50'
          }`}
        >
          {data ? (
            <p className="text-yellow-700">Code détecté</p>
          ) : (
            <p>Aucun code barre détecté</p>
          )}
        </div>
      </div>
    </div>
  )
}
