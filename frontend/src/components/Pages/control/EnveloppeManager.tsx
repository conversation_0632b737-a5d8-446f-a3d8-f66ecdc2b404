import { use<PERSON><PERSON>back, useContext, useEffect, useState, useRef } from 'react'
import { Checkbox } from '@/components/Forms/Checkbox'
import { Input } from '@/components/Forms/Input'
import { Button } from '@/components/Forms/Button'
import { SearchSelector } from '@/components/Forms/SearchSelector'
import { ControlContext } from '@/contexts/Control'
import { Destination } from '@/models/destination'
import { Sender } from '@/models/sender'
import { EnveloppeService } from '@/services/enveloppeService'
import { SenderService } from '@/services/senderService'
import { DestinationService } from '@/services/destinationService'
import { Body_post_enveloppe } from '@/models/enveloppe'

import { AddSenderModal } from '@/components/Pages/control/AddSenderModal'
import Modal from '@/components/Forms/Modal'
import { useDisclosure } from '@/hooks/useDisclosure'
import { useQueryClient } from '@tanstack/react-query'
import { TbInfoCircle, TbMapPin, Tb<PERSON><PERSON>age, TbUser, TbWeight } from 'react-icons/tb'

export const EnveloppeManager = () => {
  const queryClient = useQueryClient()
  const {
    selectedSender,
    selectedDestination,
    weight,
    isOverWeight,
    isOverSized,
    destinationsData,
    handleIsOverSizedChange,
    setSelectedSender,
    setSelectedDestination,
    setWeight,
    currentEnveloppe,
    setSelectedDestinationEnveloppe,
    selectedDestinationEnveloppe,
  } = useContext(ControlContext)

  // États pour les modaux

  const [isAddSenderModalShown, setIsAddSenderModalShown] = useState(false)
  const [newSenderName, setNewSenderName] = useState('')
  const [isCasierChangeModalShown, setIsCasierChangeModalShown] = useState(false)
  const [casierChangeInfo, setCasierChangeInfo] = useState<{
    ancienCasier: string | null
    nouveauCasier: string | null
  } | null>(null)

  const timeoutRef = useRef<number | null>(null)

  const [shouldUpdateEnveloppe, setShouldUpdateEnveloppe] = useState(false)

  const { onOpen: onOpenSenderModal, onClose: onCloseSenderModal } =
    useDisclosure()

  const {
    isOpen: isOpenProductModal,
    onOpen: onOpenProductModal,
    onClose: onCloseProductModal,
  } = useDisclosure()

  // Charger les données de l'enveloppe courante
  useEffect(() => {
    if (currentEnveloppe) {
      setWeight(currentEnveloppe.poids)
      setSelectedDestinationEnveloppe(currentEnveloppe.destination_enveloppe)
      if (currentEnveloppe.expediteur) {
        setSelectedSender(currentEnveloppe.expediteur as Sender)
      }
      if (currentEnveloppe.destination) {
        setSelectedDestination(currentEnveloppe.destination as Destination)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentEnveloppe])

  // Fonction pour mettre à jour l'enveloppe avec les valeurs actuelles
  useEffect(() => {
    const updateEnveloppe = async () => {
      if (!currentEnveloppe) return

      // Sauvegarder l'ancien casier_id pour détecter les changements
      const ancienCasierId = currentEnveloppe.casier?.id

      const enveloppe: Body_post_enveloppe = {
        id: currentEnveloppe.id,
        poids: weight ?? 0,
        surpoids: isOverWeight ?? false,
        surdimensionne: isOverSized ?? false,
        destination_enveloppe: selectedDestinationEnveloppe ?? '',
        destination_id: selectedDestination?.id ?? undefined,
        expediteur_id: selectedSender?.id ?? undefined,
      }

      console.log("Mise à jour de l'enveloppe avec les valeurs:", enveloppe)

      try {
        const enveloppeResponse =
          await EnveloppeService.postEnveloppe(enveloppe)

        // Vérifier si le casier_id a changé
        const nouveauCasierId = enveloppeResponse.casier?.id
        if (ancienCasierId !== nouveauCasierId) {
          setCasierChangeInfo({
            ancienCasier: currentEnveloppe.casier?.numero || null,
            nouveauCasier: enveloppeResponse.casier?.numero || null
          })
          setIsCasierChangeModalShown(true)
        }

        queryClient.setQueryData(['enveloppe'], enveloppeResponse)
        setShouldUpdateEnveloppe(false) // Réinitialiser le drapeau après la mise à jour
      } catch (error) {
        console.error("Erreur lors de la mise à jour de l'enveloppe:", error)
      }
    }

    if (shouldUpdateEnveloppe) {
      updateEnveloppe()
    }
  }, [
    shouldUpdateEnveloppe,
    weight,
    isOverWeight,
    isOverSized,
    selectedDestination,
    selectedDestinationEnveloppe,
    selectedSender,
    currentEnveloppe,
    queryClient,
  ])

  // Fonction debounce pour éviter les appels multiples
  const debouncedUpdate = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    timeoutRef.current = setTimeout(() => {
      setShouldUpdateEnveloppe(true) // Déclencher la mise à jour
      timeoutRef.current = null
    }, 400)
  }, [])

  const inputRef = useRef<HTMLInputElement>(null)

  // Gestionnaires d'événements pour les changements de valeurs
  const handleWeightChange = useCallback(
    (value: number) => {
      setWeight(value)
      // Si la valeur devient 0, sélectionner tout le texte
      if (value === 0 && inputRef.current) {
        setTimeout(() => inputRef.current?.select(), 0)
      }
      debouncedUpdate()
    },
    [setWeight, debouncedUpdate]
  )

  const handleOverSizedChange = useCallback(() => {
    handleIsOverSizedChange()
    debouncedUpdate()
  }, [handleIsOverSizedChange, debouncedUpdate])

  const handleDestinationEnveloppeChange = useCallback(
    (destination: string) => {
      setSelectedDestinationEnveloppe(destination)
      onCloseProductModal()
      debouncedUpdate()
    },
    [setSelectedDestinationEnveloppe, onCloseProductModal, debouncedUpdate]
  )

  const handleSenderChange = useCallback(
    (sender: Sender) => {
      setSelectedSender(sender)
      if (sender === undefined) {
        onOpenSenderModal()
        debouncedUpdate()
      } else {
        onCloseSenderModal()
        debouncedUpdate()
      }
    },
    [setSelectedSender, onOpenSenderModal, debouncedUpdate, onCloseSenderModal]
  )

  if (currentEnveloppe === undefined) {
    return <div>Enveloppe non trouvée</div>
  }

  return (
    <>
      <div className="flex w-full flex-col">
        <div className="mb-4 grid grid-cols-2 gap-2 md:grid-cols-4">
          {/* Sélection du produit */}
          <div
            className={`flex flex-col rounded-lg border border-gray-200 p-2 ${
              currentEnveloppe?.informations?.modifiable === false
                ? 'opacity-50'
                : 'cursor-pointer hover:bg-gray-50'
            }`}
            onClick={
              currentEnveloppe?.informations?.modifiable === false
                ? undefined
                : onOpenProductModal
            }
          >
            <label className="inline-flex items-center gap-1 text-xs text-gray-500">
              <TbPackage className="h-4 w-4" />
              Destination <span className="text-red-600">*</span>
            </label>

            <div className="truncate text-sm font-medium">
              {selectedDestinationEnveloppe
                ? selectedDestinationEnveloppe
                : 'Sélectionner une destination'}
            </div>
          </div>

          {/* Sélection de l'expéditeur */}
          {/* Sélection directe de l'expéditeur */}
          <div className="flex flex-col rounded-lg border border-gray-200 p-2">
            <label className="inline-flex items-center gap-1 text-xs text-gray-500">
              <TbUser className="h-4 w-4" /> Expéditeur{' '}
              <span className="text-red-600">*</span>
            </label>

            <SearchSelector
              placeholder="Rechercher un expéditeur"
              className="mt-1 w-full"
              selectedValue={selectedSender}
              setSelectedValue={(sender) => {
                setSelectedSender(sender)
                debouncedUpdate()
              }}
              searchResultPromise={SenderService.getSenders}
              selectedItemLabel={(s: Sender) => s.nom}
              resultItem={(s: Sender) => (
                <>
                  <div className="mb-0.5 flex items-center text-sm">
                    <span className="font-semibold">{s.nom}</span>
                    {s.siret && (
                      <span className="ml-1 rounded-sm border border-blue-300 bg-blue-50 px-1 py-0.5 text-xs text-blue-500">
                        siret : <span className="font-semibold">{s.siret}</span>
                      </span>
                    )}
                    {s.coclico && (
                      <span className="ml-1 rounded-sm border border-blue-300 bg-blue-50 px-1 py-0.5 text-xs text-blue-500">
                        coclico :{' '}
                        <span className="font-semibold">{s.coclico}</span>
                      </span>
                    )}
                  </div>
                  <div className="text-xs text-gray-500">
                    {s.adresse}, {s.code_postal} {s.ville}, {s.pays}
                  </div>
                </>
              )}
              onCreateNew={(searchValue) => {
                setNewSenderName(searchValue)
                setIsAddSenderModalShown(true)
              }}
              createNewLabel="Créer un nouvel expéditeur"
              disabled={currentEnveloppe?.informations?.modifiable === false}
            />
          </div>

          {/* Poids et dimensions */}
          <div className="flex flex-col rounded-lg border border-gray-200 p-2">
            <label className="inline-flex items-center gap-1 text-xs text-gray-500">
              <TbWeight className="h-4 w-4" /> Poids{' '}
              <span className="text-red-600">*</span>
            </label>
            <div className="mt-1 flex items-center gap-2">
              <Input
                ref={inputRef}
                className={`w-32 rounded-lg border border-gray-200 px-4 py-2 outline-3 outline-offset-0 outline-transparent focus:border-blue-500 focus:outline-blue-100 disabled:bg-slate-100 disabled:opacity-50`}
                placeholder="Poids (g)"
                autoComplete="off"
                value={weight ?? ''}
                onChange={(e) => {
                  const value = Number(e.target.value)
                  if (value >= 0 || e.target.value === '') {
                    handleWeightChange(value)
                  }
                }}
                disabled={currentEnveloppe?.informations?.modifiable === false}
                onFocus={(e) => e.target.select()}
                type="number"
                min="0"
              />

              <div className="ml-1 flex items-center">
                <Checkbox
                  id="checked-checkbox"
                  checked={isOverSized ?? false}
                  onChange={() => handleOverSizedChange()}
                  disabled={
                    currentEnveloppe?.informations?.modifiable === false
                  }
                />
                <label
                  htmlFor="checked-checkbox"
                  className="ml-1 text-xs whitespace-nowrap"
                >
                  Hors-gabarit
                </label>
              </div>
            </div>
          </div>

          {/* Destination - conditionnelle */}
          {(isOverWeight || isOverSized || (weight && weight > 2000)) &&
          !(
            currentEnveloppe.informations.nombre_affranchissements_invalides >
              0 && isOverSized
          ) ? (
            <div className="flex flex-col rounded-lg border border-gray-200 p-2">
              <label className="inline-flex items-center gap-1 text-xs text-gray-500">
                <TbMapPin className="h-4 w-4" /> Zone tarifaire{' '}
                <span className="text-red-600">*</span>
              </label>

              <SearchSelector
                placeholder="Rechercher une zone tarifaire"
                className="mt-1 w-full"
                selectedValue={selectedDestination}
                setSelectedValue={(d) => {
                  setSelectedDestination(d)
                  debouncedUpdate()
                }}
                searchResultPromise={DestinationService.getDestinations}
                selectedItemLabel={(d) => d.nom_fr}
                resultItem={(d) => (
                  <span className="font-semibold">{d.nom_fr}</span>
                )}
                disabled={currentEnveloppe?.informations?.modifiable === false}
              />
            </div>
          ) : null}
        </div>
      </div>

      {/* Modaux pour les sélections */}
      <Modal
        title="Sélectionner une destination"
        isShown={isOpenProductModal}
        closeModal={onCloseProductModal}
      >
        <div className="mb-4 grid grid-cols-2 gap-3">
          {destinationsData?.map((destination) => (
            <Button
              key={destination}
              variant={
                selectedDestinationEnveloppe === destination
                  ? 'light'
                  : 'outline'
              }
              onClick={() => handleDestinationEnveloppeChange(destination)}
              disabled={selectedDestinationEnveloppe === destination}
              forceShowColor={true}
            >
              {destination}
            </Button>
          ))}
        </div>
      </Modal>

      {/* Modal d'ajout d'expéditeur */}
      <AddSenderModal
        isModalShown={isAddSenderModalShown}
        setIsModalShown={setIsAddSenderModalShown}
        initialName={newSenderName}
        onSenderAdded={(sender) => {
          handleSenderChange(sender)
          setIsAddSenderModalShown(false)
          setNewSenderName('')
        }}
      />

      {/* Modal d'information changement de casier */}
      <Modal
        title="Changement de casier"
        isShown={isCasierChangeModalShown}
        closeModal={() => setIsCasierChangeModalShown(false)}
      >
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-blue-600">
            <TbInfoCircle className="h-5 w-5" />
            <span className="font-medium">Information</span>
          </div>

          <div className="text-sm text-gray-700">
            {casierChangeInfo?.ancienCasier && casierChangeInfo?.nouveauCasier ? (
              <p>
                L'enveloppe a été déplacée du casier{' '}
                <span className="font-semibold">{casierChangeInfo.ancienCasier}</span>
                {' '}vers le casier{' '}
                <span className="font-semibold">{casierChangeInfo.nouveauCasier}</span>.
              </p>
            ) : casierChangeInfo?.nouveauCasier ? (
              <p>
                L'enveloppe a été assignée au casier{' '}
                <span className="font-semibold">{casierChangeInfo.nouveauCasier}</span>.
              </p>
            ) : (
              <p>
                L'enveloppe n'est plus assignée à un casier.
              </p>
            )}
          </div>

          <div className="flex justify-end">
            <Button
              variant="primary"
              onClick={() => setIsCasierChangeModalShown(false)}
            >
              Compris
            </Button>
          </div>
        </div>
      </Modal>
    </>
  )
}
