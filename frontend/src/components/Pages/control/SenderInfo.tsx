import { TbMail } from 'react-icons/tb'
import { motion } from 'framer-motion'
import clsx from 'clsx'
import { Sender } from '@/models/sender'

/**
 * SenderInfo
 * ---------------------------------------------------------------------------
 * Small reusable badge that displays the sender (expéditeur) information in a
 * visually‑distinct "label" style. Designed to be dropped into your existing
 * layout (inline‑block) anywhere you need to reference the sender.
 *
 * Props
 * ────────────────────────────────────────────────────────────────────────────
 * • expediteur: {
 *     nom?: string
 *     adresse?: string
 *     code_postal?: string | number
 *   }
 * • className?  : Tailwind utility classes for custom positioning.
 * ---------------------------------------------------------------------------
 */
export const SenderInfo = ({
  expediteur,
  className,
}: {
  expediteur: Sender
  className?: string
}) => {
  if (!expediteur) return null
  const { nom, adresse, code_postal } = expediteur

  return (
    <div
      aria-label="Expéditeur"
      className={clsx(
        // Visual container
        'flex items-start gap-3 rounded-2xl border border-gray-200/70 bg-white/60 p-4 shadow-md backdrop-blur-sm',
        // Subtle hover effect for affordance (optional)
        'transition hover:-translate-y-0.5 hover:shadow-lg',
        className
      )}
    >
      {/* Envelope icon with gentle floating animation */}
      <motion.div
        animate={{ y: [0, -4, 0] }}
        transition={{ duration: 2.5, repeat: Infinity, ease: 'easeInOut' }}
        className="flex-shrink-0"
      >
        <TbMail className="h-8 w-8 text-blue-600 drop-shadow-sm" />
      </motion.div>

      {/* Sender postal label */}
      <address className="leading-tight not-italic select-none">
        {nom && <p className="font-medium text-gray-900">{nom}</p>}
        {adresse && <p className="text-sm text-gray-700">{adresse}</p>}
        {code_postal && <p className="text-sm text-gray-700">{code_postal}</p>}
      </address>
    </div>
  )
}
