import { useState } from 'react'
import { Tb<PERSON>arcode, TbCheck } from 'react-icons/tb'
import { Input } from '@/components/Forms/Input'
import { Button } from '@/components/Forms/Button'
import { CodeScanner } from '@/components/Pages/control/CodeScanner'

interface CodeInputWithScannerProps {
  onSubmit: (code: string) => void
  placeholder?: string
}

export const CodeInputWithScanner = ({
  onSubmit,
  placeholder = 'SmartData, S10, Timbre numérique',
}: CodeInputWithScannerProps) => {
  const [code, setCode] = useState('')
  const [showScanner, setShowScanner] = useState(false)

  const handleCodeScannerSubmit = (scannedCode: string) => {
    setShowScanner(false)
    onSubmit(scannedCode)
  }

  return (
    <>
      <div className="mb-6 flex flex-row items-center">
        <Input
          className="flex flex-grow rounded-l-lg rounded-r-none"
          placeholder={placeholder}
          value={code}
          onChange={(e) => setCode(e.target.value)}
        />
        <Button
          variant="primary"
          disabled={code.length === 0}
          className={`mr-3 rounded-l-none rounded-r-lg border border-l-0 !px-2 ${
            code.length === 0 ? 'border-gray-200' : 'border-blue-600'
          }`}
          onClick={() => {
            onSubmit(code)
            setCode('')
          }}
        >
          <TbCheck className="h-6 w-6" />
        </Button>

        <Button
          variant="light"
          className="!px-2"
          onClick={() => setShowScanner(true)}
        >
          <TbBarcode className="h-6 w-6" />
        </Button>
      </div>

      {showScanner && (
        <CodeScanner
          submit={handleCodeScannerSubmit}
          onClose={() => setShowScanner(false)}
        />
      )}
    </>
  )
}
