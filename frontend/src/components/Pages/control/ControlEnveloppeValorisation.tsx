import { useContext, useMemo } from 'react'

import { ControlContext } from '@/contexts/Control'
import { Divider } from '@/components/Forms/Divider'
import { Button } from '@/components/Forms/Button'
import { TbCheck, TbChevronLeft } from 'react-icons/tb'
import { useQueryClient } from '@tanstack/react-query'

export const ControlEnveloppeValorisation = () => {
  const queryClient = useQueryClient()
  const { currentEnveloppe, handleTerminerEnveloppe, enveloppeKey } =
    useContext(ControlContext)

  const goBack = () => {
    if (currentEnveloppe) {
      queryClient.setQueryData(['enveloppe', enveloppeKey], {
        ...currentEnveloppe,
        statut: 'EDITION',
      })
    }
  }

  const isPostageValid = useMemo(() => {
    return (
      (currentEnveloppe?.valorisation?.postage?.montant_sous_affranchissement ??
        0) === 0
    )
  }, [currentEnveloppe])

  const hasInvalidAffranchissements = useMemo(() => {
    return (
      (currentEnveloppe?.valorisation?.postage
        ?.nb_affranchissements_invalides ?? 0) > 0
    )
  }, [currentEnveloppe])

  return (
    <>
      <div className="mt-1 mb-3 flex w-full">
        <h2 className="text-center text-lg font-semibold">
          Vérification de la valorisation du pli
        </h2>
      </div>
      <div className="rounded-lg border border-gray-200">
        <div className="mt-3 py-2">
          <h3 className="mb-2 px-4 text-xl font-semibold text-blue-600">
            Collecte
          </h3>
          <div className="grid grid-cols-4 gap-3 px-4">
            <div></div>
            <div className="flex flex-col">
              <p className="text-sm text-gray-600">Cout HT</p>
              <p className="text-lg font-semibold">
                {(
                  currentEnveloppe?.valorisation?.collecte.cout_ht ?? 0
                ).toLocaleString('fr-FR', {
                  style: 'currency',
                  currency: 'EUR',
                })}
              </p>
            </div>
            <div className="flex flex-col">
              <p className="text-sm text-gray-600">Cout TVA</p>
              <p className="text-lg font-semibold">
                {(
                  currentEnveloppe?.valorisation?.collecte.cout_tva ?? 0
                ).toLocaleString('fr-FR', {
                  style: 'currency',
                  currency: 'EUR',
                })}
              </p>
            </div>
            <div className="flex flex-col">
              <p className="text-sm text-gray-600">Cout TTC</p>
              <p className="text-lg font-semibold">
                {(
                  currentEnveloppe?.valorisation?.collecte.cout_ttc ?? 0
                ).toLocaleString('fr-FR', {
                  style: 'currency',
                  currency: 'EUR',
                })}
              </p>
            </div>
          </div>
        </div>
        <Divider className="my-2" />
        <div className="mt-3 py-2">
          <h3 className="mb-2 px-4 text-xl font-semibold text-blue-600">
            Expédition
          </h3>
          <div className="grid grid-cols-4 gap-3 px-4">
            <div></div>

            <div className="flex flex-col">
              <p className="text-sm text-gray-600">Cout HT</p>
              <p className="text-lg font-semibold">
                {(
                  currentEnveloppe?.valorisation?.expédition.cout_ht ?? 0
                ).toLocaleString('fr-FR', {
                  style: 'currency',
                  currency: 'EUR',
                })}
              </p>
            </div>
            <div className="flex flex-col">
              <p className="text-sm text-gray-600">Cout TVA</p>
              <p className="text-lg font-semibold">
                {(
                  currentEnveloppe?.valorisation?.expédition.cout_tva ?? 0
                ).toLocaleString('fr-FR', {
                  style: 'currency',
                  currency: 'EUR',
                })}
              </p>
            </div>
            <div className="flex flex-col">
              <p className="text-sm text-gray-600">Cout TTC</p>
              <p className="text-lg font-semibold">
                {(
                  currentEnveloppe?.valorisation?.expédition.cout_ttc ?? 0
                ).toLocaleString('fr-FR', {
                  style: 'currency',
                  currency: 'EUR',
                })}
              </p>
            </div>
          </div>
        </div>
        <Divider className="my-2" />
        <div className="mt-3 py-2">
          <h3 className="mb-2 px-4 text-xl font-semibold text-blue-600">
            Livraison
          </h3>
          <div className="grid grid-cols-4 gap-3 px-4">
            <div className="flex flex-col">
              <p className="text-sm text-gray-600">
                Taxe de livraison à recouvrer
              </p>
              <p className="text-lg font-semibold">
                {Number(
                  currentEnveloppe?.valorisation?.livraison
                    ?.taxe_livraison_a_recuperer ?? 0
                ).toLocaleString('fr-FR', {
                  style: 'currency',
                  currency: 'EUR',
                })}
              </p>
            </div>
            <div className="flex flex-col">
              <p className="text-sm text-gray-600">Taxe de livraison fixe</p>
              <p className="text-lg font-semibold">
                {(
                  currentEnveloppe?.valorisation?.livraison
                    .taxe_livraison_fixe ?? 0
                ).toLocaleString('fr-FR', {
                  style: 'currency',
                  currency: 'EUR',
                })}
              </p>
            </div>
            <div className="flex flex-col">
              <p className="text-sm text-gray-600">Taxe de livraison totale</p>
              <p className="text-lg font-semibold">
                {(
                  currentEnveloppe?.valorisation?.livraison
                    .taxe_livraison_totale ?? 0
                ).toLocaleString('fr-FR', {
                  style: 'currency',
                  currency: 'EUR',
                })}
              </p>
            </div>
            <div className="flex flex-col">
              <p className="text-sm text-gray-600">Cout total</p>
              <p className="text-lg font-semibold">
                {(
                  currentEnveloppe?.valorisation?.livraison.cout_total ?? 0
                ).toLocaleString('fr-FR', {
                  style: 'currency',
                  currency: 'EUR',
                })}
              </p>
            </div>
          </div>
        </div>

        <div
          className={`mt-3 rounded-b-lg border-t border-gray-200 py-2 ${isPostageValid ? (hasInvalidAffranchissements ? 'bg-yellow-50' : 'bg-green-50') : 'bg-red-50'}`}
        >
          <h3
            className={`mt-3 mb-2 px-4 text-xl font-semibold ${isPostageValid ? (hasInvalidAffranchissements ? 'text-yellow-500' : 'text-green-500') : 'text-red-500'}`}
          >
            Postage
          </h3>
          <div className="mb-3 grid grid-cols-4 gap-3 px-4">
            <div className="flex flex-col">
              <p className="text-sm text-gray-600">Cout de l'enveloppe</p>
              <p className="text-lg font-semibold">
                {(
                  currentEnveloppe?.valorisation?.postage.cout_enveloppe ?? 0
                ).toLocaleString('fr-FR', {
                  style: 'currency',
                  currency: 'EUR',
                })}
              </p>
            </div>
            <div className="flex flex-col">
              <p className="text-sm text-gray-600">
                Cout des affranchissements valides
              </p>
              <p className="text-lg font-semibold">
                {(
                  currentEnveloppe?.valorisation?.postage
                    ?.cout_affranchissements_valide ?? 0
                ).toLocaleString('fr-FR', {
                  style: 'currency',
                  currency: 'EUR',
                })}
              </p>
            </div>
            <div className="flex flex-col">
              <p className="text-sm text-gray-600">
                Nombre d'affranchissements invalides
              </p>
              <p
                className={`text-lg font-semibold ${
                  hasInvalidAffranchissements ? 'text-yellow-500' : ''
                }`}
              >
                {currentEnveloppe?.valorisation?.postage
                  ?.nb_affranchissements_invalides ?? 0}
              </p>
            </div>
            <div className="flex flex-col">
              <p className="text-sm text-gray-600">
                Montant sous affranchissement
              </p>
              <p
                className={`text-lg font-semibold ${
                  !isPostageValid ? 'text-red-500' : ''
                }`}
              >
                {(
                  currentEnveloppe?.valorisation?.postage
                    ?.montant_sous_affranchissement ?? 0
                ).toLocaleString('fr-FR', {
                  style: 'currency',
                  currency: 'EUR',
                })}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-8 flex flex-row items-center justify-end">
        <Button
          variant="outline"
          className="flex flex-row items-center pl-2"
          onClick={() => goBack()}
        >
          <TbChevronLeft className="mr-2 h-5 w-5" />
          Retour
        </Button>
        <Button
          variant="primary"
          className="ml-3 flex flex-row items-center pl-3"
          onClick={() => {
            handleTerminerEnveloppe()
          }}
        >
          <TbCheck className="mr-2 h-5 w-5" />
          Terminer
        </Button>
      </div>
    </>
  )
}
