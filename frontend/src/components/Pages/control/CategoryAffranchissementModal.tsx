// CategoryAffranchissementModal.tsx
'use client'

import React, { useState } from 'react'
import { Modal } from '@/components/Modal' // Your provided modal component
import {
  ModeleAffranchissement,
  AffranchissementCategory,
  EnveloppeAffranchissement,
} from '@/models/affranchissement'
import { BouttonAjoutAffranchissement } from '@/components/Control/BouttonAjoutAffranchissement'
import { getAffranchissementColors } from '@/utils/controlHelpers'
import { ControlAffranchissementModal } from './ControlAffranchissementModal'
import { ModeleAffranchissementToAffranchissement } from '@/utils/affranchissementHelpers'

interface CategoryAffModalProps {
  category: AffranchissementCategory
  onAdd: (aff: EnveloppeAffranchissement) => Promise<void>
  onClose: () => void
  open: boolean
}

export const CategoryAffranchissementModal: React.FC<CategoryAffModalProps> = ({
  category,
  onAdd,
  onClose,
  open,
}) => {
  // Local state that decides if the modal should transition to a detailed form view.
  const [selectedAff, setSelectedAff] = useState<EnveloppeAffranchissement | null>(
    null
  )

  const handleSelectAff = async (aff: ModeleAffranchissement) => {
    // Convert
    const affranchissement = ModeleAffranchissementToAffranchissement(aff)

    // If required fields are missing or the affranchissement already exists (i.e. has an id),
    // switch to the detailed form view.
    if (aff.informations.champs_requis.length > 0 && aff.categorie !== 'CODE') {
      setSelectedAff(affranchissement)
    } else {
      // Otherwise, add directly and close the modal.
      await onAdd(affranchissement)
      onClose()
    }
  }

  return (
    <Modal open={open} onClose={onClose} maxWidth="2xl">
      {!selectedAff ? (
        <>
          <div className="border-b p-4">
            <h3 className="mb-2 font-medium">
              <span
                className={`rounded-lg px-2 py-1 ${getAffranchissementColors(category.display1).text} ${getAffranchissementColors(category.display1).bg}`}
              >
                {category.id}
              </span>{' '}
              - {category.description}
            </h3>
          </div>
          <div className="p-4">
            <div
              className="mb-5 grid gap-3"
              style={{
                gridTemplateColumns: 'repeat(auto-fill, minmax(130px, auto))',
              }}
            >
              {category.types_affranchissements
                .sort(
                  (a: ModeleAffranchissement, b: ModeleAffranchissement) =>
                    (b.prix_unite_devise ?? 0) - (a.prix_unite_devise ?? 0)
                )
                .map((aff, index) => (
                  aff.afficher && (
                  <BouttonAjoutAffranchissement
                    key={index}
                    affranchissement={aff}
                    category={{ id: category.id, display1: category.display1 }}
                    onClick={handleSelectAff}
                  />)
                ))}
            </div>
          </div>
        </>
      ) : (
        <ControlAffranchissementModal
          initialAffranchissement={selectedAff}
          onClose={onClose}
          isOpen={open}
        />
      )}
    </Modal>
  )
}
