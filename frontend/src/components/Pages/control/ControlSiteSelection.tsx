import { Button } from '@/components/Forms/Button'
import { useQuery } from '@tanstack/react-query'
import { Site } from '@/models/site'
import { AxiosError } from 'axios'
import { SiteService } from '@/services/siteService'
import { useSite } from '@/hooks/useSite'

export const ControlSiteSelection = () => {
  const { setSelectedSite } = useSite()

  const { data: sitesData } = useQuery<Site[], AxiosError>({
    queryKey: ['sites'],
    queryFn: SiteService.getSites,
    staleTime: 1000 * 60 * 5,
  })

  return (
    <>
      <div className="mb-2 flex w-full">
        <h2 className="text-lg">
          Sur quel site sont localisés les plis ?
          <span className="text-red-600">&nbsp;*</span>
        </h2>
      </div>

      <div className="flex w-full flex-col">
        <div
          className="mt-2 mb-5 grid grid-cols-3 gap-3"
          style={{
            gridTemplateColumns: 'repeat(auto-fill, minmax(300px, auto))',
          }}
        >
          {sitesData?.map((site) => (
            <Button
              key={site.id}
              className="min-h-16"
              variant="outline"
              onClick={() => setSelectedSite(site)}
            >
              {site.nom}
            </Button>
          ))}
        </div>
      </div>
    </>
  )
}
