'use client'

import { useEffect, useState } from 'react'
import { Modal } from '@/components/Modal'
import { TbTrash } from 'react-icons/tb'
import { Button } from '@/components/Forms/Button'

type PhotoManagerModalProps = {
  photos: string[]
  onClose: () => void
  onDelete: (photoUrl: string) => Promise<void>
}

export const PhotoManagerModal = ({
  photos,
  onClose,
  onDelete,
}: PhotoManagerModalProps) => {
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [isDeleting, setIsDeleting] = useState(false)

  // Adjust selectedIndex when photos change
  useEffect(() => {
    if (photos.length === 0) {
      onClose()
    } else if (selectedIndex >= photos.length) {
      setSelectedIndex(photos.length - 1)
    }
  }, [photos, selectedIndex, onClose])

  const handleDelete = async () => {
    setIsDeleting(true)
    try {
      await onDelete(photos[selectedIndex])
    } finally {
      setIsDeleting(false)
    }
  }

  if (photos.length === 0) return null

  return (
    <Modal open={true} onClose={onClose} maxWidth="3xl" title="Photo Manager">
      <div className="flex flex-col items-center space-y-6 p-6">
        {/* Big Image Display */}
        <div className="relative flex w-full justify-center">
          <div className="flex max-h-[80vh] max-w-4xl items-center justify-center overflow-hidden rounded bg-gray-100 shadow">
            <img
              src={photos[selectedIndex]}
              alt={`Photo ${selectedIndex + 1}`}
              className="max-h-full max-w-full object-contain"
            />
          </div>
          {/* Delete button overlay */}
          <button
            onClick={handleDelete}
            disabled={isDeleting}
            className="absolute top-4 right-4 cursor-pointer rounded-full bg-red-600 p-2 text-white transition-colors hover:bg-red-700 focus:ring-2 focus:ring-red-400 focus:outline-none"
            aria-label="Supprimer la photo"
          >
            <TbTrash size={20} />
          </button>
        </div>

        {/* Thumbnails Gallery */}
        <div className="w-full overflow-x-auto">
          <div className="flex space-x-4">
            {photos.map((photo, index) => (
              <div
                key={index}
                className={`flex-shrink-0 cursor-pointer rounded border-2 ${
                  selectedIndex === index
                    ? 'border-blue-500'
                    : 'border-transparent hover:border-gray-300'
                } transition-colors`}
                onClick={() => setSelectedIndex(index)}
              >
                <img
                  src={photo}
                  alt={`Thumbnail ${index + 1}`}
                  className="h-24 w-32 rounded object-contain"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Footer Close Button */}
        <div className="flex w-full justify-center">
          <Button variant="primary" onClick={onClose}>
            Fermer
          </Button>
        </div>
      </div>
    </Modal>
  )
}
