import React from 'react'
import { Tb<PERSON>inus, TbPlus } from 'react-icons/tb'
import {
  getAffranchissementLabel,
  getAffranchissementDeviseLabel,
} from '@/utils/controlHelpers'
import { EnveloppeAffranchissement } from '@/models/affranchissement'
interface EnveloppeAffranchissementItemProps {
  affranchissement: EnveloppeAffranchissement
  onOpenUpdateModal: (affranchissement: EnveloppeAffranchissement) => void
  onChangeQuantity: (
    affranchissement: EnveloppeAffranchissement,
    quantity: number
  ) => void
  onChangeLegitStatus: (affranchissement: EnveloppeAffranchissement) => void
}

export const AffranchissementEnveloppeItem: React.FC<
  EnveloppeAffranchissementItemProps
> = ({
  affranchissement,
  onOpenUpdateModal,
  onChangeQuantity,
  onChangeLegitStatus,
}) => {
  return (
    <div className="flex flex-row border-b border-gray-200">
      <div className="flex-grow p-1">
        <div
          className={
            'cursor-pointer flex-col rounded-md p-1 pr-4 ' +
            (affranchissement.categorie === 'CODE'
              ? 'hover:bg-green-50'
              : affranchissement.categorie === 'MARI'
                ? 'hover:bg-red-50'
                : affranchissement.categorie === 'BEAU'
                  ? 'hover:bg-blue-50'
                  : affranchissement.categorie === 'MAFF'
                    ? 'hover:bg-yellow-50'
                    : 'hover:bg-gray-50')
          }
          onClick={() => onOpenUpdateModal(affranchissement)}
        >
          <div className="flex flex-row items-center text-sm font-medium">
            <div
              className={
                'mr-1 rounded-lg px-2 py-0.5 ' +
                (affranchissement.categorie === 'CODE'
                  ? 'bg-green-50 text-green-500'
                  : affranchissement.categorie === 'MARI'
                    ? 'bg-red-50 text-red-500'
                    : affranchissement.categorie === 'BEAU'
                      ? 'bg-blue-50 text-blue-500'
                      : affranchissement.categorie === 'MAFF'
                        ? 'bg-yellow-50 text-yellow-500'
                        : 'bg-gray-50 text-gray-500')
              }
            >
              {affranchissement.categorie}
            </div>
            <div>{getAffranchissementLabel(affranchissement)}</div>
            {affranchissement.code !== '' && (
              <div className="ml-2 max-w-[140px] truncate text-xs text-gray-300">
                {affranchissement.code}
              </div>
            )}
          </div>
          <div className="mt-0.5 ml-2 text-xs text-gray-400">
            {affranchissement.quantite} x{' '}
            {affranchissement.statut === 'INVALIDE'
              ? '0.00'
              : affranchissement.prix_unite_devise?.toFixed(2)}
            {getAffranchissementDeviseLabel(affranchissement.devise || '')} ={' '}
            <b>
              {affranchissement.statut === 'INVALIDE'
                ? '0.00'
                : (
                    (affranchissement.prix_unite_euros || 0) *
                    affranchissement.quantite
                  ).toFixed(2)}
              €
            </b>
          </div>
        </div>
      </div>

      <div className="flex flex-row items-center gap-4 p-2">
        <TbMinus
          className="cursor-pointer text-gray-400 hover:text-blue-700"
          onClick={() =>
            onChangeQuantity(affranchissement, affranchissement.quantite - 1)
          }
        />
        <TbPlus
          className={`cursor-pointer text-gray-400 ${affranchissement?.code !== '' ? 'opacity-25' : 'hover:text-blue-700'}`}
          onClick={() => {
            if (affranchissement?.code === '') {
              onChangeQuantity(affranchissement, affranchissement.quantite + 1)
            }
          }}
        />
        <button
          onClick={() => onChangeLegitStatus(affranchissement)}
          className={`flex h-10 w-10 cursor-pointer items-center justify-center rounded-md text-white ${
            affranchissement.statut === 'VALIDE'
              ? 'bg-green-500'
              : affranchissement.statut === 'INVALIDE'
                ? 'bg-red-500'
                : 'bg-gray-500'
          }`}
        >
          {affranchissement.statut === 'VALIDE'
            ? 'V'
            : affranchissement.statut === 'INVALIDE'
              ? 'F'
              : 'ND'}
        </button>
      </div>
    </div>
  )
}
