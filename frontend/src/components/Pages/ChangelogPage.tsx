import { useEffect, useState } from 'react'
import { TbFileText, TbCalendarEvent } from 'react-icons/tb'

interface ChangelogEntry {
  date: string
  changes: string[]
}

const ChangelogPage = () => {
  const [entries, setEntries] = useState<ChangelogEntry[]>([])
  const [loading, setLoading] = useState<boolean>(true)

  useEffect(() => {
    fetch('/CHANGELOG.md')
      .then((response) => response.text())
      .then((data) => {
        const parsedEntries = parseChangelog(data)
        setEntries(parsedEntries)
        setLoading(false)
      })
      .catch((error) => {
        console.error('Erreur lors du chargement du changelog:', error)
        setLoading(false)
      })
  }, [])

  const parseChangelog = (content: string): ChangelogEntry[] => {
    const entries: ChangelogEntry[] = []
    const lines = content.split('\n')
    
    let currentDate = ''
    let currentChanges: string[] = []
    
    lines.forEach(line => {
      const trimmedLine = line.trim()
      
      // Détection d'une nouvelle date (# date)
      if (trimmedLine.startsWith('# ')) {
        // Si on a déjà une date en cours, on sauvegarde l'entrée précédente
        if (currentDate) {
          entries.push({
            date: currentDate,
            changes: [...currentChanges]
          })
          currentChanges = []
        }
        currentDate = trimmedLine.substring(2)
      } 
      // Détection d'un changement (- changement)
      else if (trimmedLine.startsWith('- ')) {
        currentChanges.push(trimmedLine.substring(2))
      }
    })
    
    // Ajouter la dernière entrée
    if (currentDate && currentChanges.length > 0) {
      entries.push({
        date: currentDate,
        changes: currentChanges
      })
    }
    
    return entries
  }

  return (
    <div className="mx-auto max-w-4xl p-6">
      <div className="mb-6 flex items-center">
        <TbFileText className="mr-2 h-6 w-6 text-blue-600" />
        <h1 className="text-2xl font-bold text-gray-800">Journal des modifications</h1>
      </div>

      {loading ? (
        <div className="flex justify-center p-10">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-blue-500 border-t-transparent"></div>
        </div>
      ) : (
        <div className="space-y-6">
          {entries.map((entry, index) => (
            <div key={index} className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
              <div className="mb-4 flex items-center">
                <TbCalendarEvent className="mr-2 h-5 w-5 text-blue-500" />
                <h2 className="text-lg font-semibold text-gray-800">{entry.date}</h2>
              </div>
              <ul className="space-y-2 pl-5">
                {entry.changes.map((change, changeIndex) => (
                  <li key={changeIndex} className="text-gray-700">
                    <span className="mr-2 inline-block h-1.5 w-1.5 rounded-full bg-blue-500"></span>
                    {change}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export default ChangelogPage