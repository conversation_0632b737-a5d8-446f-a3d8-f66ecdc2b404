import { useState } from 'react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { Button } from '@/components/Forms/Button'
import Modal from '@/components/Forms/Modal'
import { CasierService } from '@/services/casierService'
import { SiteService } from '@/services/siteService'
import { toast } from 'react-hot-toast'
import { ApiError } from '@/core/ApiError'
import { Casier, DeplacementPlisRequest } from '@/models/casier'
import { TbAlertTriangle, TbArrowRight, TbBox, TbBuilding } from 'react-icons/tb'

type Props = {
  isOpen: boolean
  onClose: () => void
  casierSource: Casier | null
  isLoading?: boolean
}

export const DeplacementPlisModal = ({
  isOpen,
  onClose,
  casierSource
}: Props) => {
  const queryClient = useQueryClient()
  const [casierDestinationId, setCasierDestinationId] = useState<number | null>(null)

  // Récupérer tous les sites pour filtrer les casiers
  const { data: sites = [] } = useQuery({
    queryKey: ['sites'],
    queryFn: () => SiteService.getSites(),
    enabled: isOpen,
  })

  // Récupérer tous les casiers pour sélectionner la destination
  const { data: pageData } = useQuery({
    queryKey: ['casiers-all'],
    queryFn: () => CasierService.getCasiers(0, 1000), // Récupérer tous les casiers
    enabled: isOpen,
  })

  const casiers = pageData?.items ?? []

  // Filtrer les casiers disponibles sur d'autres sites
  const casiersDisponibles = casiers.filter(
    (casier) =>
      casier.id !== casierSource?.id && // Pas le casier source
      casier.site_id !== casierSource?.site_id && // Site différent
      casier.statut === 'DISPONIBLE' && // Disponible
      !casier.lot_expediteur_id // Pas de lot associé
  )

  const deplacementMutation = useMutation({
    mutationFn: (request: DeplacementPlisRequest) =>
      CasierService.deplacerPlis(request),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ['casiers'] })
      toast.success(response.message)
      onClose()
      setCasierDestinationId(null)
    },
    onError: (error: ApiError) => {
      const errorDetail = (error.body as any)?.detail || 'Erreur inconnue'
      toast.error(`Erreur lors du déplacement: ${errorDetail}`)
      console.error('Erreur:', error)
    },
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!casierSource || !casierDestinationId) {
      toast.error('Veuillez sélectionner un casier de destination')
      return
    }

    deplacementMutation.mutate({
      casier_source_id: casierSource.id,
      casier_destination_id: casierDestinationId,
    })
  }

  const handleClose = () => {
    setCasierDestinationId(null)
    onClose()
  }

  const casierDestination = casiersDisponibles.find(
    (c) => c.id === casierDestinationId
  )

  return (
    <Modal
      title="Déplacer les plis vers un autre site"
      isShown={isOpen}
      closeModal={handleClose}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Avertissement */}
        <div className="flex items-start rounded-lg bg-amber-50 p-4">
          <div className="flex-shrink-0">
            <TbAlertTriangle className="h-5 w-5 text-amber-500" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-amber-800">
              Attention - Déplacement de plis
            </h3>
            <div className="mt-2 text-sm text-amber-700">
              <p>
                Cette action déplacera <strong>tous les plis</strong> du casier
                source vers le casier de destination. Les plis changeront
                également de site.
              </p>
            </div>
          </div>
        </div>

        {/* Casier source */}
        <div className="rounded-lg border border-gray-200 p-4">
          <h4 className="mb-3 flex items-center text-sm font-medium text-gray-900">
            <TbBox className="mr-2 h-4 w-4" />
            Casier source
          </h4>
          {casierSource && (
            <div className="space-y-2">
              <div className="flex items-center text-sm">
                <span className="font-medium">Numéro:</span>
                <span className="ml-2">{casierSource.numero}</span>
              </div>
              <div className="flex items-center text-sm">
                <TbBuilding className="mr-1 h-4 w-4 text-gray-400" />
                <span className="font-medium">Site:</span>
                <span className="ml-2">{casierSource.site.nom}</span>
              </div>
              <div className="flex items-center text-sm">
                <span className="font-medium">Emplacement:</span>
                <span className="ml-2">
                  {casierSource.emplacement || 'Non défini'}
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Flèche */}
        <div className="flex justify-center">
          <TbArrowRight className="h-6 w-6 text-gray-400" />
        </div>

        {/* Sélection casier destination */}
        <div className="rounded-lg border border-gray-200 p-4">
          <h4 className="mb-3 flex items-center text-sm font-medium text-gray-900">
            <TbBox className="mr-2 h-4 w-4" />
            Casier de destination
          </h4>

          {casiersDisponibles.length === 0 ? (
            <p className="text-sm text-gray-500">
              Aucun casier disponible sur d'autres sites
            </p>
          ) : (
            <div className="space-y-3">
              <select
                value={casierDestinationId || ''}
                onChange={(e) =>
                  setCasierDestinationId(
                    e.target.value ? Number(e.target.value) : null
                  )
                }
                className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                required
              >
                <option value="">Sélectionner un casier</option>
                {sites.map((site) => {
                  const casiersParSite = casiersDisponibles.filter(
                    (c) => c.site_id === site.id
                  )
                  if (casiersParSite.length === 0) return null

                  return (
                    <optgroup key={site.id} label={site.nom}>
                      {casiersParSite.map((casier) => (
                        <option key={casier.id} value={casier.id}>
                          {casier.numero} - {casier.emplacement || 'Sans emplacement'}
                        </option>
                      ))}
                    </optgroup>
                  )
                })}
              </select>

              {/* Détails du casier sélectionné */}
              {casierDestination && (
                <div className="mt-3 rounded-md bg-gray-50 p-3">
                  <div className="space-y-1 text-sm">
                    <div className="flex items-center">
                      <TbBuilding className="mr-1 h-4 w-4 text-gray-400" />
                      <span className="font-medium">Site:</span>
                      <span className="ml-2">{casierDestination.site.nom}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Boutons d'action */}
        <div className="flex justify-end space-x-3">
          <Button
            type="button"
            variant="outline"
            onClick={handleClose}
            disabled={deplacementMutation.isPending}
          >
            Annuler
          </Button>
          <Button
            type="submit"
            color="blue"
            disabled={
              !casierDestinationId ||
              deplacementMutation.isPending ||
              casiersDisponibles.length === 0
            }
          >
            Déplacer les plis
          </Button>
        </div>
      </form>
    </Modal>
  )
}
