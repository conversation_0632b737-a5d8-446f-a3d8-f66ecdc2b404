// BouttonAjoutAffranchissement.tsx
'use client'

import React from 'react'
import {
  getAffranchissementColors,
  getAffranchissementLabel,
} from '@/utils/controlHelpers'
import { Button } from '@/components/Forms/Button'
import { ModeleAffranchissement } from '@/models/affranchissement'

interface BouttonAjoutAffranchissementProps {
  affranchissement: ModeleAffranchissement
  category: { id: string; display1: string }
  onClick: (affranchissement: ModeleAffranchissement) => void
}

export const BouttonAjoutAffranchissement: React.FC<
  BouttonAjoutAffranchissementProps
> = ({ affranchissement, category, onClick }) => {
  const colors = getAffranchissementColors(category.id)

  return (
    <Button
      variant="outline"
      color={colors.name as 'green' | 'red' | 'blue' | 'yellow' | 'gray'}
      className="flex min-h-[28px] flex-col items-center justify-center rounded-lg border border-gray-200 px-2 py-0.5 transition hover:bg-gray-50"
      onClick={() => onClick(affranchissement)}
    >
      <p className="text-xs">{getAffranchissementLabel(affranchissement)}</p>
      {affranchissement.prix_unite_devise && (
        <p className="text-xs text-gray-400">
          {Number(affranchissement.prix_unite_devise)?.toFixed(2)} €
        </p>
      )}
    </Button>
  )
}
