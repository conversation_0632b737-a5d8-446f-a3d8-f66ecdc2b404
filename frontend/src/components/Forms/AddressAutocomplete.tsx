import { useState, useEffect } from 'react'
import { Input } from './Input'
import { useDebounce } from '@/hooks/useDebounce'
import { Spinner } from './Spinner'

type AddressAutocompleteProps = {
  value: string
  onChange: (value: string) => void
  onSelect: (addressData: AddressData) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  postcodeFilter?: string
}

export type AddressData = {
  label: string
  housenumber?: string
  street?: string
  postcode?: string
  city?: string
  context?: string
  name?: string
}

export const AddressAutocomplete = ({
  value,
  onChange,
  onSelect,
  placeholder = 'Saisir une adresse',
  className = '',
  disabled = false,
  postcodeFilter,
}: AddressAutocompleteProps) => {
  const [isResultsVisible, setIsResultsVisible] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [searchResults, setSearchResults] = useState<AddressData[]>([])
  const debouncedValue = useDebounce(value, 300)

  useEffect(() => {
    const fetchAddresses = async () => {
      if (debouncedValue && debouncedValue.length >= 3) {
        setIsLoading(true)
        try {
          let url = `https://api-adresse.data.gouv.fr/search/?q=${encodeURIComponent(
            debouncedValue
          )}&limit=5`

          if (postcodeFilter) {
            url += `&postcode=${encodeURIComponent(postcodeFilter)}`
          }

          const response = await fetch(url)
          const data = await response.json()

          const formattedResults = data.features.map((feature: any) => ({
            label: feature.properties.label,
            housenumber: feature.properties.housenumber,
            street: feature.properties.street,
            postcode: feature.properties.postcode,
            city: feature.properties.city,
            context: feature.properties.context,
            name: feature.properties.name,
          }))

          setSearchResults(formattedResults)
        } catch (error) {
          console.error('Erreur lors de la récupération des adresses', error)
          setSearchResults([])
        } finally {
          setIsLoading(false)
        }
      } else {
        setSearchResults([])
      }
    }

    fetchAddresses()
  }, [debouncedValue, postcodeFilter])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value)
    if (e.target.value.length >= 3) {
      setIsResultsVisible(true)
    }
  }

  return (
    <div className={`relative ${className}`}>
      <Input
        type="text"
        placeholder={placeholder}
        autoComplete="off"
        className="w-full"
        value={value}
        onChange={handleInputChange}
        onFocus={() => value.length >= 3 && setIsResultsVisible(true)}
        disabled={disabled}
      />

      {isResultsVisible && (
        <>
          <div
            className="fixed top-0 left-0 z-10 h-full w-full"
            onClick={() => setIsResultsVisible(false)}
          ></div>
          <div className="absolute top-[42px] right-0 left-0 z-20 flex max-h-[300px] w-full flex-col gap-1 overflow-y-auto rounded-lg border border-gray-200 bg-white p-3">
            {value.length < 3 && (
              <p className="text-sm text-gray-500 italic">
                Veuillez saisir au moins 3 caractères
              </p>
            )}

            {value.length >= 3 && isLoading && (
              <div className="flex flex-row items-center text-sm text-gray-500 italic">
                <Spinner className="mr-2 h-4 w-4" /> Chargement des adresses...
              </div>
            )}

            {value.length >= 3 && !isLoading && searchResults.length === 0 && (
              <p className="text-sm">Aucun résultat</p>
            )}

            {searchResults.map((result, index) => (
              <div
                key={index}
                className="cursor-pointer rounded-md p-2 hover:bg-blue-50 hover:text-blue-500"
                onClick={() => {
                  onSelect(result)
                  setIsResultsVisible(false)
                }}
              >
                <div className="text-sm font-medium">{result.name}</div>
                <div className="text-xs text-gray-500">
                  {result.postcode} {result.city}
                </div>
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  )
}
