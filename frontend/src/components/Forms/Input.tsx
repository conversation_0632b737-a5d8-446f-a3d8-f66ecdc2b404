import React, { forwardRef } from 'react'

type InputProps = React.InputHTMLAttributes<HTMLInputElement>

export const Input = forwardRef<HTMLInputElement, InputProps>(
  (props, ref) => {
    return (
      <input
        ref={ref}
        {...props}
        className={`rounded-lg border border-gray-200 px-4 py-2 outline-3 outline-offset-0 outline-transparent focus:border-blue-500 focus:outline-blue-100 disabled:bg-slate-100 disabled:opacity-50 ${props.className}`}
      />
    )
  }
)

Input.displayName = 'Input'
