import {
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
  Transition,
} from '@headlessui/react'
import { Fragment } from 'react'
import { FiChevronDown } from 'react-icons/fi'

type SelectOption = {
  label: string
  value: string
}

type SelectProps = {
  onChange: (value: SelectOption) => void
  options: SelectOption[]
  label?: string
  value?: SelectOption | null
  className?: string
}

export const Select = ({
  value,
  onChange,
  options,
  className,
}: SelectProps) => {
  return (
    <Listbox value={value} onChange={onChange} as="div">
      {({ open }) => (
        <>
          <div className="relative">
            <ListboxButton
              className={`flex h-[42px] w-full flex-row items-center rounded-lg border border-gray-200 px-4 py-2 outline-3 outline-offset-0 outline-transparent focus:border-blue-500 focus:outline-blue-100 disabled:bg-slate-100 disabled:opacity-50 ${className}`}
            >
              <div
                className={`flex flex-grow truncate ${
                  value?.label ? 'text-gray-800' : 'text-gray-400'
                }`}
              >
                {value?.label ?? 'Selectionner une valeur'}
              </div>

              <FiChevronDown
                className={`transform transition-transform ${
                  open ? 'rotate-180' : ''
                }`}
              />
            </ListboxButton>

            <Transition
              show={open}
              as={Fragment}
              leave="transition ease-in duration-100"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <ListboxOptions
                className={`absolute top-[38px] z-50 mt-1 mb-4 max-h-[350px] min-w-[200px] overflow-auto overflow-y-auto rounded-md border border-gray-200 bg-white p-2`}
              >
                {options.map((option) => (
                  <ListboxOption
                    key={option.value}
                    value={option}
                    className={({ focus }) =>
                      `${
                        focus || option.label === value?.label
                          ? 'bg-blue-50 text-blue-500'
                          : 'text-gray-900'
                      } ${
                        option.label === value?.label
                          ? 'font-semibold'
                          : 'font-normal'
                      } mt-1 cursor-pointer rounded-md px-3 py-1.5 select-none`
                    }
                  >
                    <span>{option.label}</span>
                  </ListboxOption>
                ))}
              </ListboxOptions>
            </Transition>
          </div>
        </>
      )}
    </Listbox>
  )
}
