import React, { useEffect } from 'react'

type Props = {
  closeModal: () => void
  isShown: boolean
  children: React.ReactNode
  title: string | React.ReactNode
}

export const Modal = ({ closeModal, isShown, children, title }: Props) => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        closeModal()
      }
    }

    if (isShown) {
      document.addEventListener('keydown', handleKeyDown)
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isShown, closeModal])

  if (isShown) {
    return (
      <div className="fixed top-0 left-0 z-40 flex h-svh w-svw flex-col items-center overflow-visible">
        <div
          className="fixed top-0 right-0 bottom-0 left-0 z-50 bg-black/25 backdrop-blur-sm"
          onClick={closeModal}
        ></div>
        <div className="z-60 m-2 mt-24 mb-16 flex max-h-[80%] min-w-[700px] flex-col rounded-lg border border-gray-200 bg-white">
          <div
            className={`border-b border-gray-200 px-4 text-lg font-semibold ${
              typeof title === 'string' ? 'py-4' : 'py-2'
            }`}
          >
            {title}
          </div>
          <div className="flex-grow overflow-visible p-4 pt-2">{children}</div>
        </div>
      </div>
    )
  } else {
    return <></>
  }
}

export default Modal
