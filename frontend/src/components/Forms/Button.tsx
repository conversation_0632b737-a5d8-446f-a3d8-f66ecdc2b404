type ButtonProps = {
  children: React.ReactNode
  variant?: 'outline' | 'primary' | 'light'
  color?: 'blue' | 'green' | 'red' | 'yellow' | 'gray' | 'primary'
  forceShowColor?: boolean
} & React.ButtonHTMLAttributes<HTMLButtonElement>

export const Button = ({
  variant = 'outline',
  color = 'primary',
  children,
  forceShowColor = false,
  ...props
}: ButtonProps) => {
  const defaultClasses =
    'py-0.5 px-2.5 rounded-lg min-h-10 font-medium transition-all duration-150 outline-offset-0 outline-3 outline-transparent'

  const cursorClasses = props.disabled ? 'cursor-not-allowed' : 'cursor-pointer'

  const activeClasses = {
    red: `active:outline-red-100`,
    green: `active:outline-green-100`,
    blue: `active:outline-blue-100`,
    yellow: `active:outline-yellow-100`,
    gray: `active:outline-gray-100`,
    primary: `active:outline-primary`,
  }

  const disabledClasses = {
    outline: 'bg-slate-100 text-gray-400 border border-gray-200',
    primary: 'bg-slate-100 text-gray-400',
    light: 'bg-slate-100 text-gray-400 border border-gray-200',
  }

  const variantClasses = {
    outline: {
      red: 'border border-gray-200',
      green: 'border border-gray-200',
      blue: 'border border-gray-200',
      yellow: 'border border-gray-200',
      gray: 'border border-gray-200',
      primary: 'border border-gray-200 text-primary',
    },
    primary: {
      red: `bg-red-700 text-white`,
      green: `bg-green-700 text-white`,
      blue: `bg-blue-700 text-white`,
      yellow: `bg-yellow-700 text-white`,
      gray: `bg-gray-700 text-white`,
      primary: `bg-primary text-white`,
    },
    light: {
      red: `bg-red-100 text-red-700 border border-red-500`,
      green: `bg-green-100 text-green-700 border border-green-500`,
      blue: `bg-blue-100 text-blue-700 border border-blue-500`,
      yellow: `bg-yellow-100 text-yellow-700 border border-yellow-500`,
      gray: `bg-gray-100 text-gray-700 border border-gray-500`,
      primary: `bg-primary text-white`,
    },
  }

  const hoverVariantClasses = {
    outline: {
      red: `hover:border-red-500`,
      green: `hover:border-green-500`,
      blue: `hover:border-blue-500`,
      yellow: `hover:border-yellow-500`,
      gray: `hover:border-gray-500`,
      primary: `hover:border-primary`,
    },
    primary: {
      red: `hover:bg-red-600`,
      green: `hover:bg-green-600`,
      blue: `hover:bg-blue-600`,
      yellow: `hover:bg-yellow-600`,
      gray: `hover:bg-gray-600`,
      primary: `hover:bg-primary`,
    },
    light: {
      red: `hover:bg-red-50`,
      green: `hover:bg-green-50`,
      blue: `hover:bg-blue-50`,
      yellow: `hover:bg-yellow-50`,
      gray: `hover:bg-gray-50`,
      primary: `hover:bg-primary`,
    },
  }

  return (
    <button
      {...props}
      className={`${defaultClasses} ${cursorClasses} ${
        (!props.disabled || forceShowColor) && variantClasses[variant][color]
      } ${props.className} ${!props.disabled && activeClasses[color]} ${
        !(props.disabled && !forceShowColor) &&
        hoverVariantClasses[variant][color]
      } ${props.disabled && !forceShowColor && disabledClasses[variant]}`}
    >
      {children}
    </button>
  )
}
