import { FieldErrors } from 'react-hook-form'

interface FormErrorsProps {
  errors: FieldErrors
  className?: string
}

export default function FormErrors({
  errors,
  className = '',
}: FormErrorsProps) {
  const errorMessages = Object.values(errors).map((error) => error?.message)

  if (errorMessages.length === 0) return null

  return (
    <div className={`list-disc ${className}`}>
      {errorMessages.map((message, index) => (
        <li key={index} className="text-red-600 text-sm">
          {message?.toString()}
        </li>
      ))}
    </div>
  )
}
