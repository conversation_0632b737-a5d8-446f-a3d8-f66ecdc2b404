import { useEffect, useState, useRef } from 'react'
import { TbPlus, TbX } from 'react-icons/tb'
import { Spinner } from './Spinner'
import { useDebounce } from '@/hooks/useDebounce'
import type { CancelablePromise } from '@/core/CancelablePromise'

/**
 * Generic async search‑select input with optional inline “create new” action.
 */
export type SearchSelectorProps<T = any> = {
  placeholder: string
  /** Currently selected item (or undefined). */
  selectedValue: T | undefined
  /** Setter for the selected value */
  setSelectedValue: (value: T | undefined) => void
  /** Promise that resolves to the list of results for the search string. */
  searchResultPromise: (search: string) => CancelablePromise<T[]>
  /** Render a single result inside the list */
  resultItem: (item: T) => React.ReactNode
  /** Render the chip label once an item is selected */
  selectedItemLabel: (item: T) => string
  /** Callback when “create new” is clicked (omit to disable) */
  onCreateNew?: (search: string) => void
  /** Optional custom label for the create option (ignored if onCreateNew is undefined) */
  createNewLabel?: string
  className?: string
  disabled?: boolean
  minInput?: number
}

export function SearchSelector<T = any>({
  placeholder,
  selectedValue,
  setSelectedValue,
  searchResultPromise,
  resultItem,
  selectedItemLabel,
  onCreateNew,
  createNewLabel = 'Créer un nouvel élément',
  disabled,
  className,
  minInput = 3,
}: SearchSelectorProps<T>) {
  // ----------------------------------------------------------------------------------
  const [search, setSearch] = useState<string>('')
  const debounced = useDebounce(search, 400)

  const [results, setResults] = useState<T[]>([])
  const [loading, setLoading] = useState(false)
  const [dropdownOpen, setDropdownOpen] = useState(false)

  const inputRef = useRef<HTMLInputElement>(null)

  // Focus the input if the chip is cleared
  useEffect(() => {
    if (!selectedValue) inputRef.current?.focus()
  }, [selectedValue])

  // Fetch results whenever debounced search changes
  useEffect(() => {
    async function fetchResults() {
      if (debounced.length < minInput) return
      setLoading(true)
      try {
        const data = await searchResultPromise(debounced)
        setResults(data)
      } finally {
        setLoading(false)
      }
    }
    fetchResults()
  }, [debounced]) // eslint-disable-line react-hooks/exhaustive-deps

  /** UI helpers */
  const nothingTyped = search.length < minInput
  const noResults =
    !loading && debounced.length >= minInput && results.length === 0
  const canCreate = !!onCreateNew && debounced.length >= minInput

  const handleCreateNew = () => {
    if (onCreateNew) {
      onCreateNew(search); // Passer la valeur de recherche actuelle
      setDropdownOpen(false);
    }
  };

  // ----------------------------------------------------------------------------------
  return (
    <div className={`relative ${className ?? ''}`}>
      {/** ==================== INPUT OR CHIP ==================== */}
      {selectedValue ? (
        <div className="flex h-10 w-full items-center justify-between rounded-md border border-gray-300 px-2 text-sm">
          <span className="font-semibold text-blue-600">
            {selectedItemLabel(selectedValue)}
          </span>
          <TbX
            className="ml-2 h-4 w-4 cursor-pointer text-red-500 hover:text-red-700"
            onClick={() => setSelectedValue(undefined)}
          />
        </div>
      ) : (
        <input
          ref={inputRef}
          placeholder={placeholder}
          value={search}
          disabled={disabled}
          onFocus={() => setDropdownOpen(true)}
          onChange={(e) => {
            setSearch(e.target.value)
            if (!dropdownOpen) setDropdownOpen(true)
          }}
          className={`rounded-lg border border-gray-200 px-4 py-2 outline-3 outline-offset-0 outline-transparent focus:border-blue-500 focus:outline-blue-100 disabled:bg-slate-100 disabled:opacity-50 ${className}`}
        />
      )}

      {/** ==================== DROPDOWN ==================== */}
      {dropdownOpen && !selectedValue && (
        <>
          <div
            className="fixed inset-0 z-10"
            onClick={() => setDropdownOpen(false)}
          />

          <div className="absolute z-20 mt-1 max-h-80 w-full overflow-y-auto rounded-lg border border-gray-200 bg-white p-3 shadow-lg">
            {/***** guidance *****/}
            {nothingTyped && (
              <p className="text-sm text-gray-500 italic">
                Tapez au moins {minInput} caractères
              </p>
            )}
            {loading && !nothingTyped && (
              <div className="flex items-center text-sm text-gray-500">
                <Spinner className="mr-2 h-4 w-4" /> Chargement…
              </div>
            )}
            {noResults && <p className="text-sm">Aucun résultat</p>}

            {/***** results list *****/}
            {results.map((item) => (
              <div
                key={(item as any).id ?? JSON.stringify(item)}
                className="cursor-pointer rounded-md p-2 hover:bg-blue-50 hover:text-blue-600"
                onClick={() => {
                  setSelectedValue(item)
                  setDropdownOpen(false)
                }}
              >
                {resultItem(item)}
              </div>
            ))}

            {/***** create‑new option *****/}
            {canCreate && (
              <div
                className="cursor-pointer border-t border-gray-100 p-2 hover:bg-gray-50"
                onClick={handleCreateNew}
              >
                <div className="flex items-center gap-2 text-sm text-blue-600">
                  <TbPlus className="h-4 w-4" />
                  {createNewLabel}
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  )
}
