import useAuth from '@/hooks/useAuth'
import { Role } from '@/models/usersModel'
import { Link } from '@tanstack/react-router'
import {
  Tb<PERSON>ser,
  TbUsersGroup,
  TbDeviceMobile,
  TbChevronLeft,
  TbChevronRight,
  TbListCheck,
  TbChartBar,
  TbScan,
  TbBrandStackoverflow,
  TbStack2,
} from 'react-icons/tb'
import { useState } from 'react'
import clsx from 'clsx'

export const SideMenu = () => {
  const { user, logout } = useAuth()
  const [collapsed, setCollapsed] = useState(true)

  // Menu items based on user role
  const guestMenuItems = [
    {
      label: 'Dashboard',
      icon: <TbChartBar className="h-6 w-6" />,
      route: '/',
    },
  ]
  const standardMenuItems = [
    {
      label: 'Dashboard',
      icon: <TbChartBar className="h-6 w-6" />,
      route: '/',
    },
    {
      label: 'Contrôler les plis',
      icon: <TbScan className="h-6 w-6" />,
      route: '/control',
    },

    {
      label: 'Associer mobile',
      icon: <TbDeviceMobile className="h-6 w-6" />,
      route: '/qrcode',
    },
    {
      label: 'Plis traitées',
      icon: <TbStack2 className="h-6 w-6" />,
      route: '/enveloppes',
    },
  ]
  const adminMenuItems = [
    ...standardMenuItems,
    {
      label: 'Lots expéditeurs',
      icon: <TbBrandStackoverflow className="h-6 w-6" />,
      route: '/lots-expediteurs',
    },
    {
      label: 'Gestion des utilisateurs',
      icon: <TbUsersGroup className="h-6 w-6" />,
      route: '/users',
    },
    {
      label: 'Gestion des règles métier',
      icon: <TbListCheck className="h-6 w-6" />,
      route: '/rules',
    },
  ]
  const managerMenuItems = adminMenuItems

  const menuItems =
    user?.role === Role.ADMIN
      ? adminMenuItems
      : user?.role === Role.MANAGER
        ? managerMenuItems
        : user?.role === Role.USER
          ? standardMenuItems
          : guestMenuItems

  return (
    <nav
      // flex-shrink-0 ensures the nav keeps its width
      className={`relative flex h-full flex-shrink-0 flex-col overflow-auto border-r border-gray-200 bg-white p-2 transition-all duration-300 ${
        collapsed ? 'w-[100px]' : 'w-[300px] xl:p-4'
      }`}
    >
      {/* Toggle button */}
      <button
        onClick={() => setCollapsed(!collapsed)}
        className="absolute top-2 right-2 z-10 rounded-full bg-white p-1 text-gray-500 hover:bg-gray-100 hover:text-gray-700"
        aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
      >
        {collapsed ? (
          <TbChevronRight className="h-4 w-4" />
        ) : (
          <TbChevronLeft className="h-4 w-4" />
        )}
      </button>

      {/* Logo */}
      <div className="my-4 w-full xl:my-8">
        <img
          className="mx-auto h-auto w-full max-w-32"
          loading="lazy"
          alt="La Poste"
          src="https://le-groupe-laposte.cdn.prismic.io/le-groupe-laposte/8312d45c-311b-4dab-aee5-12b140882197_Logo.svg"
        />
      </div>

      {/* Nav links */}
      <div className="flex-1">
        {menuItems.map((item, index) => (
          <Link
            key={index}
            to={item.route}
            className={`mb-2 flex min-h-[72px] flex-col items-center justify-center rounded-lg p-2 text-gray-700 transition-all duration-300 ease-[var(--ease-snappy)] hover:bg-blue-50 hover:text-blue-700 ${
              !collapsed
                ? 'xl:min-h-[unset] xl:flex-row xl:justify-start xl:px-4 xl:py-3'
                : ''
            }`}
            activeProps={{ className: 'bg-blue-50 !text-blue-700' }}
          >
            <span className={`mb-1 ${!collapsed ? 'xl:mr-3 xl:mb-0' : ''}`}>
              {item.icon}
            </span>
            <span
              className={clsx('text-center', collapsed ? 'text-xs' : 'text-sm')}
            >
              {item.label}
            </span>
          </Link>
        ))}
      </div>

      {/* Logout / user info */}
      <div className="flex flex-col gap-1">
        <Link
          to="/changelog"
          className="text-center text-xs text-gray-500 transition-colors hover:text-blue-600"
        >
          Journal des modifications
        </Link>
        <div
          className={`mb-2 flex min-h-[72px] cursor-pointer flex-col items-center justify-center rounded-lg p-1 text-gray-700 transition-all duration-300 ease-[var(--ease-snappy)] hover:bg-blue-50 hover:text-blue-700 ${
            !collapsed
              ? 'xl:min-h-[unset] xl:flex-row xl:justify-start xl:px-2 xl:py-1'
              : ''
          }`}
          onClick={logout}
        >
          <span
            className={`mb-1 rounded-full bg-blue-50 p-2 text-blue-700 ${
              !collapsed ? 'xl:mr-3 xl:mb-0' : ''
            }`}
          >
            <TbUser className="h-6 w-6" />
          </span>
          {(!collapsed || window.innerWidth < 1280) && (
            <div className="w-full truncate text-center text-xs xl:w-[unset] xl:text-base">
              {user?.email}
            </div>
          )}
        </div>
      </div>
    </nav>
  )
}

export default SideMenu
