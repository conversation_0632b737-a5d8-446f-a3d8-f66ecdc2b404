export const getStatusPliConfig = (status: string) => {
  const configs = {
    EDITION: {
      bg: 'bg-blue-50',
      text: 'text-blue-700',
      border: 'border-blue-200',
      icon: '✏️',
      label: 'En édition',
    },
    TERMINEE: {
      bg: 'bg-emerald-50',
      text: 'text-emerald-700',
      border: 'border-emerald-200',
      icon: '✅',
      label: 'Terminée',
    },
    SOUS_AFFRANCHI: {
      bg: 'bg-orange-50',
      text: 'text-orange-700',
      border: 'border-orange-200',
      icon: '⚠️',
      label: 'Sous-affranchissement',
    },
    FRAUDULEUSE: {
      bg: 'bg-red-50',
      text: 'text-red-700',
      border: 'border-red-200',
      icon: '🚫',
      label: 'Frauduleuse',
    },
    PAUSE: {
      bg: 'bg-amber-50',
      text: 'text-amber-700',
      border: 'border-amber-200',
      icon: '⏸️',
      label: 'En pause',
    },
    VALORISE: {
      bg: 'bg-purple-50',
      text: 'text-purple-700',
      border: 'border-purple-200',
      icon: '💰',
      label: 'Valoris<PERSON>',
    },
    default: {
      bg: 'bg-slate-50',
      text: 'text-slate-700',
      border: 'border-slate-200',
      icon: '○',
      label: status || 'Inconnu',
    },
  }
  return configs[status as keyof typeof configs] || configs.default
}

export const getStatusLotConfig = (status: string) => {
  const configs = {
    OUVERT: {
      bg: 'bg-green-50',
      text: 'text-green-700',
      border: 'border-green-200',
      icon: '📂',
      label: 'Ouvert',
    },
    NOTIFIE: {
      bg: 'bg-amber-50',
      text: 'text-amber-700',
      border: 'border-amber-200',
      icon: '📣',
      label: 'Notifié',
    },
    PAIEMENT_RECU: {
      bg: 'bg-blue-50',
      text: 'text-blue-700',
      border: 'border-blue-200',
      icon: '💳',
      label: 'Paiement reçu',
    },
    TRAITE: {
      bg: 'bg-emerald-50',
      text: 'text-emerald-700',
      border: 'border-emerald-200',
      icon: '✅',
      label: 'Traité',
    },
    ANNULE: {
      bg: 'bg-red-50',
      text: 'text-red-700',
      border: 'border-red-200',
      icon: '🚫',
      label: 'Annulé',
    },
    default: {
      bg: 'bg-slate-50',
      text: 'text-slate-700',
      border: 'border-slate-200',
      icon: '○',
      label: status || 'Inconnu',
    },
  }
  return configs[status as keyof typeof configs] || configs.default
}
