import {
  EnveloppeAffranchissement,
  ModeleAffranchissement,
} from '@/models/affranchissement'
import { Product } from '@/models/product'

const products = [
  {
    id: 'LV',
    name: 'Lettre Verte',
  },
  {
    id: 'LINT',
    name: 'Lettre Internationale',
  },
  {
    id: 'PPI',
    name: 'Petit paquet International',
  },
  {
    id: 'LSP',
    name: 'Lettre Simple',
  },
  {
    id: 'NON_DETERMINE',
    name: 'Non déterminée',
  },
]

export const getProductLabel = (product: Product) => {
  const producLabet = products.find((p) => p.id === product)
  return producLabet?.name
}

export const getAffranchissementDeviseLabel = (devise: string) => {
  switch (devise) {
    case 'EURO':
      return `€`
    case 'FRANCS':
      return `F`
    case 'ANCIEN_FRANCS':
      return `A`
    default:
      return `€`
  }
}

export const getAffranchissementColors = (code: string) => {
  switch (code) {
    case 'CODE':
      return { name: 'green', text: 'text-green-500', bg: 'bg-green-50' }
    case 'MARI':
      return { name: 'red', text: 'text-red-500', bg: 'bg-red-50' }
    case 'BEAU':
      return { name: 'blue', text: 'text-blue-500', bg: 'bg-blue-50' }
    case 'MAFF':
      return { name: 'yellow', text: 'text-yellow-500', bg: 'bg-yellow-50' }
    case 'T':
      return { name: 'gray', text: 'text-white', bg: 'bg-blue-500' }
    case 'VIGN':
      return { name: 'pink', text: 'text-pink-500', bg: 'bg-pink-50' }
    default:
      return { name: 'gray', text: 'text-gray-500', bg: 'bg-gray-50' }
  }
}

export const getAffranchissementLabel = (
  affranchissement: EnveloppeAffranchissement | ModeleAffranchissement
) => {
  if (affranchissement.type === 'VAL' && affranchissement.prix_unite_devise) {
    return `${Number(affranchissement.prix_unite_devise)?.toFixed(2)}${getAffranchissementDeviseLabel(
      affranchissement?.devise
    )}`
  } else if (
    affranchissement.type === 'VAL' &&
    !affranchissement.prix_unite_devise
  ) {
    return getAffranchissementDeviseLabel(affranchissement.devise)
  } else {
    return affranchissement.informations?.label
  }
}
