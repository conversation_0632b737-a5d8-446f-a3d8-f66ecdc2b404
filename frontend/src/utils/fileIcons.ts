// fileIcons.ts
import { IconType } from "react-icons";
import {
  FaFile,
  FaFilePdf,
  FaFileWord,
  FaFileExcel,
  FaFileImage,
} from "react-icons/fa";

interface FileIconConfig {
  icon: IconType;
  colorClass: string;
}

// 1. Define a map from extension to { icon, colorClass }
const fileIconMap: Record<string, FileIconConfig> = {
  pdf: {
    icon: FaFilePdf, // or SiAdobeacrobatreader
    colorClass: "text-red-600",
  },
  doc: {
    icon: FaFileWord,
    colorClass: "text-blue-600",
  },
  docx: {
    icon: FaFileWord,
    colorClass: "text-blue-600",
  },
  xls: {
    icon: FaFileExcel,
    colorClass: "text-green-600",
  },
  xlsx: {
    icon: FaFileExcel,
    colorClass: "text-green-600",
  },
  png: {
    icon: FaFileImage,
    colorClass: "text-pink-600",
  },
  jpg: {
    icon: FaFileImage,
    colorClass: "text-pink-600",
  },
  jpeg: {
    icon: FaFileImage,
    colorClass: "text-pink-600",
  },
  webp: {
    icon: FaFileImage,
    colorClass: "text-pink-600",
  },
  // Add more extensions if needed
};

// 2. A fallback if the extension is not known
const defaultIcon: FileIconConfig = {
  icon: FaFile,
  colorClass: "text-gray-500",
};

/**
 * Takes a file name or extension (e.g. "document.pdf" or "pdf")
 * and returns an object containing:
 *  - icon:   IconType (from react-icons)
 *  - colorClass: The Tailwind class for color styling
 */
export function getFileIcon(fileExtensionOrName: string): FileIconConfig {
  // Extract extension from full file name if needed:
  const ext = fileExtensionOrName.toLowerCase().replace(/^.*\./, "");
  //   ^ This strips everything up to the last "."
  //   (e.g. "myDoc.pdf" => "pdf", "photo.JPG" => "jpg")

  return fileIconMap[ext] ?? defaultIcon;
}
