import { EnveloppeAffranchissement, ModeleAffranchissement } from "@/models/affranchissement";
/**
 * Vérifie si tous les champs requis d'un affranchissement sont remplis
 * @param affranchissement L'affranchissement à vérifier
 * @returns Un tableau des champs manquants, vide si tous les champs sont remplis
 */
export const getChampsManquants = (
  affranchissement: EnveloppeAffranchissement
): string[] => {

  // if(affranchissement.informations.complet) {
  //   return [];
  // }

  return affranchissement.informations.champs_requis.filter(
    (champ) => {
      const valeur = affranchissement[champ as keyof EnveloppeAffranchissement];
      if(valeur && typeof valeur === 'object') {
        if('id' in valeur) {
          return valeur.id === "NON_DETERMINE";
        }
        // Vérifier si c'est un dictionnaire vide
        return Object.keys(valeur).length === 0;
      }
      return !valeur || valeur === 0 //|| valeur === "NON_DETERMINE";
    }
  );
};

export const affranchissementToJSON = (affranchissement: EnveloppeAffranchissement) => {
  return {
    categorie: affranchissement.categorie,
    type: affranchissement.type,
    nature: affranchissement.nature,
    statut: affranchissement.statut,
    quantite: affranchissement.quantite,
    prix_unite_devise: affranchissement.prix_unite_devise,
    code: affranchissement.code,
    devise: affranchissement.devise,
    origine: affranchissement.origine
  }
}

export const ModeleAffranchissementToAffranchissement = (modele: ModeleAffranchissement) => {
  return {
    id: null,
    categorie: modele.categorie,
    type: modele.type,
    sous_type: '',
    nature: "NON_DETERMINE",
    statut: modele.categorie === 'CODE' ? 'INVALIDE' : 'VALIDE',
    quantite: 1,
    prix_unite_devise: modele.prix_unite_devise,
    prix_unite_euros: 0,
    code: '',
    devise: modele.devise,
    origine: modele.origine,
    donnees: {},
    verifications: [],
    informations: modele.informations || { champs_requis: [] }
  } as EnveloppeAffranchissement
}
