// Function to format the code with ellipsis in the middle
export const formatCodeWithEllipsis = (
  code: string,
  maxLength: number = 13
): string => {
  if (!code || code.length <= maxLength) {
    return code || 'Non spécifié'
  }

  // Calculate how many characters to keep on each side
  const sideLength = Math.floor((maxLength - 3) / 2)
  const leftSide = code.substring(0, sideLength)
  const rightSide = code.substring(code.length - sideLength)

  return `${leftSide}...${rightSide}`
}
