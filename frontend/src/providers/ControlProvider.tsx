import React, { useEffect, useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import toast from 'react-hot-toast'
import { isLoggedIn } from '@/hooks/useAuth'
import { Destination } from '@/models/destination'
import { Nature } from '@/models/nature'
import { Sender } from '@/models/sender'
import { EnumerationService } from '@/services/enumerationService'
import { EnveloppeService } from '@/services/enveloppeService'
import { ControlContext } from '@/contexts/Control'
import { ApiError } from '@/core/ApiError'
import { useSite } from '@/hooks/useSite'
import { ValidationResponse } from '@/models/affranchissement'
import { Enveloppe } from '@/models/enveloppe'

type Props = {
  children: React.ReactNode
}

export function ControlProvider({ children }: Props) {
  const { setSelectedSite } = useSite()

  const queryClient = useQueryClient()
  // Instead of a local state for the enveloppe, we use a query with a dynamic key.
  // The key is either a numeric ID or the string 'edition' for the edition mode.
  const [enveloppeKey, setEnveloppeKey] = useState<number | 'edition'>(
    'edition'
  )
  const [scannedCodes, setScannedCodes] = useState<Set<string>>(new Set())
  const [scanResults, setScanResults] = useState<
    Map<string, ValidationResponse>
  >(new Map())

  const [isFinished, setIsFinished] = useState(false)

  const [selectedSender, setSelectedSender] = useState<Sender | undefined>()
  const [selectedDestination, setSelectedDestination] = useState<
    Destination | undefined
  >()
  const [weight, setWeight] = useState<number | undefined>()
  const [isOverWeight, setIsOverWeight] = useState(false)
  const [isOverSized, setIsOverSized] = useState(false)

  const [selectedDestinationEnveloppe, setSelectedDestinationEnveloppe] =
    useState<string | undefined>()

  const { data: currentEnveloppe } = useQuery({
    queryKey: ['enveloppe', enveloppeKey],
    queryFn: async () => {
      if (enveloppeKey === 'edition') {
        return await EnveloppeService.getEnveloppeEdition()
      } else {
        return await EnveloppeService.getEnveloppe(enveloppeKey as number)
      }
    },
  })

  useEffect(() => {
    if (currentEnveloppe) {
      if (currentEnveloppe.poids) setWeight(currentEnveloppe.poids)
      if (currentEnveloppe.site) {
        setSelectedSite(currentEnveloppe.site)
      }
      if (currentEnveloppe.surdimensionne !== undefined) {
        setIsOverSized(currentEnveloppe.surdimensionne)
      }
      if (currentEnveloppe.expediteur)
        setSelectedSender(currentEnveloppe.expediteur)
      // Zone Tarifaire
      if (currentEnveloppe.destination)
        setSelectedDestination(currentEnveloppe.destination)
      // Destination enveloppe
      if (currentEnveloppe.destination_enveloppe)
        setSelectedDestinationEnveloppe(currentEnveloppe.destination_enveloppe)
    }
  }, [currentEnveloppe, setSelectedSite])

  // ── MUTATIONS ──

  const terminerEnveloppeMutation = useMutation({
    mutationFn: async (enveloppeId: number) => {
      return await EnveloppeService.postTerminerEnveloppe(enveloppeId)
    },
    onSuccess: (returnedEnveloppe: Enveloppe) => {
      setEnveloppeKey(returnedEnveloppe.id)
      queryClient.setQueryData(['enveloppe', enveloppeKey], returnedEnveloppe)
      setIsFinished(true)
      toast.success('Enveloppe terminée avec succès')
    },
    onError: (err: ApiError) => {
      const errDetail = (err.body as any)?.detail
      toast.error(`Erreur lors de la terminaison. ${errDetail}`)
    },
  })

  // ── HANDLER FUNCTIONS ──

  const handleIsOverSizedChange = () => {
    setIsOverSized((prev) => !prev)
  }

  const handleTerminerEnveloppe = async () => {
    if (!currentEnveloppe) return
    if (!selectedSender) {
      toast.error(
        "Veuillez renseigner l'expéditeur avant de terminer l'enveloppe"
      )
      return
    }
    if (!selectedDestinationEnveloppe) {
      toast.error(
        "Veuillez renseigner la destination de l'enveloppe avant de terminer l'enveloppe"
      )
      return
    }

    if (isOverSized && isOverWeight && !selectedDestination) {
      toast.error(
        "Veuillez renseigner le destinataire avant de terminer l'enveloppe"
      )
      return
    }

    if (!weight || (weight != undefined && weight < 1)) {
      toast.error('Veuillez renseigner un poids supérieur à 0')
      return
    }

    terminerEnveloppeMutation.mutate(currentEnveloppe.id)
  }

  // Helpers to reset state.

  const resetAll = () => {
    // For currentEnveloppe, we simply reset the query key to edition.
    setEnveloppeKey('edition')
    setSelectedSender(undefined)
    setSelectedDestination(undefined)
    setWeight(undefined)
    setIsOverWeight(false)
    setIsOverSized(false)
  }

  const handleNouvelleEnveloppe = () => {
    // Quand l'utilisateur clique sur : Nouveau pli après avoir terminer une enveloppe
    setIsFinished(false)
    setEnveloppeKey('edition')
  }

  useEffect(() => {
    if (weight !== undefined) {
      setIsOverWeight(weight > 2000)
      if (weight > 2000) {
        setIsOverSized(true)
      }
    }
  }, [weight])

  // ── INITIAL DATA QUERIES ──

  const { data: destinationsData } = useQuery<string[], AxiosError>({
    queryKey: ['destinations'],
    queryFn: EnumerationService.getDestinations,
    enabled: isLoggedIn(),
    staleTime: 1000 * 60 * 5,
  })

  const { data: naturesData } = useQuery<Nature[], AxiosError>({
    queryKey: ['natures'],
    queryFn: EnumerationService.getNatures,
    enabled: isLoggedIn(),
    staleTime: 1000 * 60 * 5,
  })

  return (
    <ControlContext.Provider
      value={{
        isFinished,
        setIsFinished,
        currentEnveloppe,
        selectedSender,
        setSelectedSender,
        selectedDestination,
        setSelectedDestination,
        weight,
        setWeight,
        isOverWeight,
        setIsOverWeight,
        isOverSized,
        setIsOverSized,
        handleIsOverSizedChange,
        handleTerminerEnveloppe,
        resetAll,
        destinationsData,
        naturesData,
        scannedCodes,
        scanResults,
        setScannedCodes,
        setScanResults,
        enveloppeKey,
        setEnveloppeKey,
        handleNouvelleEnveloppe,
        setSelectedDestinationEnveloppe,
        selectedDestinationEnveloppe,
      }}
    >
      {children}
    </ControlContext.Provider>
  )
}
