import { LotExpediteur } from "./lotExpediteurs"
import { Site } from "./site"

export type Casier = {
  id: number
  numero: string
  emplacement: string | null
  statut: 'DISPONIBLE' | 'OCCUPE'
  site_id: number
  site: Site
  lot_expediteur: LotExpediteur | null
  lot_expediteur_id: number | null
  date_attribution: string | null
  date_liberation: string | null
  created_at: string
  updated_at: string
}

export type DeplacementPlisRequest = {
  casier_source_id: number
  casier_destination_id: number
}

export type DeplacementPlisResponse = {
  nb_plis_deplaces: number
  casier_source: {
    id: number
    numero: string
    site: string
  }
  casier_destination: {
    id: number
    numero: string
    site: string
  }
  lots_expediteur_impactes: number
  message: string
}