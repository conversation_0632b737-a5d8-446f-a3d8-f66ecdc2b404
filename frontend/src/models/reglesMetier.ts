export enum TypeAffranchissementEnum {
  S10 = 'S10',
  TIMBRE = 'TIMBRE', // ajoute d'autres si nécessaire
}

export enum TypeRegleMetierEnum {
  SEQUENCE = 'SEQUENCE',
  VALEUR = 'VALEUR',
}

export interface RegleMetier {
  id?: number
  cle: string
  type_affranchissement: TypeAffranchissementEnum
  type_regle: TypeRegleMetierEnum
  valeur: Record<string, any>
  created_at?: string
  updated_at?: string
  active: boolean
}
