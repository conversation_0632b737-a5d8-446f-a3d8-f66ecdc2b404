import { EnveloppeAffranchissement } from "./affranchissement"
import { Enveloppe } from "./enveloppe"

export type HTTPValidationError = {
  detail?: Array<ValidationError>
}

export type Message = {
  message: string
}

export type ValidationError = {
  loc: Array<string | number>
  msg: string
  type: string
}

export type EnveloppeFullData = {
  enveloppe: Enveloppe
  affranchissement: EnveloppeAffranchissement
  frontend_action: string
}
