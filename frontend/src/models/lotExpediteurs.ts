import { Site } from './site'
import { Enveloppe, Valorisation } from './enveloppe'
import { <PERSON>asier } from './casier'
import { Sender } from './sender'

export interface LotExpediteur {
  id: number
  expediteur: Sender
  casier: Casier | null
  casiers: Casier[]
  statut: string
  site: Site

  nombre_enveloppes: number

  created_at: string
  updated_at: string
  valorisation: Valorisation
}

export interface LotExpediteurFilters {
  statut?: string
  expediteur_id?: number
  site_id?: number
  sort_by?: string
  sort_order?: string
}

export interface LotExpediteurDetails {
  id: number
  statut: string
  casier?: Casier
  casiers: Casier[]
  expediteur: Sender
  site: Site
  nombre_enveloppes: number
  valorisation: any
  created_at: string
  enveloppes: Enveloppe[]
}
