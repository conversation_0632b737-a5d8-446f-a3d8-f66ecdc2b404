import { Enveloppe } from './enveloppe'

export type ModeleAffranchissement = {
  categorie: string
  type: string
  color: string
  variant: string
  devise: 'EURO' | 'FRANCS' | 'ANCIEN_FRANCS'
  prix_unite_devise: number | null
  afficher: boolean
  informations: AffranchissementInformations
  origine?: string
}

export type EnveloppeAffranchissement = {
  id: number | null
  categorie: string
  type: string
  sous_type: string
  nature: string | undefined
  prix_unite_devise: number | null
  prix_unite_euros: number | null
  devise: 'EURO' | 'FRANCS' | 'ANCIEN_FRANCS'
  quantite: number
  code: string
  statut: 'VALIDE' | 'INVALIDE'
  donnees: any
  verifications: AffranchissementVerification[]
  informations: AffranchissementInformations
  origine?: string 
}

export type AffranchissementCategory = {
  id: string
  display1: string
  display2: string
  description: string
  color: string
  variant: string
  types_affranchissements: ModeleAffranchissement[]
}

export type AffranchissementVerification = {
  affranchissement_id: number
  donnees: any
  id: number
  message: string
  statut: 'VALIDE' | 'INVALIDE' | 'NON_DETERMINE'
  type: string
}

export type AffranchissementDonnee = {
  id: string
  segmented: string
  content: {
    cp: string | null
    origin: string | null
    number: string | null
    key: string | null
    socode: string | null
    country: string | null
    ascode: string | null
    customer: string | null
  }
}

export type AffranchissementInformations = {
  complet: boolean
  champs_requis: string[]
  label: string
}

export type Body_post_affranchissement = {
  categorie?: string
  type?: string
  prix_unite_devise?: number | null
  devise?: 'EURO' | 'FRANCS' | 'ANCIEN_FRANCS'
  nature?: string
  quantite?: number
  code?: string
  statut?: 'VALIDE' | 'INVALIDE'
  origine?: string
}

export type Body_update_affranchissement = {
  statut?: 'VALIDE' | 'INVALIDE'
  quantite?: number
  nature?: string
}

export type Response_post_affranchissement = {
  enveloppe: Enveloppe
  affranchissement: EnveloppeAffranchissement
  frontend_action: string
}

export interface VerifierAffranchissement {
  code: string
}

export interface AffranchissementItemPublic {
  id?: number
  code: string
  type?: string
  montant?: number
  verifications?: Array<{
    statut: 'VALIDE' | 'INVALIDE' | 'EN_ATTENTE'
    type: string
    message: string
  }>
}

export interface ValidationResponse {
  is_valid: boolean
  affranchissement: AffranchissementItemPublic | null
  message: string | null
}
