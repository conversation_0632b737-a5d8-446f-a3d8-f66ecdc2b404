import { Site } from './site'

export enum Role {
  GUEST = 'guest',
  USER = 'user',
  MANAGER = 'manager',
  ADMIN = 'admin',
}

export type UserCreate = {
  email: string
  role: Role
  first_name?: string
  last_name?: string
  password: string
  site_id: number
}

export type UserPublic = {
  email: string
  first_name?: string
  last_name?: string
  full_name: string
  role: Role
  site_id: number
  id: number
  is_active: boolean
  site: Site
}

export type UserRegister = {
  email: string
  password: string
  first_name?: string
  last_name?: string
}

export type UserUpdate = {
  email?: string
  first_name?: string
  last_name?: string
  password?: string
  role?: Role
  site_id?: number
  is_active?: boolean
}

export type UserUpdateMe = {
  first_name?: string
  last_name?: string
  email?: string
}

export type UsersPaginatedPublic = {
  items: Array<UserPublic>
  total_items: number
  total_pages: number
  current_page: number
  page_size: number
}

export type QrCodeToken = string
