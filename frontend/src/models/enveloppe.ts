import { Destination } from './destination'
import { EnveloppeAffranchissement } from './affranchissement'
import { Sender } from './sender'
import { Site } from './site'
import { Casier } from './casier'
import { UserPublic } from './usersModel'

export type PhotoEnveloppe = {
  id: number
  public_url: string
  format: string
  enveloppe_id: number
}

export type Valorisation = {
  postage: {
    cout_enveloppe: number
    cout_affranchissements_valide: number
    nb_affranchissements_invalides: number
    montant_sous_affranchissement: number
    presence_affranchissements_invalide: boolean
  }
  livraison: {
    taxe_livraison_a_recuperer: number
    taxe_livraison_fixe: number
    taxe_livraison_totale: number
    cout_total: number
  }
  collecte: {
    cout_ht: number
    cout_tva: number
    cout_ttc: number
  }
  expédition: {
    cout_ht: number
    cout_tva: number
    cout_ttc: number
  }
}

export type EnveloppeInformations = {
  complet: boolean
  modifiable: boolean
  nombre_affranchissements_invalides: number
  prix_affranchissements_valide: number
}

export enum StatutEnveloppe {
  EDITION = 'EDITION',
  TERMINEE = 'TERMINEE',
  SOUS_AFFRANCHI = 'SOUS_AFFRANCHI',
  FRAUDULEUSE = 'FRAUDULEUSE',
  PAUSE = 'PAUSE',
  VALORISE = 'VALORISE',
}

export type Enveloppe = {
  id: number
  created_at: string
  updated_at: string
  statut: StatutEnveloppe
  poids: number
  surpoids: boolean
  surdimensionne: boolean
  id_migration: string
  produit: string
  traitement_lot_enveloppes_id: number
  expediteur_id: number
  destination_id: number
  site_id: number
  user_id: number
  valorisation: Valorisation | null
  affranchissements?: EnveloppeAffranchissement[]
  site?: Site
  expediteur?: Sender
  destination?: Destination
  photos?: PhotoEnveloppe[]
  informations: EnveloppeInformations
  user: UserPublic
  casier?: Casier
  destination_enveloppe: string
}

export type Body_post_enveloppe = {
  id?: number
  poids: number
  surpoids: boolean
  surdimensionne: boolean
  destination_enveloppe: string
  site_id?: number
  destination_id?: number
  expediteur_id?: number
}
