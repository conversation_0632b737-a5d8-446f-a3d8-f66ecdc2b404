import type { CancelablePromise } from '@/core/CancelablePromise'
import { OpenAPI } from '@/core/OpenAPI'
import { request as __request } from '@/core/request'
import { PaginatedResponse } from '@/models/paginatedResponse'
import { RegleMetier } from '@/models/reglesMetier'

export class ReglesMetierService {
  static list(params?: {
    search?: string
    skip?: number
    limit?: number
  }): CancelablePromise<PaginatedResponse<RegleMetier>> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/admin/regles-metier/',
      query: {
        search: params?.search,
        skip: params?.skip ?? 0,
        limit: params?.limit ?? 20,
      },
      mediaType: 'application/json',
    })
  }

  static get(id: number): CancelablePromise<RegleMetier> {
    return __request(OpenAPI, {
      method: 'GET',
      url: `/api/v1/admin/regles-metier/${id}`,
      mediaType: 'application/json',
    })
  }

  static create(body: RegleMetier): CancelablePromise<RegleMetier> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/api/v1/admin/regles-metier/',
      body,
      mediaType: 'application/json',
    })
  }

  static update(id: number, body: RegleMetier): CancelablePromise<RegleMetier> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: `/api/v1/admin/regles-metier/${id}`,
      body,
      mediaType: 'application/json',
    })
  }

  static remove(id: number): CancelablePromise<RegleMetier> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: `/api/v1/admin/regles-metier/${id}`,
      mediaType: 'application/json',
    })
  }
}
