import type { CancelablePromise } from '@/core/CancelablePromise'
import { OpenAPI } from '@/core/OpenAPI'
import { request as __request } from '@/core/request'
import {
  LotExpediteur,
  LotExpediteurDetails,
  LotExpediteurFilters,
} from '@/models/lotExpediteurs'
import { PaginatedResponse } from '@/models/paginatedResponse'

export class LotExpediteurService {
  public static getLots(
    page: number = 1,
    pageSize: number = 10,
    filters?: LotExpediteurFilters
  ): CancelablePromise<PaginatedResponse<LotExpediteur>> {
    const query: Record<string, string> = {
      page: page.toString(),
      pageSize: pageSize.toString(),
    }

    if (filters?.statut) {
      query.statut = filters.statut
    }

    if (filters?.expediteur_id) {
      query.expediteur_id = filters.expediteur_id.toString()
    }

    if (filters?.site_id) {
      query.site_id = filters.site_id.toString()
    }

    if (filters?.sort_by) {
      query.sort_by = filters.sort_by
    }

    if (filters?.sort_order) {
      query.sort_order = filters.sort_order
    }

    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/lots-expediteurs/',
      query,
      mediaType: 'application/json',
    })
  }

  public static getLotByIdToCsv(id: number): CancelablePromise<Blob> {
    return __request(OpenAPI, {
      method: 'GET',
      url: `/api/v1/lots-expediteurs/${id}/csv`,
      responseType: 'blob',
    })
  }

  public static getLotById(
    id: number
  ): CancelablePromise<LotExpediteurDetails> {
    return __request(OpenAPI, {
      method: 'GET',
      url: `/api/v1/lots-expediteurs/${id}`,
    })
  }

  public static updateLotStatus(
    lotId: number,
    statut: string
  ): CancelablePromise<LotExpediteur> {
    return __request(OpenAPI, {
      method: 'PATCH',
      url: `/api/v1/lots-expediteurs/${lotId}/statut`,
      query: {
        statut: statut,
      },
      mediaType: 'application/json',
    })
  }

  public static getLotValorisation(
    lotId: number
  ): CancelablePromise<Record<string, any>> {
    return __request(OpenAPI, {
      method: 'GET',
      url: `/api/v1/lots-expediteurs/${lotId}/valorisation`,
      mediaType: 'application/json',
    })
  }

  public static createLot(
    expediteur_id: number
  ): CancelablePromise<LotExpediteur> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/api/v1/lots-expediteurs?expediteur_id=' + expediteur_id,
      mediaType: 'application/json',
    })
  }

  public static requestNewCasier(
    lotId: number
  ): CancelablePromise<LotExpediteur> {
    return __request(OpenAPI, {
      method: 'POST',
      url: `/api/v1/lots-expediteurs/${lotId}/nouveau-casier`,
      mediaType: 'application/json',
    })
  }
}
