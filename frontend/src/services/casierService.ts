import type { CancelablePromise } from '@/core/CancelablePromise'
import { OpenAPI } from '@/core/OpenAPI'
import { request as __request } from '@/core/request'
import { Casier, DeplacementPlisRequest, DeplacementPlisResponse } from '@/models/casier'
import { PaginatedResponse } from '@/models/paginatedResponse'

export class CasierService {
  /**
   * Récupère tous les casiers avec pagination et filtrage optionnel
   * @param skip Nombre d'éléments à sauter
   * @param limit Nombre maximum d'éléments à retourner
   * @param search Terme de recherche optionnel
   * @returns Liste des casiers
   * @throws ApiError
   */
  public static getCasiers(
    skip: number = 0,
    limit: number = 10,
    search?: string
  ): CancelablePromise<PaginatedResponse<Casier>> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/casiers',
      query: {
        skip: skip.toString(),
        limit: limit.toString(),
        search: search,
      },
      mediaType: 'application/json',
    })
  }

  /**
   * Récupère les casiers disponibles pour le site de l'utilisateur connecté
   * @returns Liste des casiers disponibles
   * @throws ApiError
   */
  public static getCasiersDisponibles(): CancelablePromise<Casier[]> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/casiers/disponibles',
      mediaType: 'application/json',
    })
  }

  /**
   * Récupère un casier par son ID
   * @param casierId ID du casier à récupérer
   * @returns Le casier demandé
   * @throws ApiError
   */
  public static getCasierById(casierId: number): CancelablePromise<Casier> {
    return __request(OpenAPI, {
      method: 'GET',
      url: `/api/v1/casiers/${casierId}`,
      mediaType: 'application/json',
    })
  }

  /**
   * Crée un nouveau casier
   * @param casier Données du casier à créer
   * @returns Le casier créé
   * @throws ApiError
   */
  public static createCasier(casier: Casier): CancelablePromise<Casier> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/api/v1/casiers',
      body: casier,
      mediaType: 'application/json',
    })
  }

  /**
   * Met à jour un casier existant
   * @param casierId ID du casier à mettre à jour
   * @param casier Nouvelles données du casier
   * @returns Le casier mis à jour
   * @throws ApiError
   */
  public static updateCasier(
    casierId: number,
    casier: Casier
  ): CancelablePromise<Casier> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: `/api/v1/casiers/${casierId}`,
      body: casier,
      mediaType: 'application/json',
    })
  }

  /**
   * Supprime un casier
   * @param casierId ID du casier à supprimer
   * @returns Le casier supprimé
   * @throws ApiError
   */
  public static deleteCasier(casierId: number): CancelablePromise<Casier> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: `/api/v1/casiers/${casierId}`,
      mediaType: 'application/json',
    })
  }

  /**
   * Déplace l'ensemble des plis d'un casier vers un autre casier sur un site différent
   * @param request Données de la requête de déplacement
   * @returns Résultat du déplacement
   * @throws ApiError
   */
  public static deplacerPlis(
    request: DeplacementPlisRequest
  ): CancelablePromise<DeplacementPlisResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/api/v1/casiers/deplacer-plis',
      body: request,
      mediaType: 'application/json',
    })
  }
}
