import { tokenService } from "./tokenService";

// Configuration Keycloak
export const keycloakConfig = {
  url: import.meta.env.VITE_KEYCLOAK_URL || 'https://keycloak.iam.aks.sandbox.innovation-laposte.io/auth/',
  realm: import.meta.env.VITE_KEYCLOAK_REALM || 'prod',
  clientId: import.meta.env.VITE_KEYCLOAK_CLIENT_ID || 'vigie',
  redirectURL: window.location.origin + "/login-callback"
};

// Fonctions utilitaires pour Keycloak
export const keycloakService = {
  buildAuthUrl: (kc_idp_hint = 'gaia'): string => {
    const { url, realm, clientId, redirectURL } = keycloakConfig;
    const authUrl = `${url}/realms/${realm}/protocol/openid-connect/auth`;
    
    const params = new URLSearchParams({
      client_id: clientId,
      redirect_uri: redirectURL,
      response_type: 'code',
      scope: 'openid profile email',
      kc_idp_hint: kc_idp_hint
    });
    
    return `${authUrl}?${params.toString()}`;
  },

  buildLogoutUrl: (redirectUri = `${window.location.origin}/login`): string => {
    const { url, realm } = keycloakConfig;
    const logoutUrl = `${url}/realms/${realm}/protocol/openid-connect/logout`;
    
    const params = new URLSearchParams({
      redirect_uri: redirectUri,
    });
    
    return `${logoutUrl}?${params.toString()}`;
  },

  exchangeCodeForToken: async (code: string): Promise<any> => {
    const { url, realm, clientId, redirectURL } = keycloakConfig;
    const tokenUrl = `${url}/realms/${realm}/protocol/openid-connect/token`;
    
    const params = new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: clientId,
      code: code,
      redirect_uri: redirectURL,
      client_secret: import.meta.env.VITE_KEYCLOAK_CLIENT_SECRET || '',
    });
    
    console.log('Paramètres d\'échange de token:', Object.fromEntries(params.entries()));
    
    const response = await fetch(tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: params.toString(),
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      console.error('Erreur de réponse Keycloak:', errorData);
      throw new Error(`Erreur lors de l'échange du code: ${errorData.error_description || 'Erreur inconnue'}`);
    }
    
    const tokenResponse = await response.json();    
    tokenService.store(tokenResponse.access_token, tokenResponse.refresh_token);
  },

  refreshToken: async (): Promise<any> => {
    const { url, realm, clientId } = keycloakConfig;
    const tokenUrl = `${url}/realms/${realm}/protocol/openid-connect/token`;
    
    const params = new URLSearchParams({
      grant_type: 'refresh_token',
      client_id: clientId,
      refresh_token: tokenService.get('refresh_token') || ''
    });
    
    try {
      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params.toString(),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Erreur lors du rafraîchissement du token:', errorData);
        throw new Error(`Échec du rafraîchissement: ${errorData.error_description || 'Erreur inconnue'}`);
      }
      
      const tokenResponse = await response.json();    
      tokenService.store(tokenResponse.access_token, tokenResponse.refresh_token);
    } catch (error) {
      console.error('Erreur lors de la requête de rafraîchissement:', error);
      throw error;
    }
  }
}; 