import { Message } from '@/models/othersModels'
import { CancelablePromise } from '../core/CancelablePromise'
import { OpenAPI } from '../core/OpenAPI'
import { UpdatePassword } from '@/models/authenticationModels'
import {
  UserCreate,
  UserPublic,
  UserRegister,
  UsersPaginatedPublic,
  UserUpdate,
  UserUpdateMe,
  Role,
} from '@/models/usersModel'
import { request as __request } from '../core/request'

export type TDataReadUsers = {
  page?: number
  pageSize?: number
  search?: string
  role?: Role
  site_id?: number
}

export type TDataCreateUser = {
  requestBody: UserCreate
}

export type TDataUpdateUserMe = {
  requestBody: UserUpdateMe
}

export type TDataUpdatePasswordMe = {
  requestBody: UpdatePassword
}

export type TDataRegisterUser = {
  requestBody: UserRegister
}

export type TDataReadUserById = {
  userId: number
}

export type TDataGetQrCodeToken = {
  token: string
}

export type TDataUpdateUser = {
  requestBody: UserUpdate
  userId: number
}

export type TDataDeleteUser = {
  userId: number
}

export class UsersService {
  /**
   * Read Users
   * Retrieve users with pagination and filtering.
   * @returns UsersPaginatedPublic Successful Response
   * @throws ApiError
   */
  public static readUsers(
    data: TDataReadUsers = {}
  ): CancelablePromise<UsersPaginatedPublic> {
    const { page = 1, pageSize = 10, search, role, site_id } = data

    const query: Record<string, any> = {
      page,
      pageSize,
    }

    if (search) query.search = search
    if (role) query.role = role
    if (site_id) query.site_id = site_id

    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/users/',
      query,
      errors: {
        422: `Validation Error`,
      },
    })
  }

  /**
   * Create User
   * Create new user.
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static createUser(
    data: TDataCreateUser
  ): CancelablePromise<UserPublic> {
    const { requestBody } = data
    console.log('requestBody', requestBody)
    return __request(OpenAPI, {
      method: 'POST',
      url: '/api/v1/users/',
      body: requestBody,
      mediaType: 'application/json',
      errors: {
        422: `Validation Error`,
      },
    })
  }

  /**
   * Read User Me
   * Get current user.
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static readUserMe(): CancelablePromise<UserPublic> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/users/me',
    })
  }

  /**
   * Delete User Me
   * Delete own user.
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static deleteUserMe(): CancelablePromise<Message> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/api/v1/users/me',
    })
  }

  /**
   * Update User Me
   * Update own user.
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static updateUserMe(
    data: TDataUpdateUserMe
  ): CancelablePromise<UserPublic> {
    const { requestBody } = data
    return __request(OpenAPI, {
      method: 'PATCH',
      url: '/api/v1/users/me',
      body: requestBody,
      mediaType: 'application/json',
      errors: {
        422: `Validation Error`,
      },
    })
  }

  /**
   * Update Password Me
   * Update own password.
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static updatePasswordMe(
    data: TDataUpdatePasswordMe
  ): CancelablePromise<Message> {
    const { requestBody } = data
    return __request(OpenAPI, {
      method: 'PATCH',
      url: '/api/v1/users/me/password',
      body: requestBody,
      mediaType: 'application/json',
      errors: {
        422: `Validation Error`,
      },
    })
  }

  /**
   * Register User
   * Create new user without the need to be logged in.
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static registerUser(
    data: TDataRegisterUser
  ): CancelablePromise<UserPublic> {
    const { requestBody } = data
    return __request(OpenAPI, {
      method: 'POST',
      url: '/api/v1/users/signup',
      body: requestBody,
      mediaType: 'application/json',
      errors: {
        422: `Validation Error`,
      },
    })
  }

  /**
   * Read User By Id
   * Get a specific user by id.
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static readUserById(
    data: TDataReadUserById
  ): CancelablePromise<UserPublic> {
    const { userId } = data
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/users/{user_id}',
      path: {
        user_id: userId,
      },
      errors: {
        422: `Validation Error`,
      },
    })
  }

  /**
   * Read User By Id
   * Get a specific user by id.
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static getQrCodeToken(): CancelablePromise<string> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/users/me/qrcode',
      errors: {
        422: `Validation Error`,
      },
    })
  }

  /**
   * Update User
   * Update a user.
   * @returns UserPublic Successful Response
   * @throws ApiError
   */
  public static updateUser(
    data: TDataUpdateUser
  ): CancelablePromise<UserPublic> {
    const { requestBody, userId } = data
    return __request(OpenAPI, {
      method: 'PATCH',
      url: '/api/v1/users/{user_id}',
      path: {
        user_id: userId,
      },
      body: requestBody,
      mediaType: 'application/json',
      errors: {
        422: `Validation Error`,
      },
    })
  }

  /**
   * Delete User
   * Delete a user.
   * @returns Message Successful Response
   * @throws ApiError
   */
  public static deleteUser(data: TDataDeleteUser): CancelablePromise<Message> {
    const { userId } = data
    return __request(OpenAPI, {
      method: 'DELETE',
      url: '/api/v1/users/{user_id}',
      path: {
        user_id: userId,
      },
      errors: {
        422: `Validation Error`,
      },
    })
  }

  /**
   * Get QR Code Blob
   * Récupère le QR code de l'utilisateur au format blob
   * @returns Blob QR Code de l'utilisateur
   * @throws ApiError
   */
  public static getQrCodeBlob(
    newToken: boolean = false
  ): CancelablePromise<Blob> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/users/me/qrcode',
      query: {
        new_token: newToken,
      },
      responseType: 'blob',
      errors: {
        422: `Validation Error`,
      },
    })
  }
}
