import type { CancelablePromise } from '@/core/CancelablePromise'
import { OpenAPI } from '@/core/OpenAPI'
import { request as __request } from '@/core/request'
import { Body_post_senders, Sender } from '@/models/sender'

export class SenderService {
  /**
   * Login Access Token
   * OAuth2 compatible token login, get an access token for future requests
   * @returns Token Successful Response
   * @throws ApiError
   */
  public static getSenders(search: string | undefined): CancelablePromise<Sender[]> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/expediteurs?search=' + search,
      mediaType: 'application/json',
    })
  }

  public static postSenders(sender: Body_post_senders): CancelablePromise<Sender> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/api/v1/expediteurs',
      body: sender,
      mediaType: 'application/json',
    })
  }

  public static searchSimilarSenders(searchParams: {
    nom?: string;
    adresse?: string;
    ville?: string;
    code_postal?: string;
  }): CancelablePromise<Sender[]> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/api/v1/expediteurs/search',
      body: searchParams,
      mediaType: 'application/json',
    })
  }
}
