// Constantes
const TOKEN_STORAGE = {
    ACCESS: "access_token",
    REFRESH: "refresh_token",
    EXTERNAL_ACCESS: "external_access_token"
};
  
const REFRESH_MARGIN_MS = 60000; // 1 minute avant expiration

// Fonctions utilitaires pour la gestion des tokens
export const tokenService = {
    store: (accessToken: string, refreshToken?: string): void => {
      localStorage.setItem(TOKEN_STORAGE.ACCESS, accessToken);
      if (refreshToken) localStorage.setItem(TOKEN_STORAGE.REFRESH, refreshToken);
    },
    
    get: (tokenType: string): string | null => localStorage.getItem(tokenType),
    
    remove: (): void => {
      localStorage.removeItem(TOKEN_STORAGE.ACCESS);
      localStorage.removeItem(TOKEN_STORAGE.REFRESH);
      localStorage.removeItem(TOKEN_STORAGE.EXTERNAL_ACCESS);
    },
    
    isStored: (): boolean => !!localStorage.getItem(TOKEN_STORAGE.ACCESS),
    
    getExpiration: (token: string): number => {
      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        return payload.exp * 1000; // Convertir en millisecondes
      } catch (e) {
        return 0;
      }
    },
    
    isExpired: (safetyMarginMs = REFRESH_MARGIN_MS): boolean => {
      const token = tokenService.get(TOKEN_STORAGE.ACCESS);
      if (!token) return true;
      
      const expiration = tokenService.getExpiration(token);
      return expiration <= (Date.now() + safetyMarginMs);
    }
  };