import type { CancelablePromise } from '@/core/CancelablePromise'
import { OpenAPI } from '@/core/OpenAPI'
import { request as __request } from '@/core/request'
import type { 
VerifierAffranchissement,
  ValidationResponse 
} from '@/models/affranchissement'

export class AffranchissementService {
  public static verifierAffranchissement(
    data: VerifierAffranchissement
  ): CancelablePromise<ValidationResponse> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/api/v1/affranchissements/verifier',
      body: data,
      mediaType: 'application/json',
    })
  }
} 