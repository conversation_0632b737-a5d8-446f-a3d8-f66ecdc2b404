import type { CancelablePromise } from '@/core/CancelablePromise'
import { OpenAPI } from '@/core/OpenAPI'
import { request as __request } from '@/core/request'

export class PappersService {
  public static searchCompanies(q: string, page: number = 1, per_page: number = 10): CancelablePromise<any> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/expediteurs/pappers/search',
      query: {
        q,
        page,
        per_page
      },
      mediaType: 'application/json',
    })
  }
}