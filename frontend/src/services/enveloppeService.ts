import type { CancelablePromise } from '@/core/CancelablePromise'
import { OpenAPI } from '@/core/OpenAPI'
import { request as __request } from '@/core/request'
import {
  Body_post_affranchissement,
  Body_update_affranchissement,
  Response_post_affranchissement,
} from '@/models/affranchissement'
import { Body_post_enveloppe, Enveloppe, PhotoEnveloppe } from '@/models/enveloppe'
import { PaginatedResponse } from '@/models/paginatedResponse'

export class EnveloppeService {
  public static getEnveloppe(id: number): CancelablePromise<Enveloppe> {
    return __request(OpenAPI, {
      method: 'GET',
      url: `/api/v1/enveloppes/${id}`,
      mediaType: 'application/json',
    })
  }

  public static getEnveloppeEdition(): CancelablePromise<Enveloppe> {
    return __request(OpenAPI, {
      method: 'GET',
      url: `/api/v1/enveloppes/edition`,
      mediaType: 'application/json',
    })
  }

  public static postEnveloppe(
    enveloppe: Body_post_enveloppe
  ): CancelablePromise<Enveloppe> {
    return __request(OpenAPI, {
      method: 'POST',
      url: '/api/v1/enveloppes/',
      body: enveloppe,
      mediaType: 'application/json',
    })
  }

  public static postValoriserEnveloppe(
    enveloppeId: number
  ): CancelablePromise<Enveloppe> {
    return __request(OpenAPI, {
      method: 'POST',
      url: `/api/v1/enveloppes/${enveloppeId}/valoriser`,
      mediaType: 'application/json',
    })
  }

  public static postTerminerEnveloppe(
    enveloppeId: number
  ): CancelablePromise<Enveloppe> {
    return __request(OpenAPI, {
      method: 'POST',
      url: `/api/v1/enveloppes/${enveloppeId}/terminer`,
      mediaType: 'application/json',
    })
  }

  public static postAffranchissement(
    enveloppeId: number,
    affranchissements: Body_post_affranchissement
  ): CancelablePromise<Response_post_affranchissement> {
    return __request(OpenAPI, {
      method: 'POST',
      url: `/api/v1/enveloppes/${enveloppeId}/add`,
      body: affranchissements,
      mediaType: 'application/json',
    })
  }

  public static postPhoto(
    enveloppeId: number,
    data: FormData
  ): CancelablePromise<Enveloppe> {
    return __request(OpenAPI, {
      method: 'POST',
      url: `/api/v1/enveloppes/${enveloppeId}/photos`,
      body: data,
      mediaType: 'multipart/form-data',
    })
  }

  public static deletePhoto(
    enveloppeId: number,
    photoId: number
  ): CancelablePromise<Enveloppe> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: `/api/v1/enveloppes/${enveloppeId}/photos/${photoId}`,
      mediaType: 'application/json',
    })
  }

  public static updateAffranchissement(
    enveloppeId: number,
    affranchissementId: number,
    data: Body_update_affranchissement
  ): CancelablePromise<Response_post_affranchissement> {
    return __request(OpenAPI, {
      method: 'PUT',
      url: `/api/v1/enveloppes/${enveloppeId}/affranchissements/${affranchissementId}`,
      body: data,
      mediaType: 'application/json',
    })
  }

  public static deleteAffranchissement(
    enveloppeId: number,
    affranchissementId: number
  ): CancelablePromise<Response_post_affranchissement> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: `/api/v1/enveloppes/${enveloppeId}/affranchissements/${affranchissementId}`,
      mediaType: 'application/json',
    })
  }

  public static uploadPhotoToCurrentEditionEnveloppe(
    file: File
  ): CancelablePromise<Enveloppe> {
    const formData = new FormData()
    formData.append('file', file)

    return __request(OpenAPI, {
      method: 'POST',
      url: `/api/v1/enveloppes/photos/token`,
      body: formData,
      mediaType: 'multipart/form-data',
    })
  }

  public static getEnveloppes(
    page: number,
    pageSize: number,
    filters: { recherche?: string; statut?: string; expediteur_nom?: string }
  ): CancelablePromise<PaginatedResponse<Enveloppe>> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/enveloppes/',
      query: {
        page: page.toString(),
        pageSize: pageSize.toString(),
        ...filters,
      },
      mediaType: 'application/json',
    })
  }

  public static deleteAllAffranchissements(
    enveloppeId: number
  ): CancelablePromise<Response_post_affranchissement> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: `/api/v1/enveloppes/${enveloppeId}/affranchissements`,
      mediaType: 'application/json',
    })
  }

  public static deleteEnveloppe(id: number): CancelablePromise<Enveloppe> {
    return __request(OpenAPI, {
      method: 'DELETE',
      url: `/api/v1/enveloppes/${id}`,
      mediaType: 'application/json',
    })
  }

  public static getEnveloppePhotos(
    enveloppeId: number
  ): CancelablePromise<PhotoEnveloppe[]> {
    return __request(OpenAPI, {
      method: 'GET',
      url: `/api/v1/enveloppes/${enveloppeId}/photos`,
      mediaType: 'application/json',
    })
  }
}
