import type { CancelablePromise } from '@/core/CancelablePromise'
import { OpenAPI } from '@/core/OpenAPI'
import { request as __request } from '@/core/request'
import { Site } from '@/models/site'

export class SiteService {
  /**
   * Login Access Token
   * OAuth2 compatible token login, get an access token for future requests
   * @returns Token Successful Response
   * @throws ApiError
   */
  public static getSites(): CancelablePromise<Site[]> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/sites',
      mediaType: 'application/json',
    })
  }
}
