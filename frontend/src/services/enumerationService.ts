import type { CancelablePromise } from '@/core/CancelablePromise'
import { OpenAPI } from '@/core/OpenAPI'
import { request as __request } from '@/core/request'
import { Nature } from '@/models/nature'
import { AffranchissementCategory } from '@/models/affranchissement'

export class EnumerationService {
  public static getNatures(): CancelablePromise<Nature[]> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/enumerations/natures',
      mediaType: 'application/json',
    })
  }

  public static getAffranchissements(): CancelablePromise<AffranchissementCategory[]> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/enumerations/affranchissements',
      mediaType: 'application/json',
    })
  }

  public static getDestinations(): CancelablePromise<string[]> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/enumerations/destinations',
      mediaType: 'application/json',
    })
  }

  public static getEnveloppesStatus(): CancelablePromise<string[]> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/enumerations/enveloppes_status',
    })
  }

  public static getLotsExpediteurStatut(): CancelablePromise<string[]> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/enumerations/lots_expediteur_statut',
    })
  }

  public static getCasierStatut(): CancelablePromise<string[]> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/enumerations/casier_statut',
    })
  }
}