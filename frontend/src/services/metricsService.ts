import type { CancelablePromise } from '@/core/CancelablePromise'
import { OpenAPI } from '@/core/OpenAPI'
import { request as __request } from '@/core/request'
import type { DashboardMetricsResponse } from '@/models/metrics'

export interface MetricsParams {
  startDateTime: string;
  endDateTime: string;
  siteId?: number;
}

/**
 * ----------  METRICS SERVICE  ----------
 * Wrapper around GET /api/v1/dashboard/metrics
 */
export class MetricsService {
  /**
   * Fetch dashboard metrics.
   *
   * @param params  Metrics parameters
   */
  public static getMetrics(params: MetricsParams): CancelablePromise<DashboardMetricsResponse> {
    const { startDateTime, endDateTime, siteId } = params;
    
    // Créer un objet pour les query params
    const query: Record<string, string> = {
      start_date: startDateTime,
      end_date: endDateTime
    };
    
    if (siteId !== undefined) {
      query.site_id = siteId.toString();
    }
    
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/dashboard/metrics',
      query: query,
      mediaType: 'application/json',
    });
  }
}
