import type { CancelablePromise } from '@/core/CancelablePromise'
import { OpenAPI } from '@/core/OpenAPI'
import { request as __request } from '@/core/request'
import { Destination } from '@/models/destination'

export class DestinationService {
  /**
   * Login Access Token
   * OAuth2 compatible token login, get an access token for future requests
   * @returns Token Successful Response
   * @throws ApiError
   */
  public static getDestinations(search: string | undefined): CancelablePromise<Destination[]> {
    return __request(OpenAPI, {
      method: 'GET',
      url: '/api/v1/destinations?search=' + search,
      mediaType: 'application/json',
    })
  }
}
