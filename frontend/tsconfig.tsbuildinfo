{"root": ["./src/main.tsx", "./src/routetree.gen.ts", "./src/vite-env.d.ts", "./src/components/dropdown.tsx", "./src/components/fileicon.tsx", "./src/components/filterdropdown.tsx", "./src/components/iconbutton.tsx", "./src/components/modal.tsx", "./src/components/pagination.tsx", "./src/components/scanditscanner.tsx", "./src/components/sidemenu.tsx", "./src/components/spinner.tsx", "./src/components/control/bouttonajoutaffranchissement.tsx", "./src/components/forms/addressautocomplete.tsx", "./src/components/forms/button.tsx", "./src/components/forms/checkbox.tsx", "./src/components/forms/divider.tsx", "./src/components/forms/formerrors.tsx", "./src/components/forms/input.tsx", "./src/components/forms/modal.tsx", "./src/components/forms/searchselector.tsx", "./src/components/forms/select.tsx", "./src/components/forms/spinner.tsx", "./src/components/pages/changelogpage.tsx", "./src/components/pages/loadingpage.tsx", "./src/components/pages/notfoundpage.tsx", "./src/components/pages/control/addsendermodal.tsx", "./src/components/pages/control/affranchissementpanel.tsx", "./src/components/pages/control/bubblecomponent.tsx", "./src/components/pages/control/categoryaffranchissementmodal.tsx", "./src/components/pages/control/codeinputwithscanner.tsx", "./src/components/pages/control/codescanner.tsx", "./src/components/pages/control/controlaffranchissementmain.tsx", "./src/components/pages/control/controlaffranchissementmodal.tsx", "./src/components/pages/control/controlenveloppevalorisation.tsx", "./src/components/pages/control/controlsiteselection.tsx", "./src/components/pages/control/controlterminer.tsx", "./src/components/pages/control/enveloppeaffranchissementitem.tsx", "./src/components/pages/control/enveloppemanager.tsx", "./src/components/pages/control/photomanagermodal.tsx", "./src/components/pages/control/sectioncategorieaffranchissement.tsx", "./src/components/pages/control/senderinfo.tsx", "./src/components/pages/control/valorisationmodal.tsx", "./src/components/pages/control/webcamview.tsx", "./src/components/pages/control/scandit/classicscanner.tsx", "./src/components/pages/control/scandit/controlscanner.tsx", "./src/components/pages/dashboard/dashboardpage.tsx", "./src/components/pages/external/mobilephotocomponent.tsx", "./src/components/pages/lots/newlotmodal.tsx", "./src/components/pages/lots/statuschangeconfirmmodal.tsx", "./src/components/pages/rules/ruleactionsmenu.tsx", "./src/components/pages/users/createusermodal.tsx", "./src/components/pages/users/editusermodal.tsx", "./src/contexts/control.tsx", "./src/contexts/sitecontext.tsx", "./src/core/apiauthentificationinterceptors.ts", "./src/core/apierror.ts", "./src/core/apirequestoptions.ts", "./src/core/apiresult.ts", "./src/core/cancelablepromise.ts", "./src/core/openapi.ts", "./src/core/request.ts", "./src/core/types.ts", "./src/hooks/useaffranchissement.ts", "./src/hooks/useauth.ts", "./src/hooks/usecontrol.ts", "./src/hooks/usedebounce.ts", "./src/hooks/usedisclosure.ts", "./src/hooks/useexternalauth.ts", "./src/hooks/usephotos.ts", "./src/hooks/usesite.ts", "./src/models/affranchissement.ts", "./src/models/authenticationmodels.ts", "./src/models/casier.ts", "./src/models/destination.ts", "./src/models/destinations.ts", "./src/models/enveloppe.ts", "./src/models/lotexpediteurs.ts", "./src/models/metrics.ts", "./src/models/nature.ts", "./src/models/othersmodels.ts", "./src/models/paginatedresponse.ts", "./src/models/paginated_response.ts", "./src/models/pappersresults.ts", "./src/models/product.ts", "./src/models/reglesmetier.ts", "./src/models/sender.ts", "./src/models/site.ts", "./src/models/toastmessage.ts", "./src/models/usersmodel.ts", "./src/providers/controlprovider.tsx", "./src/providers/siteprovider.tsx", "./src/routes/__root.tsx", "./src/routes/_external.tsx", "./src/routes/_layout.tsx", "./src/routes/login-callback.tsx", "./src/routes/login-external.tsx", "./src/routes/login.tsx", "./src/routes/_external/mobile.tsx", "./src/routes/_layout/casiers.tsx", "./src/routes/_layout/changelog.tsx", "./src/routes/_layout/control.tsx", "./src/routes/_layout/enveloppes.tsx", "./src/routes/_layout/index.tsx", "./src/routes/_layout/qrcode.tsx", "./src/routes/_layout/rules.tsx", "./src/routes/_layout/scan.tsx", "./src/routes/_layout/users.tsx", "./src/routes/_layout/lots-expediteurs/$lotexpediteurid.tsx", "./src/routes/_layout/lots-expediteurs/index.tsx", "./src/services/affranchissementservice.ts", "./src/services/authenticationservice.ts", "./src/services/casierservice.ts", "./src/services/destinationservice.ts", "./src/services/enumerationservice.ts", "./src/services/enveloppeservice.ts", "./src/services/keycloakservice.ts", "./src/services/lotexpediteurservice.ts", "./src/services/metricsservice.ts", "./src/services/pappersservice.ts", "./src/services/reglesmetierservice.ts", "./src/services/senderservice.ts", "./src/services/siteservice.ts", "./src/services/tokenservice.ts", "./src/services/usersservice.ts", "./src/utils/affranchissementhelpers.ts", "./src/utils/controlhelpers.ts", "./src/utils/datesutils.ts", "./src/utils/filehelpers.ts", "./src/utils/fileicons.ts", "./src/utils/loginrules.ts", "./src/utils/statusmetadatashelpers.ts", "./src/utils/textutils.ts"], "version": "5.7.3"}