{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.0", "@headlessui/tailwindcss": "^0.2.2", "@scandit/web-datacapture-barcode": "^7.1.2", "@scandit/web-datacapture-core": "^7.1.2", "@tailwindcss/vite": "^4.0.3", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.66.0", "@tanstack/react-router": "^1.99.6", "@tanstack/router-devtools": "^1.99.6", "@tanstack/router-vite-plugin": "^1.99.6", "@types/uniqid": "^5.3.4", "@vitejs/plugin-react-swc": "^3.7.2", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "clsx": "^2.1.1", "framer-motion": "^12.9.7", "html2canvas": "^1.4.1", "moment": "^2.30.1", "postcss": "^8.5.1", "react": "^19.0.0", "react-barcode-reader": "^0.0.2", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.1", "react-icons": "^5.4.0", "react-webcam": "^7.2.0", "recharts": "^2.15.3", "tailwindcss": "^4.0.3", "uniqid": "^5.4.0", "vite-tsconfig-paths": "^5.1.4", "zod": "^3.24.2"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "prettier": "^3.5.1", "prettier-plugin-tailwindcss": "^0.6.11", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}, "overrides": {"@headlessui/tailwindcss": {"tailwindcss": "$tailwindcss"}}}