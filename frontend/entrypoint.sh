#!/bin/bash
echo "Starting frontend..."

BUILD_SRC_DIR="/usr/share/nginx/html"

# Génération d'une nouvelle valeur pour CACHE_BUSTER à chaque démarrage
# CACHE_BUSTER=$(date +%s)busted
# echo "Generated new CACHE_BUSTER: $CACHE_BUSTER"

# # 1. Renommer les fichiers en remplaçant CACHEBUSTER par la nouvelle valeur
# find $BUILD_SRC_DIR -type f -name "*-CACHEBUSTER.*" | while read FILE; do
#   NEW_FILE=$(echo $FILE | sed "s/-CACHEBUSTER\./-$CACHE_BUSTER\./g")
#   mv "$FILE" "$NEW_FILE"
#   echo "Renamed: $FILE -> $NEW_FILE"
# done

# # 2. Mettre à jour les références dans les fichiers HTML et JS
# find $BUILD_SRC_DIR -type f \( -name "*.html" -o -name "*.js" \) | while read FILE; do
#   sed -i "s/-CACHEBUSTER\./-$CACHE_BUSTER\./g" "$FILE"
#   echo "Updated references in file: $FILE"
# done

# 4. Surcharge des variables d'environnement
for var in $(printenv | awk -F= '{print $1}'); do
  if grep -q "${var}_OVERRIDE_ENVDATA" $BUILD_SRC_DIR -r 2>/dev/null; then
    find $BUILD_SRC_DIR -type f -name "*.html" -o -name "*.js" | xargs -r sed -i "s|${var}_OVERRIDE_ENVDATA|${!var}|g"
    echo "Replaced environment variable: ${var}"
  fi
done

echo "Frontend setup completed successfully"
nginx -g "daemon off;"
