import { TanStackRouterVite } from '@tanstack/router-vite-plugin'
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import tsconfigPaths from 'vite-tsconfig-paths'
import tailwindcss from '@tailwindcss/vite'

// Ajouter une clé CACHEBUSTER fixe qui sera remplacée par l'entrypoint
// const cacheBusterPlaceholder = 'CACHEBUSTER';
const cacheBusterPlaceholder = Date.now();

// https://vitejs.dev/config https://vitest.dev/config
export default defineConfig({
  plugins: [react(), tsconfigPaths(), TanStackRouterVite(), tailwindcss()],
  server: {
    allowedHosts: true
  },
  build: {
    rollupOptions: {
      output: {
        // Ajouter CACHEBUSTER dans les noms de fichiers
        entryFileNames: `assets/[name].[hash]-${cacheBusterPlaceholder}.js`,
        chunkFileNames: `assets/[name].[hash]-${cacheBusterPlaceholder}.js`,
        assetFileNames: `assets/[name].[hash]-${cacheBusterPlaceholder}.[ext]`
      }
    }
  }
});
