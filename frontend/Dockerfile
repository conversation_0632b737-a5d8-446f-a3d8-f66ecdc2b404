FROM node:20 as dev-stage

WORKDIR /app

# Copy package files and install dependencies
COPY package*.json /app/
COPY pnpm-lock.yaml /app/
RUN npm install -g pnpm
RUN pnpm install

# Copy the rest of the source code
COPY . /app/

# Ajout d'un ARG pour le cache busting
ARG BUILD_ID
ENV VITE_BUILD_ID=$BUILD_ID

# Set up an ARG/ENV for API URL if needed
ARG VITE_API_URL=${VITE_API_URL}
ENV VITE_API_URL=VITE_API_URL_OVERRIDE_ENVDATA
ENV VITE_KEYCLOAK_URL=VITE_KEYCLOAK_URL_OVERRIDE_ENVDATA
ENV VITE_KEYCLOAK_REALM=VITE_KEYCLOAK_REALM_OVERRIDE_ENVDATA
ENV VITE_KEYCLOAK_CLIENT_ID=VITE_KEYCLOAK_CLIENT_ID_OVERRIDE_ENVDATA
ENV NODE_ENV=${NODE_ENV}
ENV VITE_REMOTE_ENV=${VITE_REMOTE_ENV}
ENV VITE_PUBLIC_SCANDIT_API_KEY=VITE_PUBLIC_SCANDIT_API_KEY_OVERRIDE_ENVDATA
ENV VITE_PUBLIC_ENVIRONMENT=VITE_PUBLIC_ENVIRONMENT_OVERRIDE_ENVDATA

RUN npm run build


# Stage 1, based on Nginx, to have only the compiled app, ready for production with Nginx
FROM nginx:1

COPY --from=dev-stage /app/dist/ /usr/share/nginx/html

COPY ./nginx.conf /etc/nginx/conf.d/default.conf
COPY ./nginx-backend-not-found.conf /etc/nginx/extra-conf.d/backend-not-found.conf


COPY ./entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh
ENTRYPOINT ["/entrypoint.sh"]
