"""empty message

Revision ID: 5efb1b4b32fa
Revises: 5236f9cc2f35
Create Date: 2025-06-05 16:36:11.150247

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '5efb1b4b32fa'
down_revision = '5236f9cc2f35'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('enveloppe', sa.Column('completed_at', sa.DateTime(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('enveloppe', 'completed_at')
    # ### end Alembic commands ###
