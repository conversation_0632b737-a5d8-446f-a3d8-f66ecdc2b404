"""empty message

Revision ID: a93868428dcb
Revises: e87445d0c927
Create Date: 2025-03-21 12:23:18.424428

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from alembic_postgresql_enum import TableReference

# revision identifiers, used by Alembic.
revision = 'a93868428dcb'
down_revision = 'e87445d0c927'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('USER', 'MANAGER', 'ADMIN', name='userrole').create(op.get_bind())
    op.add_column('enveloppe', sa.Column('informations_data', sa.JSON(), nullable=True))
    op.execute("UPDATE \"user\" SET role = 'ADMIN' WHERE role = 'superuser'")
    op.execute("UPDATE \"user\" SET role = 'MANAGER' WHERE role = 'manager'")
    op.execute("UPDATE \"user\" SET role = 'USER' WHERE role = 'user'")
    op.alter_column('user', 'role',
               existing_type=sa.VARCHAR(length=25),
               type_=sa.Enum('USER', 'MANAGER', 'ADMIN', name='userrole'),
               nullable=False,
               postgresql_using='role::userrole')
    op.sync_enum_values(
        enum_schema='public',
        enum_name='statutenveloppeenum',
        new_values=['EDITION', 'VALORISE', 'PAUSE', 'FRAUDULEUSE', 'SOUS_AFFRANCHI', 'TERMINEE'],
        affected_columns=[TableReference(table_schema='public', table_name='enveloppe', column_name='statut')],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='statutenveloppeenum',
        new_values=['EDITION', 'VALORISE', 'PAUSE', 'FRAUDULEUSE', 'TERMINEE'],
        affected_columns=[TableReference(table_schema='public', table_name='enveloppe', column_name='statut')],
        enum_values_to_rename=[],
    )
    op.alter_column('user', 'role',
               existing_type=sa.Enum('USER', 'MANAGER', 'ADMIN', name='userrole'),
               type_=sa.VARCHAR(length=25),
               nullable=True)
    op.drop_column('enveloppe', 'informations_data')
    sa.Enum('USER', 'MANAGER', 'ADMIN', name='userrole').drop(op.get_bind())
    # ### end Alembic commands ###
