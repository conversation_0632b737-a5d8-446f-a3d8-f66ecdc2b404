"""empty message

Revision ID: 408a28072368
Revises: 7ba09f310f8d
Create Date: 2025-05-16 18:33:37.391307

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '408a28072368'
down_revision = '7ba09f310f8d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('METROPOLE', 'INTERNATIONAL', 'DOM_COM', name='destinationenveloppeenum').create(op.get_bind())
    op.add_column('enveloppe', sa.Column('destination_enveloppe', postgresql.ENUM('METROPOLE', 'INTERNATIONAL', 'DOM_COM', name='destinationenveloppeenum', create_type=False), autoincrement=False, nullable=True))

    # Mise à jour des données existantes avec conversion explicite en type enum
    op.execute("""
    UPDATE enveloppe
    SET destination_enveloppe = CASE
        WHEN produit = 'LV' THEN 'METROPOLE'::destinationenveloppeenum
        WHEN produit = 'LINT' THEN 'INTERNATIONAL'::destinationenveloppeenum
        WHEN produit = 'PPI' THEN 'INTERNATIONAL'::destinationenveloppeenum
        WHEN produit = 'LSP' THEN 'METROPOLE'::destinationenveloppeenum
        ELSE 'METROPOLE'::destinationenveloppeenum
    END
    """)

    # Vérification des données invalides
    connection = op.get_bind()
    invalid_data = connection.execute(sa.text("SELECT id FROM enveloppe WHERE destination_enveloppe IS NULL")).fetchall()
    if invalid_data:
        raise Exception(f"Données invalides trouvées pour les enveloppes avec les IDs: {[row[0] for row in invalid_data]}")

    op.alter_column('enveloppe', 'produit', nullable=True)
    # op.drop_column('enveloppe', 'produit')
    # sa.Enum('LV', 'LINT', 'PPI', 'LSP', 'NON_DETERMINE', name='produitenum').drop(op.get_bind())
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('LV', 'LINT', 'PPI', 'LSP', 'NON_DETERMINE', name='produitenum').create(op.get_bind())
    op.add_column('enveloppe', sa.Column('produit', postgresql.ENUM('LV', 'LINT', 'PPI', 'LSP', 'NON_DETERMINE', name='produitenum', create_type=False), autoincrement=False, nullable=False))
    op.drop_column('enveloppe', 'destination')
    sa.Enum('METROPOLE', 'DOM_COM', 'INTERNATIONAL', name='destinationenveloppeenum').drop(op.get_bind())
    # ### end Alembic commands ###
