"""empty message

Revision ID: 1d84de4a6266
Revises: 0e7ec4e9f3d7
Create Date: 2025-03-26 15:37:53.844761

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from models.business import Affranchissement
from services.affranchissement import ServiceAffranchissement

# revision identifiers, used by Alembic.
revision = '1d84de4a6266'
down_revision = '0e7ec4e9f3d7'
branch_labels = None
depends_on = None
from sqlalchemy.orm import Session

def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('affranchissement', sa.Column('prix_unite_euros', sa.Float(), nullable=True))

    # Utiliser du SQL pur pour mettre à jour la colonne prix_unite_euros
    conn = op.get_bind()
    
    # Récupérer tous les affranchissements
    result = conn.execute(sa.text("""
        SELECT id, prix_unite_devise, devise 
        FROM affranchissement
    """))
    
    # Pour chaque affranchissement, calculer le prix en euros
    from core.utils.prix import convertir_prix_devise_en_euros
    
    for row in result:
        id_affranchissement, prix_devise, devise = row
        
        # Calculer le prix en euros directement
        if prix_devise is not None and devise is not None:
            prix_euros = convertir_prix_devise_en_euros(devise, prix_devise)
            
            # Mettre à jour l'enregistrement
            conn.execute(
                sa.text("""
                    UPDATE affranchissement 
                    SET prix_unite_euros = :prix_euros 
                    WHERE id = :id
                """),
                {"prix_euros": prix_euros, "id": id_affranchissement}
            )
    
    # Pas besoin de commit explicite car op.get_bind() gère la transaction
   

    op.alter_column('affranchissement', 'prix_unite_devise',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=False)
    op.alter_column('affranchissement', 'prix_unite_euros',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=False)
    # ### end Alembic commands ###
  

def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('affranchissement', 'prix_unite_devise',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=True)
    op.drop_column('affranchissement', 'prix_unite_euros')
    # ### end Alembic commands ###
