"""empty message

Revision ID: cb49e8d6dc9c
Revises: c1ea2aa71be0
Create Date: 2025-04-10 17:49:08.625574

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from alembic_postgresql_enum import TableReference
from sqlalchemy.orm import Session
from models.business import Affranchissement
from core.utils.prix import convertir_prix_devise_en_euros


# revision identifiers, used by Alembic.
revision = 'cb49e8d6dc9c'
down_revision = 'c1ea2aa71be0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('boiteenveloppes')
    op.drop_constraint('enveloppe_traitement_lot_enveloppes_id_fkey', 'enveloppe', type_='foreignkey')
    op.drop_column('enveloppe', 'traitement_lot_enveloppes_id')
    op.drop_constraint('lotexpediteur_traitement_lot_enveloppes_id_fkey', 'lotexpediteur', type_='foreignkey')
    op.drop_column('lotexpediteur', 'traitement_lot_enveloppes_id')
    op.add_column('messageexpediteur', sa.Column('lot_expediteur_id', sa.Integer(), nullable=True))
    op.drop_constraint('messageexpediteur_traitement_lot_enveloppes_id_fkey', 'messageexpediteur', type_='foreignkey')
    op.create_foreign_key(None, 'messageexpediteur', 'lotexpediteur', ['lot_expediteur_id'], ['id'])
    op.drop_column('messageexpediteur', 'traitement_lot_enveloppes_id')
    op.sync_enum_values(
        enum_schema='public',
        enum_name='sourceenveloppeenum',
        new_values=['MANUEL', 'TRAITEMENT', 'FACTEO', 'IMPORT'],
        affected_columns=[TableReference(table_schema='public', table_name='enveloppe', column_name='source', existing_server_default="'MANUEL'::sourceenveloppeenum")],
        enum_values_to_rename=[],
    )

    op.drop_table('traitementlotenveloppes')

    # session = Session(bind=op.get_bind())

    # # Sélectionner tous les affranchissements où prix_unite_euros = 0 mais prix_unite_devise != 0
    # affranchissements_a_recalculer = session.query(Affranchissement).filter(
    #     Affranchissement.prix_unite_euros == 0,
    #     Affranchissement.prix_unite_devise != 0
    # ).all()
    
    # print(f"Recalcul du prix en euros pour {len(affranchissements_a_recalculer)} affranchissements...")
    
    # for affranchissement in affranchissements_a_recalculer:
    #     # Recalculer le prix en euros en utilisant la fonction de conversion
    #     affranchissement.prix_unite_euros = convertir_prix_devise_en_euros(
    #         affranchissement.devise, 
    #         affranchissement.prix_unite_devise
    #     )
    #     session.add(affranchissement)
    
    # session.commit()
    print("Recalcul terminé.")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='sourceenveloppeenum',
        new_values=['MANUEL', 'TRAITEMENT', 'FACTEO'],
        affected_columns=[TableReference(table_schema='public', table_name='enveloppe', column_name='source', existing_server_default="'MANUEL'::sourceenveloppeenum")],
        enum_values_to_rename=[],
    )
    op.add_column('messageexpediteur', sa.Column('traitement_lot_enveloppes_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.drop_constraint(None, 'messageexpediteur', type_='foreignkey')
    op.create_foreign_key('messageexpediteur_traitement_lot_enveloppes_id_fkey', 'messageexpediteur', 'traitementlotenveloppes', ['traitement_lot_enveloppes_id'], ['id'])
    op.drop_column('messageexpediteur', 'lot_expediteur_id')
    op.add_column('lotexpediteur', sa.Column('traitement_lot_enveloppes_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key('lotexpediteur_traitement_lot_enveloppes_id_fkey', 'lotexpediteur', 'traitementlotenveloppes', ['traitement_lot_enveloppes_id'], ['id'])
    op.add_column('enveloppe', sa.Column('traitement_lot_enveloppes_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key('enveloppe_traitement_lot_enveloppes_id_fkey', 'enveloppe', 'traitementlotenveloppes', ['traitement_lot_enveloppes_id'], ['id'])
    op.create_table('traitementlotenveloppes',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.PrimaryKeyConstraint('id', name='traitementlotenveloppes_pkey')
    )
    op.create_table('boiteenveloppes',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('cle', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('description', sa.VARCHAR(length=255), autoincrement=False, nullable=True),
    sa.Column('site_id', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['site_id'], ['site.id'], name='boiteenveloppes_site_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='boiteenveloppes_pkey'),
    sa.UniqueConstraint('cle', name='boiteenveloppes_cle_key')
    )
    # ### end Alembic commands ###
