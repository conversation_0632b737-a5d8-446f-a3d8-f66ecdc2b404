"""empty message

Revision ID: e87445d0c927
Revises: a0559b3d7cb8
Create Date: 2025-03-15 14:38:09.940973

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from alembic_postgresql_enum import TableReference
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'e87445d0c927'
down_revision = 'a0559b3d7cb8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('DISPONIBLE', 'OCCUPE', 'MAINTENANCE', name='statutcasierenum').create(op.get_bind())
    sa.Enum('LIVRAISON_DESTINATAIRES', 'RECUPERATION_SUR_SITE', 'RENVOI_EXPEDITEUR', name='optiontraitementlotexpediteurenum').create(op.get_bind())
    sa.Enum('OUVERT', 'NOTIFIE', 'PAIEMENT_RECU', 'ANNULE', 'TRAITE', name='statutlotexpediteurenum').create(op.get_bind())
    op.create_table('casier',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('numero', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=False),
    sa.Column('emplacement', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('statut', postgresql.ENUM('DISPONIBLE', 'OCCUPE', 'MAINTENANCE', name='statutcasierenum', create_type=False), nullable=False),
    sa.Column('site_id', sa.Integer(), nullable=False),
    sa.Column('date_attribution', sa.DateTime(), nullable=True),
    sa.Column('date_liberation', sa.DateTime(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['site_id'], ['site.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('enveloppe', sa.Column('lot_expediteur_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'enveloppe', 'lotexpediteur', ['lot_expediteur_id'], ['id'])
    op.add_column('lotexpediteur', sa.Column('statut', postgresql.ENUM('OUVERT', 'NOTIFIE', 'PAIEMENT_RECU', 'ANNULE', 'TRAITE', name='statutlotexpediteurenum', create_type=False), nullable=False))
    op.add_column('lotexpediteur', sa.Column('option_recouvrement', postgresql.ENUM('LIVRAISON_DESTINATAIRES', 'RECUPERATION_SUR_SITE', 'RENVOI_EXPEDITEUR', name='optiontraitementlotexpediteurenum', create_type=False), nullable=True))
    op.add_column('lotexpediteur', sa.Column('adresse_renvoi', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True))
    op.add_column('lotexpediteur', sa.Column('date_creation', sa.DateTime(), nullable=False))
    op.add_column('lotexpediteur', sa.Column('date_paiement', sa.DateTime(), nullable=True))
    op.add_column('lotexpediteur', sa.Column('date_recuperation', sa.DateTime(), nullable=True))
    op.add_column('lotexpediteur', sa.Column('casier_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'lotexpediteur', 'casier', ['casier_id'], ['id'])
    op.sync_enum_values(
        enum_schema='public',
        enum_name='statutenveloppeenum',
        new_values=['EDITION', 'VALORISE', 'PAUSE', 'FRAUDULEUSE', 'TERMINEE'],
        affected_columns=[TableReference(table_schema='public', table_name='enveloppe', column_name='statut')],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='statutenveloppeenum',
        new_values=['EDITION', 'VALORISE', 'TERMINEE', 'PAUSE'],
        affected_columns=[TableReference(table_schema='public', table_name='enveloppe', column_name='statut')],
        enum_values_to_rename=[],
    )
    op.drop_constraint(None, 'lotexpediteur', type_='foreignkey')
    op.drop_column('lotexpediteur', 'casier_id')
    op.drop_column('lotexpediteur', 'date_recuperation')
    op.drop_column('lotexpediteur', 'date_paiement')
    op.drop_column('lotexpediteur', 'date_creation')
    op.drop_column('lotexpediteur', 'adresse_renvoi')
    op.drop_column('lotexpediteur', 'option_recouvrement')
    op.drop_column('lotexpediteur', 'statut')
    op.drop_constraint(None, 'enveloppe', type_='foreignkey')
    op.drop_column('enveloppe', 'lot_expediteur_id')
    op.drop_table('casier')
    sa.Enum('OUVERT', 'NOTIFIE', 'PAIEMENT_RECU', 'ANNULE', 'TRAITE', name='statutlotexpediteurenum').drop(op.get_bind())
    sa.Enum('LIVRAISON_DESTINATAIRES', 'RECUPERATION_SUR_SITE', 'RENVOI_EXPEDITEUR', name='optiontraitementlotexpediteurenum').drop(op.get_bind())
    sa.Enum('DISPONIBLE', 'OCCUPE', 'MAINTENANCE', name='statutcasierenum').drop(op.get_bind())
    # ### end Alembic commands ###
