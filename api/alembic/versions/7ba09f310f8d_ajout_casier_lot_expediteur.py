"""Ajout casier + lot expediteur

Revision ID: 7ba09f310f8d
Revises: 5703b631b166
Create Date: 2025-04-30 15:00:03.227047

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from alembic_postgresql_enum import TableReference
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '7ba09f310f8d'
down_revision = '5703b631b166'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('DISPONIBLE', 'PLEIN', name='statutcasierenum').create(op.get_bind())
    op.add_column('casier', sa.Column('capacite_max', sa.Integer(), nullable=False))
    op.add_column('casier', sa.Column('statut', postgresql.ENUM('DISPONIBLE', 'PLEIN', name='statutcasierenum', create_type=False), nullable=False))
    op.drop_constraint('casier_lot_expediteur_id_fkey', 'casier', type_='foreignkey')
    op.drop_column('casier', 'lot_expediteur_id')
    op.add_column('enveloppe', sa.Column('casier_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'enveloppe', 'casier', ['casier_id'], ['id'])
    op.add_column('lotexpediteur', sa.Column('site_id', sa.Integer(), nullable=True))
    op.add_column('lotexpediteur', sa.Column('casier_id', sa.Integer(), nullable=True))
    op.add_column('lotexpediteur', sa.Column('created_at', sa.DateTime(), nullable=False))
    op.add_column('lotexpediteur', sa.Column('updated_at', sa.DateTime(), nullable=False))
    op.create_foreign_key(None, 'lotexpediteur', 'site', ['site_id'], ['id'])
    op.create_foreign_key(None, 'lotexpediteur', 'casier', ['casier_id'], ['id'])
    op.sync_enum_values(
        enum_schema='public',
        enum_name='typeaffranchissementenum',
        new_values=['SD', 'S10', 'TNUM', 'LV20G', 'LP20G', 'INTMON20G', 'LSP', 'ECO20G', 'EUR20G', 'VAL', 'DEWD1', 'INTER20G', 'GUIC', 'AUTO', 'T', 'NON_DETERMINE', 'PAP_S_500', 'PAP_XS_250'],
        affected_columns=[TableReference(table_schema='public', table_name='affranchissement', column_name='type'), TableReference(table_schema='public', table_name='reglemetier', column_name='type_affranchissement')],
        enum_values_to_rename=[],
    )
    op.sync_enum_values(
        enum_schema='public',
        enum_name='statutlotexpediteurenum',
        new_values=['OUVERT', 'NOTIFIE', 'PAIEMENT_RECU', 'TRAITE', 'ANNULE'],
        affected_columns=[TableReference(table_schema='public', table_name='lotexpediteur', column_name='statut')],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='statutlotexpediteurenum',
        new_values=['OUVERT', 'NOTIFIE', 'PAIEMENT_RECU', 'ANNULE', 'TRAITE'],
        affected_columns=[TableReference(table_schema='public', table_name='lotexpediteur', column_name='statut')],
        enum_values_to_rename=[],
    )
    op.sync_enum_values(
        enum_schema='public',
        enum_name='typeaffranchissementenum',
        new_values=['SD86', 'SD87', 'SD88', 'SD', 'SDMTEL', 'SDIND', 'S10', 'TNUM', 'LV20G', 'LP20G', 'INTMON20G', 'LSP', 'ECO20G', 'EUR20G', 'VAL', 'DEWD1', 'INTER20G', 'GUIC', 'AUTO', 'T', 'NON_DETERMINE', 'PAP_S_500', 'PAP_XS_250'],
        affected_columns=[TableReference(table_schema='public', table_name='affranchissement', column_name='type'), TableReference(table_schema='public', table_name='reglemetier', column_name='type_affranchissement')],
        enum_values_to_rename=[],
    )
    op.drop_constraint(None, 'lotexpediteur', type_='foreignkey')
    op.drop_constraint(None, 'lotexpediteur', type_='foreignkey')
    op.drop_column('lotexpediteur', 'updated_at')
    op.drop_column('lotexpediteur', 'created_at')
    op.drop_column('lotexpediteur', 'casier_id')
    op.drop_column('lotexpediteur', 'site_id')
    op.drop_constraint(None, 'enveloppe', type_='foreignkey')
    op.drop_column('enveloppe', 'casier_id')
    op.add_column('casier', sa.Column('lot_expediteur_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key('casier_lot_expediteur_id_fkey', 'casier', 'lotexpediteur', ['lot_expediteur_id'], ['id'])
    op.drop_column('casier', 'statut')
    op.drop_column('casier', 'capacite_max')
    sa.Enum('DISPONIBLE', 'PLEIN', name='statutcasierenum').drop(op.get_bind())
    # ### end Alembic commands ###
