"""empty message

Revision ID: ce7e932f0131
Revises: b5388da4cbff
Create Date: 2025-04-19 16:41:36.871186

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from alembic_postgresql_enum import TableReference
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'ce7e932f0131'
down_revision = 'b5388da4cbff'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('affranchissement', sa.Column('origine', sqlmodel.sql.sqltypes.AutoString(), nullable=True))
    
    # Conversion des SD86, SD87, SD88 en SD avec préservation de l'origine
    conn = op.get_bind()
    
    # Récupérer tous les affranchissements de type SD86, SD87, SD88
    result = conn.execute(sa.text("""
        SELECT id, type 
        FROM affranchissement
        WHERE type IN ('SD86', 'SD87', 'SD88', 'SDMTEL', 'SDIND')
    """))
    
    # Pour chaque affranchissement, mettre à jour le type et l'origine
    for row in result:
        id_affranchissement, type_affranchissement = row
        
        # Extraire l'origine à partir du type
        origine = None
        if type_affranchissement == 'SD86':
            origine = '86'
        elif type_affranchissement == 'SD87':
            origine = '87'
        elif type_affranchissement == 'SD88':
            origine = '88'
        elif type_affranchissement == 'SDMTEL':
            origine = 'MTEL'
        elif type_affranchissement == 'SDIND':
            origine = 'IND'
        
        # Mettre à jour l'enregistrement
        conn.execute(
            sa.text("""
                UPDATE affranchissement 
                SET type = 'SD', origine = :origine 
                WHERE id = :id
            """),
            {"origine": origine, "id": id_affranchissement}
        )
    
    op.drop_column('affranchissement', 'donnees')
    op.drop_column('codeaffranchissementfrauduleux', 'type')
    
    op.sync_enum_values(
        enum_schema='public',
        enum_name='typeaffranchissementenum',
        new_values=['SD', 'S10', 'TNUM', 'LV20G', 'LP20G', 'INTMON20G', 'LSP', 'ECO20G', 'EUR20G', 'VAL', 'DEWD1', 'INTER20G', 'GUIC', 'AUTO', 'T', 'NON_DETERMINE', 'PAP_S_500', 'PAP_XS_250'],
        affected_columns=[TableReference(table_schema='public', table_name='affranchissement', column_name='type'), TableReference(table_schema='public', table_name='reglemetier', column_name='type_affranchissement')],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='typeaffranchissementenum',
        new_values=['SD86', 'SD87', 'SD88', 'SD', 'SDMTEL', 'SDIND', 'S10', 'TNUM', 'LV20G', 'LP20G', 'INTMON20G', 'LSP', 'ECO20G', 'EUR20G', 'VAL', 'DEWD1', 'INTER20G', 'GUIC', 'AUTO', 'T', 'NON_DETERMINE'],
        affected_columns=[TableReference(table_schema='public', table_name='affranchissement', column_name='type'), TableReference(table_schema='public', table_name='codeaffranchissementfrauduleux', column_name='type'), TableReference(table_schema='public', table_name='reglemetier', column_name='type_affranchissement')],
        enum_values_to_rename=[],
    )
    op.add_column('affranchissement', sa.Column('donnees', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False))
    op.drop_column('affranchissement', 'origine')
    # ### end Alembic commands ###
