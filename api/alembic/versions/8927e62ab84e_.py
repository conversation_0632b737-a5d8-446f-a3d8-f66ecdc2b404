"""empty message

Revision ID: 8927e62ab84e
Revises: 44ff8478c715
Create Date: 2025-04-01 13:48:48.962281

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from alembic_postgresql_enum import TableReference

# revision identifiers, used by Alembic.
revision = '8927e62ab84e'
down_revision = '44ff8478c715'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='userrole',
        new_values=['GUEST', 'USER', 'MANAGER', 'ADMIN'],
        affected_columns=[TableReference(table_schema='public', table_name='user', column_name='role')],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='userrole',
        new_values=['USER', 'MANAGER', 'ADMIN'],
        affected_columns=[TableReference(table_schema='public', table_name='user', column_name='role')],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###
