"""empty message

Revision ID: 94a521e5859c
Revises: 7ec380abb4f3
Create Date: 2025-06-20 13:49:40.840096

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '94a521e5859c'
down_revision = '7ec380abb4f3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('transactionpaiementlotexpediteur',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('paiement_lot_expediteur_id', sa.Integer(), nullable=False),
    sa.Column('methode', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('statut', postgresql.ENUM('NON_INITIALISE', 'EN_ATTENTE', 'AUTORISE', 'PAYE', 'ECHEC', 'ANNULE', 'REMBOURSE', name='statutpaiementenum', create_type=False), nullable=False),
    sa.Column('id_transaction', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('donnees', sa.JSON(), nullable=False),
    sa.Column('date_creation', sa.DateTime(), nullable=False),
    sa.Column('date_maj', sa.DateTime(), nullable=False),
    sa.Column('date_expiration', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['paiement_lot_expediteur_id'], ['paiementlotexpediteur.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id_transaction')
    )
    op.add_column('lotexpediteur', sa.Column('option_recouvrement_params', sa.JSON(), nullable=False))
    op.drop_constraint('paiementlotexpediteur_id_transaction_key', 'paiementlotexpediteur', type_='unique')
    op.drop_column('paiementlotexpediteur', 'methode')
    op.drop_column('paiementlotexpediteur', 'donnees')
    op.drop_column('paiementlotexpediteur', 'id_transaction')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('paiementlotexpediteur', sa.Column('id_transaction', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.add_column('paiementlotexpediteur', sa.Column('donnees', postgresql.JSON(astext_type=sa.Text()), autoincrement=False, nullable=False))
    op.add_column('paiementlotexpediteur', sa.Column('methode', sa.VARCHAR(length=255), autoincrement=False, nullable=True))
    op.create_unique_constraint('paiementlotexpediteur_id_transaction_key', 'paiementlotexpediteur', ['id_transaction'])
    op.drop_column('lotexpediteur', 'option_recouvrement_params')
    op.drop_table('transactionpaiementlotexpediteur')
    # ### end Alembic commands ###
