"""empty message

Revision ID: 44ff8478c715
Revises: 1d84de4a6266
Create Date: 2025-03-28 11:09:32.880371

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from alembic_postgresql_enum import TableReference

# revision identifiers, used by Alembic.
revision = '44ff8478c715'
down_revision = '1d84de4a6266'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='sourceenveloppeenum',
        new_values=['MANUEL', 'TRAITEMENT', 'FACTEO'],
        affected_columns=[TableReference(table_schema='public', table_name='enveloppe', column_name='source', existing_server_default="'MANUEL'::sourceenveloppeenum")],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='sourceenveloppeenum',
        new_values=['MANUEL', 'TRAITEMENT'],
        affected_columns=[TableReference(table_schema='public', table_name='enveloppe', column_name='source', existing_server_default="'MANUEL'::sourceenveloppeenum")],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###
