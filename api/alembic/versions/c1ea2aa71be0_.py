"""empty message

Revision ID: c1ea2aa71be0
Revises: 8927e62ab84e
Create Date: 2025-04-05 11:32:15.961998

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'c1ea2aa71be0'
down_revision = '8927e62ab84e'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('codeaffranchissementfrauduleux_code_key', 'codeaffranchissementfrauduleux', type_='unique')
    op.create_index(op.f('ix_codeaffranchissementfrauduleux_code'), 'codeaffranchissementfrauduleux', ['code'], unique=True)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_codeaffranchissementfrauduleux_code'), table_name='codeaffranchissementfrauduleux')
    op.create_unique_constraint('codeaffranchissementfrauduleux_code_key', 'codeaffranchissementfrauduleux', ['code'])
    # ### end Alembic commands ###
