"""empty message

Revision ID: 5236f9cc2f35
Revises: 408a28072368
Create Date: 2025-05-31 07:17:58.358857

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from alembic_postgresql_enum import TableReference
from sqlalchemy.dialects import postgresql
from sqlalchemy import text


# revision identifiers, used by Alembic.
revision = '5236f9cc2f35'
down_revision = '408a28072368'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('casier', sa.Column('lot_expediteur_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'casier', 'lotexpediteur', ['lot_expediteur_id'], ['id'])
    op.alter_column('enveloppe', 'destination_enveloppe',
               existing_type=postgresql.ENUM('METROPOLE', 'INTERNATIONAL', 'DOM_COM', name='destinationenveloppeenum'),
               nullable=False)
    op.drop_column('enveloppe', 'produit')
    sa.Enum('LV', 'LINT', 'PPI', 'LSP', 'NON_DETERMINE', name='produitenum').drop(op.get_bind())
    op.sync_enum_values(
        enum_schema='public',
        enum_name='destinationenveloppeenum',
        new_values=['METROPOLE', 'DOM_COM', 'INTERNATIONAL'],
        affected_columns=[TableReference(table_schema='public', table_name='enveloppe', column_name='destination_enveloppe')],
        enum_values_to_rename=[],
    )
    
    # Mise à jour des casiers existants pour associer le lot_expediteur via les enveloppes
    connection = op.get_bind()
    
    # Récupérer tous les casiers qui ont des enveloppes
    casiers_avec_enveloppes = connection.execute(
        text("""
        SELECT DISTINCT c.id, e.lot_expediteur_id 
        FROM casier c
        JOIN enveloppe e ON e.casier_id = c.id
        WHERE e.lot_expediteur_id IS NOT NULL
        """)
    ).fetchall()
    
    # Pour chaque casier, associer le lot_expediteur de la première enveloppe trouvée
    for casier_id, lot_expediteur_id in casiers_avec_enveloppes:
        connection.execute(
            text(f"""
            UPDATE casier 
            SET lot_expediteur_id = {lot_expediteur_id} 
            WHERE id = {casier_id} AND lot_expediteur_id IS NULL
            """)
        )
    
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='destinationenveloppeenum',
        new_values=['METROPOLE', 'INTERNATIONAL', 'DOM_COM'],
        affected_columns=[TableReference(table_schema='public', table_name='enveloppe', column_name='destination_enveloppe')],
        enum_values_to_rename=[],
    )
    sa.Enum('LV', 'LINT', 'PPI', 'LSP', 'NON_DETERMINE', name='produitenum').create(op.get_bind())
    op.add_column('enveloppe', sa.Column('produit', postgresql.ENUM('LV', 'LINT', 'PPI', 'LSP', 'NON_DETERMINE', name='produitenum', create_type=False), autoincrement=False, nullable=True))
    op.alter_column('enveloppe', 'destination_enveloppe',
               existing_type=postgresql.ENUM('METROPOLE', 'INTERNATIONAL', 'DOM_COM', name='destinationenveloppeenum'),
               nullable=True)
    op.drop_constraint(None, 'casier', type_='foreignkey')
    op.drop_column('casier', 'lot_expediteur_id')
    # ### end Alembic commands ###
