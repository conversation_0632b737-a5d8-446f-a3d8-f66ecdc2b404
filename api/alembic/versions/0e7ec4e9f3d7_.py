"""empty message

Revision ID: 0e7ec4e9f3d7
Revises: a93868428dcb
Create Date: 2025-03-24 11:57:00.903610

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '0e7ec4e9f3d7'
down_revision = 'a93868428dcb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('MANUEL', 'TRAITEMENT', name='sourceenveloppeenum').create(op.get_bind())
    op.alter_column('affranchissement', 'nature',
               existing_type=postgresql.ENUM('LV_20', 'LV_100', 'LV_250', 'LV_500', 'LV_1000', 'LV_2000', 'LSP_20', 'LSP_100', 'LSP_250', 'LSP_500', 'LSP_1000', 'LSP_2000', 'LINT_20', 'LINT_50', 'LINT_100', 'LINT_250', 'LINT_500', 'LINT_2000', 'PPI_50', 'PPI_100', 'PPI_250', 'PPI_500', 'PPI_1000', 'PPI_2000', 'LV_20_S', 'LV_100_S', 'LV_250_S', 'LV_500_S', 'LV_1000_S', 'LV_2000_S', 'LSP_20_S', 'LSP_100_S', 'LSP_250_S', 'LSP_500_S', 'LSP_1000_S', 'LSP_2000_S', 'LINT_20_S', 'LINT_50_S', 'LINT_100_S', 'LINT_250_S', 'LINT_500_S', 'LINT_2000_S', 'PPI_50_S', 'PPI_100_S', 'PPI_250_S', 'PPI_500_S', 'PPI_1000_S', 'PPI_2000_S', 'NON_DETERMINE', name='natureaffranchissementenum'),
               nullable=True)
    op.add_column('enveloppe', sa.Column('source', postgresql.ENUM('MANUEL', 'TRAITEMENT', name='sourceenveloppeenum', create_type=False), server_default='MANUEL', nullable=False))
    op.add_column('user', sa.Column('site_id', sa.Integer(), nullable=False, server_default='1'))
    op.create_foreign_key(None, 'user', 'site', ['site_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'user', type_='foreignkey')
    op.drop_column('user', 'site_id')
    op.drop_column('enveloppe', 'source')
    op.alter_column('affranchissement', 'nature',
               existing_type=postgresql.ENUM('LV_20', 'LV_100', 'LV_250', 'LV_500', 'LV_1000', 'LV_2000', 'LSP_20', 'LSP_100', 'LSP_250', 'LSP_500', 'LSP_1000', 'LSP_2000', 'LINT_20', 'LINT_50', 'LINT_100', 'LINT_250', 'LINT_500', 'LINT_2000', 'PPI_50', 'PPI_100', 'PPI_250', 'PPI_500', 'PPI_1000', 'PPI_2000', 'LV_20_S', 'LV_100_S', 'LV_250_S', 'LV_500_S', 'LV_1000_S', 'LV_2000_S', 'LSP_20_S', 'LSP_100_S', 'LSP_250_S', 'LSP_500_S', 'LSP_1000_S', 'LSP_2000_S', 'LINT_20_S', 'LINT_50_S', 'LINT_100_S', 'LINT_250_S', 'LINT_500_S', 'LINT_2000_S', 'PPI_50_S', 'PPI_100_S', 'PPI_250_S', 'PPI_500_S', 'PPI_1000_S', 'PPI_2000_S', 'NON_DETERMINE', name='natureaffranchissementenum'),
               nullable=False)
    sa.Enum('MANUEL', 'TRAITEMENT', name='sourceenveloppeenum').drop(op.get_bind())
    # ### end Alembic commands ###
