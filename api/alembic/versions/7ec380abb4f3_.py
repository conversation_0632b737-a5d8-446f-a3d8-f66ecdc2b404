"""empty message

Revision ID: 7ec380abb4f3
Revises: a4e9c897f3a6
Create Date: 2025-06-18 17:34:45.999763

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '7ec380abb4f3'
down_revision = 'a4e9c897f3a6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('casier', 'capacite_max')
    op.drop_column('casier', 'statut')
    sa.Enum('DISPONIBLE', 'PLEIN', name='statutcasierenum').drop(op.get_bind())
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('DISPONIBLE', 'PLEIN', name='statutcasierenum').create(op.get_bind())
    op.add_column('casier', sa.Column('statut', postgresql.ENUM('DISPONIBLE', 'PLEIN', name='statutcasierenum', create_type=False), autoincrement=False, nullable=False))
    op.add_column('casier', sa.Column('capacite_max', sa.INTEGER(), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
