"""empty message

Revision ID: a4e9c897f3a6
Revises: 5efb1b4b32fa
Create Date: 2025-06-18 09:30:29.185754

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from alembic_postgresql_enum import TableReference
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'a4e9c897f3a6'
down_revision = '5efb1b4b32fa'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('NON_INITIALISE', 'EN_ATTENTE', 'AUTORISE', 'PAYE', 'ECHEC', 'ANNULE', 'REMBOURSE', name='statutpaiementenum').create(op.get_bind())
    op.create_table('paiementlotexpediteur',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('id_paiement', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('lot_expediteur_id', sa.Integer(), nullable=False),
    sa.Column('methode', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('id_transaction', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('donnees', sa.JSON(), nullable=False),
    sa.Column('numero_paiement', sa.Integer(), nullable=False),
    sa.Column('pourcentage', sa.Float(), nullable=False),
    sa.Column('montant_ttc', sa.Float(), nullable=False),
    sa.Column('statut', postgresql.ENUM('NON_INITIALISE', 'EN_ATTENTE', 'AUTORISE', 'PAYE', 'ECHEC', 'ANNULE', 'REMBOURSE', name='statutpaiementenum', create_type=False), nullable=False),
    sa.Column('date_creation', sa.DateTime(), nullable=False),
    sa.Column('date_paiement', sa.DateTime(), nullable=True),
    sa.Column('date_echeance', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['lot_expediteur_id'], ['lotexpediteur.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id_paiement'),
    sa.UniqueConstraint('id_transaction')
    )
    op.add_column('lotexpediteur', sa.Column('id_public', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True))
    op.add_column('lotexpediteur', sa.Column('montant_ttc', sa.Float(), nullable=True))
    op.add_column('lotexpediteur', sa.Column('user_modification_id', sa.Integer(), nullable=True))
    op.create_unique_constraint(None, 'lotexpediteur', ['id_public'])
    op.create_foreign_key(None, 'lotexpediteur', 'user', ['user_modification_id'], ['id'])
    op.sync_enum_values(
        enum_schema='public',
        enum_name='statutlotexpediteurenum',
        new_values=['OUVERT', 'FERME', 'NOTIFIE', 'PAIEMENT_PARTIEL', 'PAIEMENT_TOTAL_RECU', 'TRAITE_LIBERABLE', 'TRAITE_LIBOURNE', 'TRAITE_PPDC', 'REMB_PARTIEL', 'TRAITE_OPERATIONNEL', 'ARRIVEE_LIBOURNE', 'VALORISER_LIBOURNE', 'CONTENTIEUX'],
        affected_columns=[TableReference(table_schema='public', table_name='lotexpediteur', column_name='statut')],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='statutlotexpediteurenum',
        new_values=['OUVERT', 'NOTIFIE', 'PAIEMENT_RECU', 'TRAITE', 'ANNULE'],
        affected_columns=[TableReference(table_schema='public', table_name='lotexpediteur', column_name='statut')],
        enum_values_to_rename=[],
    )
    op.drop_constraint(None, 'lotexpediteur', type_='foreignkey')
    op.drop_constraint(None, 'lotexpediteur', type_='unique')
    op.drop_column('lotexpediteur', 'user_modification_id')
    op.drop_column('lotexpediteur', 'montant_ttc')
    op.drop_column('lotexpediteur', 'id_public')
    op.drop_table('paiementlotexpediteur')
    sa.Enum('NON_INITIALISE', 'EN_ATTENTE', 'AUTORISE', 'PAYE', 'ECHEC', 'ANNULE', 'REMBOURSE', name='statutpaiementenum').drop(op.get_bind())
    # ### end Alembic commands ###
