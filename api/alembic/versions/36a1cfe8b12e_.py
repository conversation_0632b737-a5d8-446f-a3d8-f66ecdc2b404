"""empty message

Revision ID: 36a1cfe8b12e
Revises: c1ea2aa71be0
Create Date: 2025-04-20 13:00:47.077682

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from alembic_postgresql_enum import TableReference
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '36a1cfe8b12e'
down_revision = 'c1ea2aa71be0'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # op.drop_column('codeaffranchissementfrauduleux', 'type')
    op.sync_enum_values(
        enum_schema='public',
        enum_name='categorieaffranchissementenum',
        new_values=['CODE', 'MARI', 'BEAU', 'MAFF', 'T', 'VIGN', 'PAP'],
        affected_columns=[TableReference(table_schema='public', table_name='affranchissement', column_name='categorie')],
        enum_values_to_rename=[],
    )
    op.sync_enum_values(
        enum_schema='public',
        enum_name='typeaffranchissementenum',
        new_values=['SD86', 'SD87', 'SD88', 'SD', 'SDMTEL', 'SDIND', 'S10', 'TNUM', 'LV20G', 'LP20G', 'INTMON20G', 'LSP', 'ECO20G', 'EUR20G', 'VAL', 'DEWD1', 'INTER20G', 'GUIC', 'AUTO', 'T', 'NON_DETERMINE', 'PAP_S_500', 'PAP_XS_250'],
        affected_columns=[TableReference(table_schema='public', table_name='affranchissement', column_name='type'), TableReference(table_schema='public', table_name='reglemetier', column_name='type_affranchissement')],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='typeaffranchissementenum',
        new_values=['SD86', 'SD87', 'SD88', 'SD', 'SDMTEL', 'SDIND', 'S10', 'TNUM', 'LV20G', 'LP20G', 'INTMON20G', 'LSP', 'ECO20G', 'EUR20G', 'VAL', 'DEWD1', 'INTER20G', 'GUIC', 'AUTO', 'T', 'NON_DETERMINE'],
        affected_columns=[TableReference(table_schema='public', table_name='affranchissement', column_name='type'), TableReference(table_schema='public', table_name='reglemetier', column_name='type_affranchissement')],
        enum_values_to_rename=[],
    )
    op.sync_enum_values(
        enum_schema='public',
        enum_name='categorieaffranchissementenum',
        new_values=['CODE', 'MARI', 'BEAU', 'MAFF', 'T', 'VIGN'],
        affected_columns=[TableReference(table_schema='public', table_name='affranchissement', column_name='categorie')],
        enum_values_to_rename=[],
    )
    # op.add_column('codeaffranchissementfrauduleux', sa.Column('type', postgresql.ENUM('SD86', 'SD87', 'SD88', 'SD', 'SDMTEL', 'SDIND', 'S10', 'TNUM', 'LV20G', 'LP20G', 'INTMON20G', 'LSP', 'ECO20G', 'EUR20G', 'VAL', 'DEWD1', 'INTER20G', 'GUIC', 'AUTO', 'T', 'NON_DETERMINE', name='typeaffranchissementenum', create_type=False), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
