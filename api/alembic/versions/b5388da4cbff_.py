"""empty message

Revision ID: b5388da4cbff
Revises: cb49e8d6dc9c
Create Date: 2025-04-10 19:03:55.150416

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b5388da4cbff'
down_revision = 'cb49e8d6dc9c'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('casier', sa.Column('lot_expediteur_id', sa.Integer(), nullable=True))
    op.create_foreign_key(None, 'casier', 'lotexpediteur', ['lot_expediteur_id'], ['id'])
    op.drop_column('casier', 'statut')
    op.drop_constraint('lotexpediteur_casier_id_fkey', 'lotexpediteur', type_='foreignkey')
    op.drop_column('lotexpediteur', 'casier_id')
    sa.Enum('DISPONIBLE', 'OCCUPE', 'MAINTENANCE', name='statutcasierenum').drop(op.get_bind())
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('DISPONIBLE', 'OCCUPE', 'MAINTENANCE', name='statutcasierenum').create(op.get_bind())
    op.add_column('lotexpediteur', sa.Column('casier_id', sa.INTEGER(), autoincrement=False, nullable=True))
    op.create_foreign_key('lotexpediteur_casier_id_fkey', 'lotexpediteur', 'casier', ['casier_id'], ['id'])
    op.add_column('casier', sa.Column('statut', postgresql.ENUM('DISPONIBLE', 'OCCUPE', 'MAINTENANCE', name='statutcasierenum', create_type=False), autoincrement=False, nullable=False))
    op.drop_constraint(None, 'casier', type_='foreignkey')
    op.drop_column('casier', 'lot_expediteur_id')
    # ### end Alembic commands ###
