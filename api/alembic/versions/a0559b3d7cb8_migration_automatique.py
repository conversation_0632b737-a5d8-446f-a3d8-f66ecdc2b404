"""Migration automatique

Revision ID: a0559b3d7cb8
Revises: 
Create Date: 2025-03-13 12:42:28.315010

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'a0559b3d7cb8'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('codeaffranchissementfrauduleux',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('code', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('type', sa.Enum('SD86', 'SD87', 'SD88', 'SD', 'SDMTEL', 'SDIND', 'S10', 'TNUM', 'LV20G', 'LP20G', 'INTMON20G', 'LSP', 'ECO20G', 'EUR20G', 'VAL', 'DEWD1', 'INTER20G', 'GUIC', 'AUTO', 'T', 'NON_DETERMINE', name='typeaffranchissementenum'), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('code')
    )
    op.create_table('destination',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('id_migration', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('nom_fr', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('nom_en', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('alpha2', sqlmodel.sql.sqltypes.AutoString(length=2), nullable=True),
    sa.Column('alpha3', sqlmodel.sql.sqltypes.AutoString(length=3), nullable=True),
    sa.Column('num', sa.Integer(), nullable=True),
    sa.Column('zone', sqlmodel.sql.sqltypes.AutoString(length=3), nullable=True),
    sa.Column('supplement', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id_migration')
    )
    op.create_table('expediteur',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('id_migration', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('prenom', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('nom', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('adresse', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('ville', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('code_postal', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('pays', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('siret', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('coclico', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id_migration')
    )
    op.create_table('reglemetier',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('cle', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('type_affranchissement', sa.Enum('SD86', 'SD87', 'SD88', 'SD', 'SDMTEL', 'SDIND', 'S10', 'TNUM', 'LV20G', 'LP20G', 'INTMON20G', 'LSP', 'ECO20G', 'EUR20G', 'VAL', 'DEWD1', 'INTER20G', 'GUIC', 'AUTO', 'T', 'NON_DETERMINE', name='typeaffranchissementenum'), nullable=True),
    sa.Column('type_regle', sa.Enum('SEQUENCE', 'VALEUR', name='typereglemetierenum'), nullable=False),
    sa.Column('valeur', sa.JSON(), nullable=False),
    sa.Column('active', sa.Boolean(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('cle', 'type_regle', 'type_affranchissement', name='uq_cle_type_regle_type_affranchissement')
    )
    op.create_table('site',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('id_migration', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('nom', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id_migration')
    )
    op.create_table('traitementlotenveloppes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('user',
    sa.Column('email', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('role', sqlmodel.sql.sqltypes.AutoString(length=25), nullable=True),
    sa.Column('full_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('pseudo', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('profile_picture', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('hashed_password', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('token_ajout_photo', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_email'), 'user', ['email'], unique=True)
    op.create_table('boiteenveloppes',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('cle', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('description', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('site_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['site_id'], ['site.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('cle')
    )
    op.create_table('enveloppe',
    sa.Column('statut', sa.Enum('EDITION', 'VALORISE', 'TERMINEE', 'PAUSE', name='statutenveloppeenum'), nullable=False),
    sa.Column('poids', sa.Float(), nullable=False),
    sa.Column('surpoids', sa.Boolean(), nullable=False),
    sa.Column('surdimensionne', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('id_migration', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('uuid', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('produit', sa.Enum('LV', 'LINT', 'PPI', 'LSP', 'NON_DETERMINE', name='produitenum'), nullable=False),
    sa.Column('valorisation', sa.JSON(), nullable=True),
    sa.Column('traitement_lot_enveloppes_id', sa.Integer(), nullable=True),
    sa.Column('expediteur_id', sa.Integer(), nullable=True),
    sa.Column('destination_id', sa.Integer(), nullable=True),
    sa.Column('site_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['destination_id'], ['destination.id'], ),
    sa.ForeignKeyConstraint(['expediteur_id'], ['expediteur.id'], ),
    sa.ForeignKeyConstraint(['site_id'], ['site.id'], ),
    sa.ForeignKeyConstraint(['traitement_lot_enveloppes_id'], ['traitementlotenveloppes.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id_migration'),
    sa.UniqueConstraint('uuid')
    )
    op.create_table('lotexpediteur',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('nom', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('expediteur_id', sa.Integer(), nullable=True),
    sa.Column('traitement_lot_enveloppes_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['expediteur_id'], ['expediteur.id'], ),
    sa.ForeignKeyConstraint(['traitement_lot_enveloppes_id'], ['traitementlotenveloppes.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('messageexpediteur',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('expediteur_id', sa.Integer(), nullable=True),
    sa.Column('traitement_lot_enveloppes_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['expediteur_id'], ['expediteur.id'], ),
    sa.ForeignKeyConstraint(['traitement_lot_enveloppes_id'], ['traitementlotenveloppes.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('affranchissement',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('id_migration', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('categorie', sa.Enum('CODE', 'MARI', 'BEAU', 'MAFF', 'T', 'VIGN', name='categorieaffranchissementenum'), nullable=False),
    sa.Column('type', sa.Enum('SD86', 'SD87', 'SD88', 'SD', 'SDMTEL', 'SDIND', 'S10', 'TNUM', 'LV20G', 'LP20G', 'INTMON20G', 'LSP', 'ECO20G', 'EUR20G', 'VAL', 'DEWD1', 'INTER20G', 'GUIC', 'AUTO', 'T', 'NON_DETERMINE', name='typeaffranchissementenum'), nullable=False),
    sa.Column('sous_type', sa.Enum('SDN', 'SDX', 'NON_DETERMINE', name='soustypeaffranchissementenum'), nullable=False),
    sa.Column('nature', sa.Enum('LV_20', 'LV_100', 'LV_250', 'LV_500', 'LV_1000', 'LV_2000', 'LSP_20', 'LSP_100', 'LSP_250', 'LSP_500', 'LSP_1000', 'LSP_2000', 'LINT_20', 'LINT_50', 'LINT_100', 'LINT_250', 'LINT_500', 'LINT_2000', 'PPI_50', 'PPI_100', 'PPI_250', 'PPI_500', 'PPI_1000', 'PPI_2000', 'LV_20_S', 'LV_100_S', 'LV_250_S', 'LV_500_S', 'LV_1000_S', 'LV_2000_S', 'LSP_20_S', 'LSP_100_S', 'LSP_250_S', 'LSP_500_S', 'LSP_1000_S', 'LSP_2000_S', 'LINT_20_S', 'LINT_50_S', 'LINT_100_S', 'LINT_250_S', 'LINT_500_S', 'LINT_2000_S', 'PPI_50_S', 'PPI_100_S', 'PPI_250_S', 'PPI_500_S', 'PPI_1000_S', 'PPI_2000_S', 'NON_DETERMINE', name='natureaffranchissementenum'), nullable=False),
    sa.Column('prix_unite_devise', sa.Float(), nullable=True),
    sa.Column('devise', sa.Enum('EURO', 'FRANCS', 'ANCIEN_FRANCS', name='deviseenum'), nullable=False),
    sa.Column('quantite', sa.Integer(), nullable=False),
    sa.Column('poids_max', sa.Float(), nullable=False),
    sa.Column('code', sqlmodel.sql.sqltypes.AutoString(length=200), nullable=False),
    sa.Column('statut', sa.Enum('VALIDE', 'INVALIDE', name='validiteaffranchissementenum'), nullable=False),
    sa.Column('donnees', sa.JSON(), nullable=False),
    sa.Column('enveloppe_id', sa.Integer(), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['enveloppe_id'], ['enveloppe.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id_migration')
    )
    op.create_table('photoenveloppe',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('format', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('qualite', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('largeur', sa.Float(), nullable=True),
    sa.Column('hauteur', sa.Float(), nullable=True),
    sa.Column('orientation', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('url', sqlmodel.sql.sqltypes.AutoString(length=2048), nullable=True),
    sa.Column('commentaire', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('enveloppe_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['enveloppe_id'], ['enveloppe.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('verificationaffranchissement',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('type', sa.Enum('COUNT', 'WISSOU', 'CODE_FRAUDULEUX', 'TIMBRE', 'TARIF', 'SSU_TRACKING', 'SSU_PURCHASE', 'GRAMMAIRE', 'GRAMMAIRE_ORIGIN', 'GRAMMAIRE_CLE', 'GRAMMAIRE_SERVICE', 'GRAMMAIRE_COUNTRY', 'GRAMMAIRE_SIGNATURE', 'SEQUENCE', 'AUTRE', name='typeverificationaffranchissementenum'), nullable=False),
    sa.Column('statut', sa.Enum('VALIDE', 'INVALIDE', 'NON_DETERMINE', name='statutverificationenum'), nullable=False),
    sa.Column('message', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('donnees', sa.JSON(), nullable=False),
    sa.Column('affranchissement_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['affranchissement_id'], ['affranchissement.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('verificationaffranchissement')
    op.drop_table('photoenveloppe')
    op.drop_table('affranchissement')
    op.drop_table('messageexpediteur')
    op.drop_table('lotexpediteur')
    op.drop_table('enveloppe')
    op.drop_table('boiteenveloppes')
    op.drop_index(op.f('ix_user_email'), table_name='user')
    op.drop_table('user')
    op.drop_table('traitementlotenveloppes')
    op.drop_table('site')
    op.drop_table('reglemetier')
    op.drop_table('expediteur')
    op.drop_table('destination')
    op.drop_table('codeaffranchissementfrauduleux')
    # ### end Alembic commands ###
