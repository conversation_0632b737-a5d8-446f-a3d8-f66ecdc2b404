from models import Expediteur, Destination, Site, Enveloppe, PhotoEnveloppe, VerificationAffranchissement
from constants.enumerations import SourceEnveloppeEnum, StatutVerificationEnum, TypeVerificationAffranchissementEnum
from core.utils.prix import convertir_prix_devise_en_euros
from sqlmodel import Session, select
from core.db import get_session
from services.affranchissement import ServiceAffranchissement
from models.users import User

class ServiceMigration:
    
    @classmethod
    def create_enveloppe(cls, source, no_relations=False, session=None):
        """
        Créer une enveloppe à partir d'un document Elasticsearch (POC source)
        """
        session = session or get_session()
    
        # Créer un utilisateur "IMPORT"
        user = session.exec(
            select(User).where(User.email == "<EMAIL>")
        ).first()

        if not user:
            user = User(
                email="<EMAIL>",
                hashed_password="import",
                role="ADMIN",
                site_id=session.exec(select(Site)).first().id
            )
            session.add(user)
            session.commit()
            
        # Filter and validate sender data
        id = source.get('_id')
        source = source.get('_source', {})
        sender_data = source.get('sender', {})
        main_data = source.get('main', {})

        # Enveloppe already exists ?
        if session.exec(
            select(Enveloppe).where(Enveloppe.id_migration == id)
        ).first():
            return

        # Identify relations :
        # Exepediteur (senderId)
        # Destination (destinationId)
        # Product (productId)
        # Site (siteId)
        expediteur = None
        destination = None
        site = None
        
        id_sender = main_data.pop('idSender')
        if id_sender:
            expediteur = session.exec(
                select(Expediteur).where(Expediteur.id_migration == id_sender)
            ).first()
            if not expediteur and not no_relations:
                raise ValueError(f"Expediteur with id_migration {id_sender} not found")
    

        id_destination = main_data.pop('idDestination')
        if id_destination:
            destination = session.exec(
                select(Destination).where(Destination.id_migration == id_destination)
            ).first()
            if not destination and not no_relations:
                raise ValueError(f"Destination with id_migration {id_destination} not found")
    

        # Site traitement
        id_site = main_data.pop('idSite')
        site = session.exec(
                select(Site).where(Site.id_migration == id_site)
            ).first()
        if not site:
            site = Site(
                id_migration=id_site,
                nom=id_site
            )
            session.add(site)

        # Create Enveloppe
        enveloppe = Enveloppe(
            produit=main_data.pop("idProduct"),
            statut="EDITION",
            statut_verification="NON_DETERMINE",
            poids=main_data.pop("weight", None),
            surpoids=main_data.pop("overweight"),
            surdimensionne=main_data.pop("outofsize"),
            created_at=source.get('@timestamp'),
            updated_at=source.pop('@timestamp'),
            expediteur=expediteur,
            destination_enveloppe=Destination,
            site=site,
            user=user,
            id_migration=id,
            source=SourceEnveloppeEnum.IMPORT
        )
        
        session.add(enveloppe)
        # session.commit()

        # Remove image/data from pictures
        for index, picture in enumerate(main_data.get('pictures', [])):
            photo = PhotoEnveloppe(
                enveloppe=enveloppe,
                format=picture.pop('format'),
                qualite=picture.pop('quality'),
                largeur=picture.pop('width'),
                hauteur=picture.pop('height'),
                orientation=picture.pop('orientation')
            )
            session.add(photo)
            # session.commit()

            # Convertir le dataUrl en bytes
            # image_data = base64.b64decode(picture['dataUrl'].split(',')[1])
                            
            # # Stream upload to Azure Blob
            # connection_str = os.getenv("AZURE_BLOB_CONNECTION_STRING")
            # container_name = os.getenv("AZURE_BLOB_CONTAINER_NAME")

            # blob_service_client = BlobServiceClient.from_connection_string(connection_str)
            # blob_client = blob_service_client.get_blob_client(
            #     container=container_name,
            #     blob=f"enveloppes/photos/{enveloppe.id}/{photo.id}.jpg"                
            # )
            # blob_client.upload_blob(image_data, overwrite=False)

            # Remove picture of main_data
            main_data['pictures'].pop(index)
        

        # session.refresh(enveloppe)
        
        DEVISE_MAPPING = {
            "E": "EURO",
            "F": "FRANCS",
            "A": "ANCIEN_FRANCS"
        }
        
        # Filter and create Affranchissements
        for element_origin in main_data.get('elements', []):
            element = element_origin.copy()

            format_dict = element.pop('code', {}) or {}
            rules = format_dict.pop('rules', {})
            format = format_dict.pop("format", None)
            idCodeMain = format_dict.pop("idCodeMain", None)
            count = format_dict.pop("count", None)
            code = format_dict.pop("raw", None)
            checks = format_dict.pop("check", {})
            
            if "content" in format_dict:
                format_dict['content'] = format_dict['content'][format.lower()]
        
            devise = DEVISE_MAPPING.get(element.pop('unit', 'E'))        
            valueEuro = format_dict.pop("valueEuro", None)
            valueEuro2 = element.pop('valueEuro', None)
            prix_unite_devise = element.pop('valueUnit', None)
            
            price_unit_euros = convertir_prix_devise_en_euros(devise, prix_unite_devise)
            prix_unite_euros = max(valueEuro or 0, price_unit_euros or 0)
            prix_unite_euros = max(valueEuro2 or 0, prix_unite_euros)
                        
            nature = element.pop('idNature')
            categorie = element.pop('idCategory')
            type = element.pop('idMention')

            
            
            affranchissement = ServiceAffranchissement.create_affranchissement({
                "categorie": categorie,
                "type": type,
                "nature": nature,
                "devise": devise,
                "prix_unite_euros": prix_unite_euros,
                "prix_unite_devise": prix_unite_devise,
                "quantite": element.pop('quantity', 1),
                "code": code,
                "format": format_dict
            })
            
            if element.pop('fake') == True:
                affranchissement.statut = StatutVerificationEnum.INVALIDE
                affranchissement.prix_unite_euros = 0
                
            affranchissement.enveloppe = enveloppe
            affranchissement.user = user
            session.add(affranchissement)                
            
            # Mapping
            def check_mapper_to_verifications(field, checks, affranchissement):
                check = checks.get(field)
                
                if check is None:
                    return []

                details = check["result"]["details"]
                verifs = []

                mapping = [
                    {
                        "text": "sur 1 jour maxi",
                        "field": "ssu",
                        "data": "tracking",
                        "type": TypeVerificationAffranchissementEnum.SSU_TRACKING
                    },
                    {
                        "text": "Achat trouvé",
                        "field": "ssu",
                        "data": "purchase",
                        "type": TypeVerificationAffranchissementEnum.SSU_PURCHASE
                    },
                    {
                        "text": "service",
                        "field": "grammar",
                        "data": None,
                        "type": TypeVerificationAffranchissementEnum.GRAMMAIRE_SERVICE
                    },
                    {
                        "text": "key",
                        "field": "grammar",
                        "data": None,
                        "type": TypeVerificationAffranchissementEnum.GRAMMAIRE_CLE
                    },
                    {
                        "text": "country",
                        "field": "grammar",
                        "data": None,
                        "type": TypeVerificationAffranchissementEnum.GRAMMAIRE_COUNTRY
                    },
                    {
                        "text": "origin",
                        "field": "grammar",
                        "data": None,
                        "type": TypeVerificationAffranchissementEnum.GRAMMAIRE_ORIGIN
                    },
                    {
                        "text": "Doit être scanné",
                        "field": "count",
                        "data": True,
                        "type": TypeVerificationAffranchissementEnum.COUNT
                    },
                    {
                        "text": "jamais scanné",
                        "field": "count",
                        "data": True,
                        "type": TypeVerificationAffranchissementEnum.COUNT
                    }
                ]

                # Mapping des états
                state_mapping = {
                    "OK": StatutVerificationEnum.VALIDE,
                    "KO": StatutVerificationEnum.INVALIDE
                }

                for detail in details:
                    # Utilisation du dictionnaire de correspondance pour déterminer l'état
                    state = next((state_mapping[key] for key in state_mapping if key in detail), StatutVerificationEnum.NON_DETERMINE)

                    for item in mapping:
                        if item['text'] in detail and item['field'] == field:

                            if item['data'] is not None:
                                if item['data'] == True:
                                    donnees = check.get("data", {})
                                else:
                                    donnees = check.get("data", {}).get(item['data'])
                            else:
                                donnees = {}    


                            session.add(
                                VerificationAffranchissement(
                                    type=item['type'],
                                    statut=state,
                                    message=detail,
                                    donnees=donnees,
                                    affranchissement=affranchissement
                                )
                            )
                            break  # Exit the loop once a match is found

                return verifs
            
            # if(checks != {}):
            #     print(checks)
            #     raise ValueError("OK")

            # Migre les verifications)
            check_mapper_to_verifications('grammar', checks, affranchissement)
            check_mapper_to_verifications('count', checks, affranchissement)
            check_mapper_to_verifications('ssu', checks, affranchissement)


        valo = source.get('valo', {})
            
        # Valorisation
        enveloppe.valorisation = {
            'postage': {
                'cout_enveloppe': valo.get('postage', {}).get('postageDue', 0),
                'cout_affranchissements_valide': valo.get('postage', {}).get('postageObserved', 0),
                'nb_affranchissements_invalides': valo.get('postage', {}).get('postageFakeCount', 0),
                'montant_sous_affranchissement': valo.get('postage', {}).get('postageToRecover', 0),
                'presence_affranchissements_invalide': valo.get('postage', {}).get('postageFakeOne', False),
                'presence_taxe': valo.get('postage', {}).get('postageTaxOne', False),
                'coeff_taxe_livraison': valo.get('deliver', {}).get('deliverTaxCoeff', 0),
            },
            'livraison': {
                'taxe_livraison_a_recuperer': valo.get('deliver', {}).get('deliverTaxToRecover', 0),
                'taxe_livraison_fixe': valo.get('deliver', {}).get('deliverTaxSet', 0),
                'taxe_livraison_totale': valo.get('deliver', {}).get('deliverTaxTotal', 0),
                'cout_total': valo.get('deliver', {}).get('deliverTotalToRecover', 0)
            },
            'collecte': {
                'frais_collecte_ht': valo.get('collect', {}).get('collectFeesHT', 0),
                'coeff_collecte_tva': valo.get('collect', {}).get('collectTVACoeff', 0),
                'cout_ht': valo.get('collect', {}).get('collectTotalToRecoverHT', 0),
                'cout_tva': valo.get('collect', {}).get('collectTotalToRecoverTVA', 0),
                'cout_ttc': valo.get('collect', {}).get('collectTotalToRecoverTTC', 0)
            },
            'expédition': {
                'frais_expédition': valo.get('shipping', {}).get('shippingPostageDue', 0),
                'cout_ht': valo.get('shipping', {}).get('shippingTotalToRecoverHT', 0),
                'cout_tva': valo.get('shipping', {}).get('shippingTotalToRecoverTVA', 0),
                'cout_ttc': valo.get('shipping', {}).get('shippingTotalToRecoverTTC', 0)
            }
        }
        session.add(enveloppe)
        # session.commit()          
        # session.refresh(enveloppe)            
            
                
        return enveloppe
