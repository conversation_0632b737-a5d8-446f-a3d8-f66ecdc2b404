import os
import requests

class SSUApi:
    @staticmethod
    def get_headers():
        return {
            "Accept": "application/json",
            "X-Okapi-Key": os.environ["OKAPI_KEY"],
        }

    @classmethod
    def get_tracking(cls, sid):
        if not os.getenv("SSU_BASE_URL"):
            return None, "SSU non configuré"
            
        url = f"{os.environ['SSU_BASE_URL']}/suivi-unifie/idship/{sid}"
        
        try:
            response = requests.get(url, headers=cls.get_headers())
            return response.json(), None
        except requests.exceptions.RequestException:
            return None, "SSU non joignable"

    @classmethod
    def get_purchase(cls, sid):
        if not os.getenv("SSU_BASE_URL"):
            return None, "SSU non configuré"
            
        url = f"{os.environ['SSU_BASE_URL']}/shipments?idShips={sid}&fields=purchase"
        
        try:
            response = requests.get(url, headers=cls.get_headers())
            return response.json(), None
        except requests.exceptions.RequestException:
            return None, "SSU non joignable " 