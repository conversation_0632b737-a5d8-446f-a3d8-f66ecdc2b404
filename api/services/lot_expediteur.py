from datetime import datetime
from typing import List, Optional, Dict
from sqlalchemy.orm import Session
from sqlalchemy import func
from sqlalchemy.orm import joinedload
from models.encaissement import FractionnementPaiement
from models.business import Enveloppe, Expediteur, LotExpediteur, <PERSON><PERSON><PERSON>, <PERSON>le<PERSON><PERSON><PERSON>, User, PaiementLotExpediteur
from constants.enumerations import TypeRegleMetierEnum, StatutLotExpediteurEnum, OptionTraitementLotExpediteurEnum, StatutEnveloppeEnum
from services.casier import ServiceCasier
from fastapi import HTTPException
import uuid


class ServiceLotExpediteur:
    STATUT_CONCERNES = [StatutEnveloppeEnum.FRAUDULEUSE, StatutEnveloppeEnum.SOUS_AFFRANCHI]
    
    def __init__(self, session: Session):
        self.session = session

    def lot_expediteur(self, enveloppe: Enveloppe):
        """
        Pour une Enveloppe et son Expéditeur, retourne le LotExpediteur associé et le Casier disponible.
        Si l'enveloppe est frauduleuse, crée un nouveau lot si nécessaire ou retourne le lot existant.
        """
        if not enveloppe or not enveloppe.expediteur_id or enveloppe.casier_id is not None:
            return

        if enveloppe.statut not in self.STATUT_CONCERNES:
            return 

        # possibilité d'avoir 2 lots ouverts pour 2 site différents
        # Recherche un lot existant pour le site de l'utilisateur courant
        lot = self.lot_existant_pour(enveloppe)
        
        # Pas de lot existant, on crée
        if not lot:
            if self.verifier_besoin_creation_lot(enveloppe.expediteur_id):
                lot = self.creer_lot_expediteur(enveloppe)
            else:
                return
    

        # Ajouter l'enveloppe au lot existant
        enveloppe.casier = lot.casier
        enveloppe.lot_expediteur = lot
        self.session.commit()

    def verifier_besoin_creation_lot(self, expediteur_id: int) -> bool:
        """Vérifie si un lot doit être créé pour un Expéditeur."""
        count = self.session.query(func.count(Enveloppe.id)).filter(
            Enveloppe.expediteur_id == expediteur_id,
            Enveloppe.statut.in_(self.STATUT_CONCERNES)
        ).scalar()

        seuil = RegleMetier.valeur_pour(self.session, 
                                        cle="SEUIL_LOT_EXPEDITEUR", 
                                        type_regle=TypeRegleMetierEnum.VALEUR,
                                        valeur_defaut=10)

        return count >= seuil
    
    def lot_existant_pour(self, enveloppe: Enveloppe) -> Optional[LotExpediteur]:
        """
        Vérifie si l'expéditeur est déjà associé à un lot existant ouvert.
        """
        expediteur = enveloppe.expediteur

        if not expediteur:
            return None
        
        return self.session.query(LotExpediteur).filter(
            LotExpediteur.expediteur_id == expediteur.id,
            LotExpediteur.statut == StatutLotExpediteurEnum.OUVERT,
            LotExpediteur.site_id == enveloppe.site_id
        ).first()
    
    def attribuer_casier_lot(self, lot : LotExpediteur):
         # Trouver un casier disponible
        lot.casier = ServiceCasier.obtenir_casier_disponible(lot.site)

        if lot.casier is None:
            raise HTTPException(status_code=400, detail=f"Impossible de trouver un casier disponible pour cet expéditeur sur le site {lot.site.nom}")

        # Spécifier que ce Casier est désormais rattaché à ce Lot
        lot.casier.lot_expediteur_id = lot.id

        self.session.commit()
        
    
    def creer_lot_expediteur(self, enveloppe: Enveloppe) -> LotExpediteur:
        """Crée un lot pour un expéditeur avec une première enveloppe frauduleuse."""
        lot = LotExpediteur(
            expediteur_id=enveloppe.expediteur_id,
            statut=StatutLotExpediteurEnum.OUVERT,
            date_creation=datetime.utcnow(),
            site=enveloppe.site
        )
        
        self.session.add(lot)       

        self.attribuer_casier_lot(lot)

        return lot
    
    def changer_statut_lot(self, lot_id: int, statut: StatutLotExpediteurEnum) -> Optional[LotExpediteur]:
        """Change le statut d'un lot."""
        lot = self.session.query(LotExpediteur).filter(LotExpediteur.id == lot_id).first()
        if not lot:
            return None
            
        lot.statut = statut
        self.session.commit()
        return lot
        
    def appliquer_option_recouvrement(
        self, 
        lot: LotExpediteur, 
        option: OptionTraitementLotExpediteurEnum,
        params: Optional[dict] = None
    ) -> Optional[LotExpediteur]:
        """Applique l'option de recouvrement choisie par l'expéditeur, en vérifiant les règles métier."""

        # Vérification métier : restreindre les options selon le statut ou d'autres critères
        options_possibles = self.options_possibles_pour_lot(lot)
        if option not in options_possibles:
            raise HTTPException(status_code=400, detail="Option non autorisée pour ce lot")
        
        lot.option_recouvrement = option
        lot.option_recouvrement_params = params
        
        self.session.commit()
        return lot

    def options_possibles_pour_lot(self, lot: LotExpediteur) -> list:
        """
        Retourne la liste des options possibles pour un lot selon les règles métier.
        (Exemple : toutes les options sauf si restriction future)
        """
        # TODO : Ajouter ici la logique métier pour restreindre les options si besoin
        return [
            OptionTraitementLotExpediteurEnum.LIVRAISON_DESTINATAIRES,
            OptionTraitementLotExpediteurEnum.RECUPERATION_SUR_SITE,
            OptionTraitementLotExpediteurEnum.RENVOI_EXPEDITEUR
        ]
       
        
    def liberer_casiers(self, lot: LotExpediteur) -> None:
        """Libère tous les casiers associés à un lot."""

        # Relacher l'ensemble des Casier associées        
        for casier in lot.casiers:
            casier.lot_expediteur_id = None

        self.session.commit()

        # Relâcher le casier
        if lot.casier:
            lot.casier_id = None

        # Pour toutes les enveloppes du Lot
        for enveloppe in lot.enveloppes:
            # Changer le statut de l'enveloppe
            enveloppe.casier_id = None

    @classmethod
    def calcul_somme_valorisation(cls, lot):
        # Initialisation des compteurs
        cumul = {
            'postage': {
                'cout_enveloppe': 0,
                'cout_affranchissements_valide': 0,
                'nb_affranchissements_invalides': 0,
                'montant_sous_affranchissement': 0,
                'presence_affranchissements_invalide': False,
                'presence_taxe': False,
            },
            'livraison': {
                'taxe_livraison_a_recuperer': 0,
                'taxe_livraison_fixe': 0,
                'taxe_livraison_totale': 0,
                'cout_total': 0
            },
            'collecte': {
                'cout_ht': 0,
                'cout_tva': 0,
                'cout_ttc': 0
            },
            'expédition': {
                'cout_ht': 0,
                'cout_tva': 0,
                'cout_ttc': 0
            }
        }
        
        # Parcourir toutes les enveloppes du lot
        for enveloppe in lot.enveloppes:
            if not enveloppe.valorisation:
                continue
                
            # Cumul des valeurs de postage
            if 'postage' in enveloppe.valorisation:
                postage = enveloppe.valorisation['postage']
                cumul['postage']['cout_enveloppe'] += postage.get('cout_enveloppe', 0)
                cumul['postage']['cout_affranchissements_valide'] += postage.get('cout_affranchissements_valide', 0)
                cumul['postage']['nb_affranchissements_invalides'] += postage.get('nb_affranchissements_invalides', 0)
                cumul['postage']['montant_sous_affranchissement'] += postage.get('montant_sous_affranchissement', 0)
                cumul['postage']['presence_affranchissements_invalide'] |= postage.get('presence_affranchissements_invalide', False)
                cumul['postage']['presence_taxe'] |= postage.get('presence_taxe', False)
            
            # Cumul des valeurs de livraison
            if 'livraison' in enveloppe.valorisation:
                livraison = enveloppe.valorisation['livraison']
                cumul['livraison']['taxe_livraison_a_recuperer'] += livraison.get('taxe_livraison_a_recuperer', 0)
                cumul['livraison']['taxe_livraison_fixe'] += livraison.get('taxe_livraison_fixe', 0)
                cumul['livraison']['taxe_livraison_totale'] += livraison.get('taxe_livraison_totale', 0)
                cumul['livraison']['cout_total'] += livraison.get('cout_total', 0)
            
            # Cumul des valeurs de collecte
            if 'collecte' in enveloppe.valorisation:
                collecte = enveloppe.valorisation['collecte']
                cumul['collecte']['cout_ht'] += collecte.get('cout_ht', 0)
                cumul['collecte']['cout_tva'] += collecte.get('cout_tva', 0)
                cumul['collecte']['cout_ttc'] += collecte.get('cout_ttc', 0)
            
            # Cumul des valeurs d'expédition
            if 'expédition' in enveloppe.valorisation:
                expedition = enveloppe.valorisation['expédition']
                cumul['expédition']['cout_ht'] += expedition.get('cout_ht', 0)
                cumul['expédition']['cout_tva'] += expedition.get('cout_tva', 0)
                cumul['expédition']['cout_ttc'] += expedition.get('cout_ttc', 0)
        
        return cumul

    def reassigner_enveloppe_apres_changement_expediteur(self, enveloppe: Enveloppe, ancien_expediteur_id: int):
        """
        Réassigne une enveloppe à un nouveau lot expéditeur après changement d'expéditeur.
        """
        # Si l'enveloppe n'est pas dans un lot, rien à faire
        # if not enveloppe.lot_expediteur_id:
        #     return
        
        # Retirer l'enveloppe de son lot actuel
        ancien_lot = enveloppe.lot_expediteur
        enveloppe.lot_expediteur_id = None
        enveloppe.casier_id = None
        self.session.commit()
        
        # Vérifier si l'ancien lot est vide et le traiter si nécessaire
        if ancien_lot:
            nb_enveloppes = self.session.query(func.count(Enveloppe.id)).filter(
                Enveloppe.lot_expediteur_id == ancien_lot.id
            ).scalar()
            
            if nb_enveloppes == 0 and ancien_lot.statut == StatutLotExpediteurEnum.OUVERT:
                # Si le lot est vide et ouvert, on peut le supprimer
                self.session.delete(ancien_lot)
                self.session.commit()
        
        # Réassigner l'enveloppe à un nouveau lot si nécessaire
        if enveloppe.expediteur_id:
            self.lot_expediteur(enveloppe)

    
    # Statut N à N+1
    MATRICE_TRANSITIONS = {
        StatutLotExpediteurEnum.OUVERT: {
            StatutLotExpediteurEnum.FERME: "LIBOURNE"
        },
        StatutLotExpediteurEnum.FERME: {
            StatutLotExpediteurEnum.NOTIFIE: "LIBOURNE",
            StatutLotExpediteurEnum.CONTENTIEUX: "LIBOURNE"
        },
        StatutLotExpediteurEnum.NOTIFIE: {
            StatutLotExpediteurEnum.PAIEMENT_PARTIEL: "LIBOURNE",
            StatutLotExpediteurEnum.PAIEMENT_TOTAL_RECU: "LIBOURNE",
            StatutLotExpediteurEnum.CONTENTIEUX: "LIBOURNE"
        },
        StatutLotExpediteurEnum.PAIEMENT_PARTIEL: {
            StatutLotExpediteurEnum.PAIEMENT_TOTAL_RECU: "LIBOURNE",
            StatutLotExpediteurEnum.CONTENTIEUX: "LIBOURNE"
        },
        StatutLotExpediteurEnum.PAIEMENT_TOTAL_RECU: {
            StatutLotExpediteurEnum.TRAITE_LIBERABLE: "LIBOURNE",
            StatutLotExpediteurEnum.TRAITE_LIBOURNE: "LIBOURNE",
            StatutLotExpediteurEnum.TRAITE_PPDC: "LIBOURNE"
        },
        StatutLotExpediteurEnum.TRAITE_LIBERABLE: {
            StatutLotExpediteurEnum.TRAITE_OPERATIONNEL: "ROISSY",
            StatutLotExpediteurEnum.CONTENTIEUX: "LIBOURNE"
        },
        StatutLotExpediteurEnum.TRAITE_LIBOURNE: {
            StatutLotExpediteurEnum.ARRIVEE_LIBOURNE: "LIBOURNE",
            StatutLotExpediteurEnum.CONTENTIEUX: "LIBOURNE"
        },
        StatutLotExpediteurEnum.TRAITE_PPDC: {
            StatutLotExpediteurEnum.REMB_PARTIEL: "LIBOURNE",
            StatutLotExpediteurEnum.TRAITE_OPERATIONNEL: "LIBOURNE",
            StatutLotExpediteurEnum.CONTENTIEUX: "LIBOURNE",
        },
        StatutLotExpediteurEnum.REMB_PARTIEL: {
            StatutLotExpediteurEnum.TRAITE_OPERATIONNEL: "ROISSY",
            StatutLotExpediteurEnum.CONTENTIEUX: "LIBOURNE",
        },
        StatutLotExpediteurEnum.TRAITE_OPERATIONNEL: {
            StatutLotExpediteurEnum.CONTENTIEUX: "LIBOURNE"
        },
        StatutLotExpediteurEnum.ARRIVEE_LIBOURNE: {
            StatutLotExpediteurEnum.VALORISER_LIBOURNE: "LIBOURNE",
            StatutLotExpediteurEnum.CONTENTIEUX: "LIBOURNE"
        },
        StatutLotExpediteurEnum.VALORISER_LIBOURNE: {
            StatutLotExpediteurEnum.CONTENTIEUX: "LIBOURNE"
        },
        StatutLotExpediteurEnum.CONTENTIEUX: {
            StatutLotExpediteurEnum.FERME: "LIBOURNE",
            StatutLotExpediteurEnum.NOTIFIE: "LIBOURNE",
            StatutLotExpediteurEnum.PAIEMENT_PARTIEL: "LIBOURNE",
            StatutLotExpediteurEnum.PAIEMENT_TOTAL_RECU: "LIBOURNE",
            StatutLotExpediteurEnum.TRAITE_LIBERABLE: "LIBOURNE",
            StatutLotExpediteurEnum.TRAITE_LIBOURNE: "LIBOURNE",
            StatutLotExpediteurEnum.TRAITE_PPDC: "LIBOURNE",
            StatutLotExpediteurEnum.REMB_PARTIEL: "LIBOURNE",
            StatutLotExpediteurEnum.TRAITE_OPERATIONNEL: "LIBOURNE",
            StatutLotExpediteurEnum.ARRIVEE_LIBOURNE: "LIBOURNE",
            StatutLotExpediteurEnum.VALORISER_LIBOURNE: "LIBOURNE"
        }
    }

    def changer_statut_lot_avec_verification(self, lot: LotExpediteur, nouveau_statut: StatutLotExpediteurEnum, user: User) -> Optional[LotExpediteur]:
        """
        Change le statut d'un lot en vérifiant que la transition est autorisée selon la matrice de transitions.
        
        Args:
            lot: Le lot expéditeur à modifier
            nouveau_statut: Le nouveau statut à appliquer
            user: L'utilisateur qui effectue l'action
        
        Returns:
            Le lot modifié ou None si la transition n'est pas autorisée
        """
        if not lot:
            return None
        
        # Vérifier si la transition est autorisée
        statut_actuel = lot.statut
        
        # Si le statut ne change pas, on retourne simplement le lot
        if statut_actuel == nouveau_statut:
            return lot
        
        # Vérifier si la transition est dans la matrice
        if statut_actuel not in self.MATRICE_TRANSITIONS or nouveau_statut not in self.MATRICE_TRANSITIONS.get(statut_actuel, {}):
            return None
        
        # Vérifier si l'utilisateur a le droit d'effectuer cette transition
        sites_autorises = [self.MATRICE_TRANSITIONS[statut_actuel][nouveau_statut]] if isinstance(self.MATRICE_TRANSITIONS[statut_actuel][nouveau_statut], str) else self.MATRICE_TRANSITIONS[statut_actuel][nouveau_statut]
        site_user = user.site.nom if user and user.site else None

        if site_user not in sites_autorises:
            return None
        
        # Appliquer le changement de statut
        lot.statut = nouveau_statut
        lot.updated_at = datetime.utcnow()
        lot.user_modification_id = user.id

        # Si le statut == traité, on peut relâcher le Casier #TODO
        if lot.statut in [StatutLotExpediteurEnum.TRAITE_OPERATIONNEL]:
           self.liberer_casiers(lot)
        
        self.session.commit()

        return lot

    def creer_paiements(self, lot: LotExpediteur):
        """
        Créer les paiements pour l'encaissement d'un Lot Expediteur.
        """
        # Le Lot a déjà des paiements associées ?
        if len(lot.paiements):
            raise HTTPException(status_code=400, detail="Le lot a déjà des paiements associés")

        # Calcul du montant total du lot 
        valorisation = ServiceLotExpediteur.calcul_somme_valorisation(lot)

        if lot.option_recouvrement is None:
            raise HTTPException(status_code=400, detail="Aucun choix effectué pour le Lot")
        
        mapping_choix_cle = {
            OptionTraitementLotExpediteurEnum.LIVRAISON_DESTINATAIRES: "livraison.cout_total",
            OptionTraitementLotExpediteurEnum.RECUPERATION_SUR_SITE: "collecte.cout_ttc",
            OptionTraitementLotExpediteurEnum.RENVOI_EXPEDITEUR: "expédition.cout_ttc",
        }

        # Fonction utilitaire pour accéder à une clé imbriquée dans un dict à partir d'une string "a.b.c"
        def get_deep_value(d: dict, path: str):
            for key in path.split('.'):
                d = d[key]
            return d

        # Récupere le montant a facturer
        lot.montant_ttc = get_deep_value(valorisation, mapping_choix_cle[lot.option_recouvrement])

        # Pour le moment, on créer qu'une seule fraction
        fractions = [
            FractionnementPaiement(numero_paiement=1, pourcentage=100)
        ]

        for fraction in fractions:
            montant_paiement = lot.montant_ttc * (fraction.pourcentage / 100)

            paiement = PaiementLotExpediteur(
                # id_paiement=f"PAIEMENT-LOT-{lot.id}-{fraction.numero_paiement}",
                id_paiement=str(uuid.uuid4()),
                lot_expediteur_id=lot.id,
                numero_paiement=fraction.numero_paiement,
                pourcentage=fraction.pourcentage,
                montant_ttc=montant_paiement
            )
            self.session.add(paiement)
        
        self.session.commit()
        self.session.refresh(lot)

        return lot