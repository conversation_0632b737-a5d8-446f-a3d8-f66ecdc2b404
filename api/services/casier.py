from datetime import datetime
from typing import List, Optional
from sqlalchemy import func
from sqlalchemy.orm import Session
from models.business import <PERSON><PERSON><PERSON>, LotExpediteur, Site, Enveloppe
from constants.enumerations import StatutLotExpediteurEnum
from fastapi import HTTPException


class ServiceCasier:
    """Service pour gérer les casiers de stockage des lots d'enveloppes."""

    @staticmethod
    def obtenir_casier_disponible(site: Site) -> Optional[Casier]:
        """Attribue un casier disponible à un lot d'expéditeur."""

        from core.db import get_session
        with get_session() as session:
            # Rechercher un casier disponible (pas en maintenance et pas associé à un lot)
            casier = session.query(Casier).filter(
                ~Casier.enveloppes.any(),
                Casier.site_id == site.id,
                ~Casier.lot_expediteur.has()
            ).order_by(Casier.id).first()

            if not casier:
                # Aucun casier disponible
                return None

            # Marquer le casier comme occupé et l'associer au lot
            casier.date_attribution = datetime.utcnow()

            return casier

    @staticmethod
    def deplacer_plis_vers_autre_site(
        session: Session,
        casier_source_id: int,
        casier_destination_id: int
    ) -> dict:
        """
        Déplace l'ensemble des plis d'un casier A vers un casier B présents sur un autre site.

        Args:
            session: Session de base de données
            casier_source_id: ID du casier source
            casier_destination_id: ID du casier destination

        Returns:
            dict: Résumé de l'opération avec le nombre de plis déplacés

        Raises:
            HTTPException: Si les validations échouent
        """
        # Récupérer les casiers avec leurs relations
        casier_source = session.query(Casier).filter(Casier.id == casier_source_id).first()
        casier_destination = session.query(Casier).filter(
                ~Casier.enveloppes.any(),
                Casier.id == casier_destination_id,
                ~Casier.lot_expediteur.has()
            ).first()

        if not casier_source:
            raise HTTPException(status_code=404, detail="Casier source non trouvé")

        if not casier_destination:
            raise HTTPException(status_code=404, detail="Casier destination non trouvé ou non disponible")

        # Vérifier que les casiers sont sur des sites différents
        if casier_source.site_id == casier_destination.site_id:
            raise HTTPException(
                status_code=400,
                detail="Les casiers doivent être sur des sites différents"
            )

        # Récupérer toutes les enveloppes du casier source
        enveloppes = session.query(Enveloppe).filter(
            Enveloppe.casier_id == casier_source_id
        ).all()

        if not enveloppes:
            raise HTTPException(
                status_code=400,
                detail="Aucun pli trouvé dans le casier source"
            )

        # Vérifier la capacité du casier destination
        nb_enveloppes_destination = session.query(func.count(Enveloppe.id)).filter(
            Enveloppe.casier_id == casier_destination_id
        ).scalar()


        # Effectuer le déplacement
        nb_plis_deplaces = 0
        lots_expediteur_impactes = set()

        for enveloppe in enveloppes:
            # Changer le casier de l'enveloppe
            enveloppe.casier_id = casier_destination_id

            # Mettre à jour le site de l'enveloppe
            enveloppe.site_id = casier_destination.site_id

            # Collecter les lots expéditeurs impactés
            if enveloppe.lot_expediteur_id:
                lots_expediteur_impactes.add(enveloppe.lot_expediteur_id)

            nb_plis_deplaces += 1

        # Mettre à jour les casiers
        # Si le casier source n'a plus d'enveloppes, le libérer
        if nb_enveloppes_destination == 0:
            casier_source.lot_expediteur_id = None
            casier_source.date_liberation = datetime.utcnow()

        # Gérer les lots expéditeurs impactés
        for lot_id in lots_expediteur_impactes:
            lot = session.query(LotExpediteur).filter(LotExpediteur.id == lot_id).first()
            if lot:
                # Mettre à jour le site du lot si nécessaire
                lot.site_id = casier_destination.site_id

                # Si le lot n'a plus d'enveloppes dans le casier source,
                # associer le casier destination comme casier principal
                enveloppes_restantes_source = session.query(func.count(Enveloppe.id)).filter(
                    Enveloppe.lot_expediteur_id == lot_id,
                    Enveloppe.casier_id == casier_source_id
                ).scalar()

                if enveloppes_restantes_source == 0 and lot.casier_id == casier_source_id:
                    lot.casier_id = casier_destination_id
                    casier_destination.lot_expediteur_id = lot_id

        session.commit()

        return {
            "nb_plis_deplaces": nb_plis_deplaces,
            "casier_source": {
                "id": casier_source.id,
                "numero": casier_source.numero,
                "site": casier_source.site.nom
            },
            "casier_destination": {
                "id": casier_destination.id,
                "numero": casier_destination.numero,
                "site": casier_destination.site.nom
            },
            "lots_expediteur_impactes": len(lots_expediteur_impactes)
        }
    