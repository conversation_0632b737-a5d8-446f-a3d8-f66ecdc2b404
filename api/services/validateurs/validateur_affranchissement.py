from constants.enumerations import TypeAffranchissementEnum
from services.validateurs.types.validateur_tnum import ValidateurTNUM
from services.validateurs.types.validateur_s10 import ValidateurS10
from models import VerificationAffranchissement, Affranchissement, StatutVerificationEnum
from sqlmodel import select
from constants.enumerations import SousTypeAffranchissementEnum
from services.validateurs.types.validateur_sdn import ValidateurSDN
from services.validateurs.types.validateur_sdx import ValidateurSDX


class ValidateurAffranchissement:
    
    AFFRANCHISSEMENT_TYPE_MAPPING = {
        TypeAffranchissementEnum.TNUM: ValidateurTNUM,
        TypeAffranchissementEnum.S10: ValidateurS10
    }
    
    AFFRANCHISSEMENT_SOUS_TYPE_MAPPING = {
        SousTypeAffranchissementEnum.SDN: ValidateurSDN,
        SousTypeAffranchissementEnum.SDX: ValidateurSDX
    }

    @classmethod
    def classe_validation(cls, affranchissement):
        return cls.AFFRANCHISSEMENT_TYPE_MAPPING.get(affranchissement.type) or cls.AFFRANCHISSEMENT_SOUS_TYPE_MAPPING.get(affranchissement.sous_type)
    
    @classmethod    
    def executer_validations(cls, affranchissement):
        if affranchissement.code is None or affranchissement.code == "":
            return

        classe = cls.classe_validation(affranchissement)

        if not classe:
            return
 
        # Exécute les validations avec la classe appropriée
        classe(affranchissement).executer_validations()
        
        failed_verifications = [v for v in affranchissement.verifications if v.statut == StatutVerificationEnum.INVALIDE]

        if len(failed_verifications) > 0:
            affranchissement.statut = StatutVerificationEnum.INVALIDE
        else:
            affranchissement.statut= StatutVerificationEnum.VALIDE
            
        # On a rien commit jusqu'à présent
            
            
            
    
    
