from models import TypeVerificationAffranchissementEnum, StatutVerificationEnum
from services.validateurs.types.validateur_type_affranchissement import ValidateurTypeAffranchissement
from services.affranchissement import ServiceAffranchissement
from constants.enumerations import TypeAffranchissementEnum


class ValidateurSD(ValidateurTypeAffranchissement):

    def liste_validations(self):
        validations = [self.verification_signature]

        if self.affranchissement.origine in ["86", "87"]:
            validations.append(self.verification_ssu_purchase)

        return super().liste_validations() + validations

    def verification_grammaire(self):
        """
        Vérifie la grammaire du numéro
        """
        RULES = {
            "origins": ["86", "87", "88"]
        }

        affranchissement_content = self.donnees()
        origin = affranchissement_content["origin"]

        # Test origin
        if origin not in RULES["origins"]:
            self.ajouter_verification(TypeVerificationAffranchissementEnum.GRAMMAIRE_ORIGIN,
                                StatutVerificationEnum.INVALIDE,
                                f"Origin {origin} non autorisé. Liste des origins autorisés: [{', '.join(RULES['origins'])}]")
        else:
            self.ajouter_verification(TypeVerificationAffranchissementEnum.GRAMMAIRE_ORIGIN,
                                StatutVerificationEnum.VALIDE, 
                                f"Origin {origin} autorisé. Liste des origins autorisés: [{', '.join(RULES['origins'])}]")
    
    

