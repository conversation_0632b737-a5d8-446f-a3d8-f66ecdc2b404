from models import TypeVerificationAffranchissementEnum, StatutVerificationEnum
from services.validateurs.types.validateur_sd import ValidateurSD
from services.affranchissement import ServiceAffranchissement
from services.secret import SecretService, Mod37_36


class ValidateurSDN(ValidateurSD):
    
        
    def verification_signature(self):
        """
        Verification de la signature
        """

        value = self.donnees()["origin"] + self.donnees()["number"]
        cle_calculee = Mod37_36.calc_check_digit(value)
        
        # Vérification de la clé
        if cle_calculee == self.donnees()["key"]:
            return self.ajouter_verification(
                type=TypeVerificationAffranchissementEnum.GRAMMAIRE_CLE,
                statut=StatutVerificationEnum.VALIDE,
                message=f"Clé calculée: {cle_calculee} == Clé SD: {self.donnees()['key']}",
            )
            
        return self.ajouter_verification(
                type=TypeVerificationAffranchissementEnum.GRAMMAIRE_CLE,
                statut=StatutVerificationEnum.INVALIDE,
                message=f"Clé calculée: {cle_calculee} != Clé SD: {self.donnees()['key']}",
                donnees={"cle_calculee": cle_calculee, "cle_code": self.donnees()["key"]}
            )
        
            

        
  