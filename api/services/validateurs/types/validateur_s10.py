from models import TypeVerificationAffranchissementEnum, StatutVerificationEnum
from services.validateurs.types.validateur_type_affranchissement import ValidateurTypeAffranchissement
from sqlmodel import select
from constants.enumerations import TypeAffranchissementEnum, TypeRegleMetierEnum
from models.business import RegleMetier
from services.affranchissement import ServiceAffranchissement


class ValidateurS10(ValidateurTypeAffranchissement):

    def liste_validations(self):
        return [self.verification_sequences] + super().liste_validations()
    
          
    def verification_sequences(self):
        """
        Vérifie si le numéro de séquence est valide en utilisant les règles métier stockées en base de données.
        Les séquences valides sont définies dans la table RegleMetier avec le type SEQUENCE_S10.
        
        Une séquence est valide si:
        - Elle existe et est active
        - ET (Elle a moins de X ans OU c'est la séquence la plus récente)
        où X est défini par la règle métier S10_SEQUENCE_EXPIRATION_ANNEES
        """
        from datetime import datetime, timedelta
        from sqlmodel import func, JSON, cast
        from core.db import get_session
        from models.business import RegleMetier
        
        sequence = self.affranchissement.service.sequence_associee()
        
        if sequence:
            # Vérifier si la séquence est récente ou la plus récente
            est_valide = False
            message = ""
            
            # Vérifier d'abord si la séquence est active
            if not sequence.active:
                self.ajouter_verification(
                    TypeVerificationAffranchissementEnum.SEQUENCE,
                    StatutVerificationEnum.INVALIDE,
                    f"Séquence {sequence.cle} trouvée mais inactive"
                )
                return
            
            # Vérifier l'âge de la séquence
            if "date_attribution" in sequence.valeur:
                try:
                    with get_session() as session:
                        # Récupérer la durée de validité des séquences depuis la règle métier
                        duree_validite_annees = RegleMetier.valeur_pour(
                            session, 
                            type_regle=TypeRegleMetierEnum.VALEUR,
                            cle="S10_SEQUENCE_EXPIRATION_ANNEES", 
                            valeur_defaut=2
                        )
                        
                        date_attribution = datetime.strptime(sequence.valeur["date_attribution"], "%d/%m/%Y")
                        date_limite = datetime.now() - timedelta(days=int(duree_validite_annees)*365)
                        
                        if date_attribution > date_limite:
                            est_valide = True
                            message = f"Numéro valide dans une SEQUENCE existante et active : {sequence.cle} (moins de {duree_validite_annees} ans)"
                        else:
                            # Vérifier si c'est la séquence la plus récente
                            service = self.donnees()["service"]
                            statement = select(RegleMetier).where(
                                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                                RegleMetier.active == True,
                                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service)
                            ).order_by(func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'date_attribution').desc())
                            
                            sequence_recente = session.exec(statement).first()
                            
                            if sequence_recente and sequence_recente.id == sequence.id:
                                est_valide = True
                                message = f"Numéro valide dans la SEQUENCE la plus récente : {sequence.cle}"
                            else:
                                message = f"Séquence {sequence.cle} trop ancienne et n'est pas la plus récente"
                except (ValueError, KeyError) as e:
                    est_valide = False  # Si on ne peut pas déterminer la date, on considère valide
                    message = f"Numéro trouvé dans une SEQUENCE existante et active : {sequence.cle} mais date indéterminée"
            else:
                est_valide = False  # Si on ne peut pas déterminer la date, on considère valide
                message = f"Numéro trouvé dans une SEQUENCE existante et active : {sequence.cle} mais date indéterminée"
            
            self.ajouter_verification(
                TypeVerificationAffranchissementEnum.SEQUENCE,
                StatutVerificationEnum.VALIDE if est_valide else StatutVerificationEnum.INVALIDE,
                message
            )
        else:
            self.ajouter_verification(
                TypeVerificationAffranchissementEnum.SEQUENCE,
                StatutVerificationEnum.INVALIDE,
                f"Aucune séquence trouvée (ou active) pour {self.affranchissement.code}"
            )

    def verification_grammaire(self):
        """
        Vérifie la grammaire du numéro
        """
        RULES = {
            "services_forbidden": ["LV", "LY", "LB"],
            "countries": ["FR"],
        }

        affranchissement_content = self.donnees()
        service = affranchissement_content["service"]
        
        # # Test service
        # if service in RULES["services_forbidden"]:
        #     self.ajouter_verification(TypeVerificationAffranchissementEnum.GRAMMAIRE_SERVICE,
        #                           StatutVerificationEnum.INVALIDE,
        #                            f"Service {service} non autorisé. Liste des services non-autorisés: [{', '.join(RULES['services_forbidden'])}]")
        # else:
        #     self.ajouter_verification(TypeVerificationAffranchissementEnum.GRAMMAIRE_SERVICE,
        #                           StatutVerificationEnum.VALIDE,
        #                           f"Service {service} autorisé. Liste des services non-autorisés: [{', '.join(RULES['services_forbidden'])}]")

        # Test clé
        key_computed = self.calcul_cle()

        if key_computed != affranchissement_content["key"]:
            self.ajouter_verification(TypeVerificationAffranchissementEnum.GRAMMAIRE_CLE,
                                  StatutVerificationEnum.INVALIDE,
                                  f"Clé {affranchissement_content['key']} différente de {key_computed}")
        else:
            self.ajouter_verification(TypeVerificationAffranchissementEnum.GRAMMAIRE_CLE,
                                  StatutVerificationEnum.VALIDE,
                                  f"Clé {affranchissement_content['key']} égal à {key_computed}")

        # Pays
        if affranchissement_content["country"] not in RULES["countries"]:
            self.ajouter_verification(TypeVerificationAffranchissementEnum.GRAMMAIRE_COUNTRY,
                                  StatutVerificationEnum.INVALIDE,
                                  f"Pays {affranchissement_content['country']} différent de {', '.join(RULES['countries'])}")
        else:
            self.ajouter_verification(TypeVerificationAffranchissementEnum.GRAMMAIRE_COUNTRY,
                                  StatutVerificationEnum.VALIDE,
                                  f"Pays {affranchissement_content['country']} égal à {', '.join(RULES['countries'])}")

        
    
    def calcul_cle(self):
        """
        Calcul la clé du numéro
        """
        id = self.donnees()["number"]

        poids = [8, 6, 4, 2, 3, 5, 9, 7]
        somme = sum(int(id[i]) * poids[i] for i in range(8))
        mod = somme % 11
        key_computed = 11 - mod
        if key_computed == 10:
            key_computed = 0
        elif key_computed == 11:
            key_computed = 5

        return str(key_computed)

