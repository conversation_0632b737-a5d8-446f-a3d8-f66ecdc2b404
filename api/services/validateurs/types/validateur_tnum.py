from services.validateurs.types.validateur_type_affranchissement import ValidateurTypeAffranchissement
from models import TypeVerificationAffranchissementEnum, StatutVerificationEnum

class ValidateurTNUM(ValidateurTypeAffranchissement):
    def verification_grammaire(self):
        self.ajouter_verification(TypeVerificationAffranchissementEnum.GRAMMAIRE, 
                              StatutVerificationEnum.NON_DETERMINE, 
                              "Non implémenté")