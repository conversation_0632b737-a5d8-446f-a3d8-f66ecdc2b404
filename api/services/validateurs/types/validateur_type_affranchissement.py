from models import CodeAffranchissementFrauduleux, Affranchissement, VerificationAffranchissement, TypeVerificationAffranchissementEnum, StatutVerificationEnum
from sqlmodel import select
import requests
import re
from services.affranchissement import ServiceAffranchissement
import os
from services.ssu_api import SSUApi


class ValidateurTypeAffranchissement:
    """
    Base class for all affranchissement processors
    """

    def __init__(self, affranchissement):
        self.affranchissement = affranchissement
        self.service = affranchissement.service
    
    def liste_validations(self):
        return [self.verification_grammaire, 
                self.verification_presence_numero,
                self.verification_ssu_tracking]

    def executer_validations(self):
        """
        Analyse the timbre & execute all verifications.
        1. Socle
        2. Achat
        3. 
        """
        for v in self.liste_validations():
            
            # Si une vérification est invalide, on arrête les autres vérifications
            if any(v.statut == StatutVerificationEnum.INVALIDE for v in self.affranchissement.verifications):
                self.affranchissement.statut = StatutVerificationEnum.INVALIDE
                return

            v()

    def donnees(self, root=False):
        """
        Format the content of the affranchissement
        """
        if root:
            return self.service.__class__.donnees(self.affranchissement.code)
        else:
            print(self.affranchissement.code)
            print(self.service.__class__.donnees(self.affranchissement.code))
            return self.service.__class__.donnees(self.affranchissement.code)["content"]

    def execute_regex(self):
        """
        Execute regex
        """
        regex = ServiceAffranchissement.identifier_type_affranchissement(code=self.affranchissement.code)
        
        return re.match(regex["regex"], self.affranchissement.code)


    def ajouter_verification(self, type, statut, message, donnees=None):
        """
        Add a verification to the affranchissement
        """
        verification = VerificationAffranchissement(
            type=type,
            statut=statut,
            message=message,
            affranchissement=self.affranchissement,
            donnees=donnees or {}
        )

    def verification_grammaire(self):
        """
        Vérifie la grammaire du numéro
        """
        pass

    def verification_presence_numero(self):
        """
        Compte le nombre d'occurrences d'un même code (ID)
        :return:
        """
        code = self.affranchissement.code

        if code is None:
            return

        from core.db import get_session
        with get_session() as db:

            # If Enveloppe has already a Affranchissement with this same numero
            filtered_timbres = db.execute(select(Affranchissement).where(
                Affranchissement.code == code,
                Affranchissement.id != self.affranchissement.id
            )).all()

            # TODO: attention si timbre sauvegarder en base avant de faire cette vérification
            if len(filtered_timbres) >= 1:
                self.ajouter_verification(TypeVerificationAffranchissementEnum.COUNT,
                                      StatutVerificationEnum.INVALIDE,
                                      "Le numéro existe déjà sur l'enveloppe")
            else:
                self.ajouter_verification(TypeVerificationAffranchissementEnum.COUNT,
                                      StatutVerificationEnum.VALIDE,
                                      "Le numéro n'existe pas sur l'enveloppe")

            # Search in CodeAffranchissementFrauduleux table (numero and type), avoid db.query
            code_frauduleux = db.execute(select(CodeAffranchissementFrauduleux).where(CodeAffranchissementFrauduleux.code == self.affranchissement.code)).first()
            
            if code_frauduleux:
                self.ajouter_verification(TypeVerificationAffranchissementEnum.CODE_FRAUDULEUX,
                                      StatutVerificationEnum.INVALIDE,
                                      "Le numéro existe dans la base des codes frauduleux")
            else:
                self.ajouter_verification(TypeVerificationAffranchissementEnum.CODE_FRAUDULEUX,
                                      StatutVerificationEnum.VALIDE,
                                      "Le numéro n'existe pas dans la base des codes frauduleux")


    def ssu_headers(self):
        return {
            "Accept": "application/json",
            "X-Okapi-Key": os.environ["OKAPI_KEY"],
        }
    
    def sid(self):
        return self.donnees(root=True).get("id", None)

    def verification_ssu_tracking(self):
        """
        SID example : 87001001392849E
        SID == self.affranchissement.numero ?
        """
        tracking = {"dates": []}

        #TODO: LIVBAL est un évènement unique de livraison. A l'inter c'est LIVCFM qui indique la livraison. LA règle métier serait : si un évènement de livraison existe, alors tout flashage postérieur est doublon/fraude
        
        response, error = SSUApi.get_tracking(self.sid())
        
        if error:
            self.ajouter_verification(TypeVerificationAffranchissementEnum.SSU_TRACKING,
                                  StatutVerificationEnum.NON_DETERMINE,
                                  error,
                                  tracking)
            return
        
        shipment = response.get("shipment", {})
        is_final = shipment.get("isFinal", None)

        if is_final is None:
            return self.ajouter_verification(TypeVerificationAffranchissementEnum.SSU_TRACKING,
                                      StatutVerificationEnum.NON_DETERMINE,
                                      "is_final non trouvé")
        if is_final is True:
            return self.ajouter_verification(TypeVerificationAffranchissementEnum.SSU_TRACKING,
                                      StatutVerificationEnum.INVALIDE,
                                      f"Livraison finalisée: is_final={is_final}")

        return self.ajouter_verification(TypeVerificationAffranchissementEnum.SSU_TRACKING,
                                      StatutVerificationEnum.VALIDE,
                                      f"Livraison non finalisée: is_final={is_final}")



        return

        

        events = shipment.get("event", [])
        
        if events:
            tracking["dates"] = sorted(set(event["date"][:10] for event in events if len(event["date"]) >= 10))

            if len(tracking["dates"]) == 0:
                self.ajouter_verification(TypeVerificationAffranchissementEnum.SSU_TRACKING,
                                      StatutVerificationEnum.VALIDE,
                                      "Evénements sur 1 jour maxi : OK (aucun évt)",
                                      tracking)
            elif len(tracking["dates"]) == 1:
                self.ajouter_verification(TypeVerificationAffranchissementEnum.SSU_TRACKING,
                                      StatutVerificationEnum.VALIDE,
                                      f"Evénements sur 1 jour maxi : OK ({tracking['dates'][0]})",
                                      tracking)
            else:
                self.ajouter_verification(TypeVerificationAffranchissementEnum.SSU_TRACKING,
                                      StatutVerificationEnum.INVALIDE,
                                      f"Evénements sur 1 jour maxi : KO ({tracking['dates'][0]} ... {tracking['dates'][-1]})",
                                      tracking)
        else:
            self.ajouter_verification(TypeVerificationAffranchissementEnum.SSU_TRACKING,
                                      StatutVerificationEnum.NON_DETERMINE,
                                      "Evénements sur 1 jour maxi : IND (JSON sans event)",
                                      tracking)

    def verification_ssu_purchase(self):
        """
        Only for SD
        """

        return #TODO: déactiver pour le moment
        purchase = {
            "idCommand": None,
            "date": None,
            "price": None,
            "weight": None,
            "rawPrice": None,
            "offerCode": None,
        }

        response, error = SSUApi.get_purchase(self.sid())
        
        if error:
            self.ajouter_verification(TypeVerificationAffranchissementEnum.SSU_PURCHASE,
                                  StatutVerificationEnum.NON_DETERMINE,
                                  error,
                                  purchase)
            return

        purchase_shipment = response.get("shipment", {}).get("purchase", {})

        if purchase_shipment and purchase_shipment.get("idCommand"):
            purchase.update({
                "idCommand": purchase_shipment["idCommand"],
                "date": purchase_shipment.get("date"),
                "price": purchase_shipment.get("price"),
                "weight": purchase_shipment.get("weight"),
                "rawPrice": purchase_shipment.get("rawPrice"),
                "offerCode": purchase_shipment.get("offerCode"),
            })

            self.ajouter_verification(TypeVerificationAffranchissementEnum.SSU_PURCHASE,
                                  StatutVerificationEnum.VALIDE,
                                  f"Achat trouvé : OK (date={purchase['date'][:10]} - Commande={purchase['idCommand']})",
                                  purchase)
        else:
            self.ajouter_verification(TypeVerificationAffranchissementEnum.SSU_PURCHASE,
                                  StatutVerificationEnum.NON_DETERMINE,
                                  "Achat trouvé : IND (JSON sans idCommand)",
                                  purchase)

    