from models import TypeVerificationAffranchissementEnum, StatutVerificationEnum
from services.validateurs.types.validateur_sd import ValidateurSD
from services.affranchissement import ServiceAffranchissement
import os
from services.secret import SecretService
from hashlib import sha1
import hmac
import re
        

class ValidateurSDX(ValidateurSD):
    
    def liste_validations(self):
        return super().liste_validations() + [self.verification_signature]
    
    def verification_signature(self):
        """
        Verification de la signature en utilisant une clé secrete
        """
        sd_key = self.clé_calcul_code_sd()        
        data = self.donnees()
        
        if not sd_key:
            return self.ajouter_verification(
                type=TypeVerificationAffranchissementEnum.GRAMMAIRE_SIGNATURE,
                statut=StatutVerificationEnum.NON_DETERMINE,
                message="signature SD : IND (clé secrète absente)"
            )
        
        # Calcul de la signature théorique
        string_to_sign = f"%{data['cp']}{data['origin']}{data['number']}{data['socode']}{data['country']}{data['ascode']}^"
        
        # Calcul HMAC-SHA1
        sd_key_bytes = bytes.fromhex(sd_key)
        h = hmac.new(sd_key_bytes, string_to_sign.encode(), sha1)
        digest = h.hexdigest()
        sign_lower = digest[:7].lower()

        # Vérification si customer contient 7 caractères hexa
        if data["customer"] and re.match(r"^[A-Fa-f0-9]{7,7}$", data["customer"]):
            
            # Si la signature est valide
            if sign_lower == data["customer"].lower():                 
                return self.ajouter_verification(
                    type=TypeVerificationAffranchissementEnum.GRAMMAIRE_SIGNATURE,
                    statut=StatutVerificationEnum.VALIDE,
                    message=f"Signature OK"
                )
            
            # Si la signature n'est pas valide
            return self.ajouter_verification(
                    type=TypeVerificationAffranchissementEnum.GRAMMAIRE_SIGNATURE,
                    statut=StatutVerificationEnum.INVALIDE,
                    message=f"Signature NON valide"
                )
            
        return self.ajouter_verification(
            type=TypeVerificationAffranchissementEnum.GRAMMAIRE_SIGNATURE,
            statut=StatutVerificationEnum.NON_DETERMINE,
            message="signature SD : IND (customer ne contient pas une signature)"
        )
     
    def clé_calcul_code_sd(self):
        """
        Récupère la clé SD depuis Redis et la déchiffre
        """
        # Sinon déchiffrement
        sd_key_value_crypted = os.getenv('SDKEY_VALUE_CRYPTED', '')
        password_dechiffrement = os.getenv('REDIS_KEY', '')

        try:
            return SecretService.dechiffer(sd_key_value_crypted, password_dechiffrement)
        except:
            return None