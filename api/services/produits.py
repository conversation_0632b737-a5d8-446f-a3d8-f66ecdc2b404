import csv
from constants.enumerations import DestinationEnveloppeEnum, ProduitEnum


class ServiceProduits:
    
    class PrixProduit:
        def __init__(self, produit, destination, poids_max, courrier_tp, courrier_mtel, colissimo_zone_fm, colissimo_zone_om1, colissimo_zone_om2, colissimo_zone_a, colissimo_zone_b, colissimo_zone_c):
            self.produit = produit
            self.destination = destination
            self.poids_max = poids_max
            self.courrier_tp = courrier_tp
            self.courrier_mtel = courrier_mtel
            self.colissimo_zone_fm = colissimo_zone_fm
            self.colissimo_zone_om1 = colissimo_zone_om1
            self.colissimo_zone_om2 = colissimo_zone_om2
            self.colissimo_zone_a = colissimo_zone_a
            self.colissimo_zone_b = colissimo_zone_b
            self.colissimo_zone_c = colissimo_zone_c
            
    
    PRIX_PRODUITS = []
    
    @classmethod
    def tous_prix(cls):
        """Initialise les tarifs des produits à partir d'un fichier TSV"""
        
        
        if len(cls.PRIX_PRODUITS) != 0:
            return cls.PRIX_PRODUITS
        
        with open("/app/constants/produits_prix.tsv", "r") as file:
            reader = csv.reader(file, delimiter='\t')
            for row in reader:
                if len(row) < 11:
                    continue

                try:
                    destination = DestinationEnveloppeEnum(row[2].strip())
                except ValueError:
                    destination = None
                
                # Crée l'objet prix
                prix = cls.PrixProduit(
                    produit=ProduitEnum(row[1].strip()),
                    destination=destination,
                    poids_max=int(row[3].strip()),
                    courrier_tp=float(row[4].strip().replace(",", ".")),
                    courrier_mtel=float(row[5].strip().replace(",", ".")),
                    colissimo_zone_fm=float(row[6].strip().replace(",", ".")),
                    colissimo_zone_om1=float(row[7].strip().replace(",", ".")),
                    colissimo_zone_om2=float(row[8].strip().replace(",", ".")),
                    colissimo_zone_a=float(row[9].strip().replace(",", ".")),
                    colissimo_zone_b=float(row[10].strip().replace(",", ".")),
                    colissimo_zone_c=float(row[11].strip().replace(",", "."))
                )
                
                cls.PRIX_PRODUITS.append(prix)
                
        return cls.PRIX_PRODUITS


    @classmethod
    def recherche_produit_proche(cls, produit: ProduitEnum = None, destination: DestinationEnveloppeEnum = None, poids: float = None):
        # Filtre selon le produit et trie dans l'ordre inverse des poids
        # pour que la 1ère ligne soit le bon tarif à prendre

        prices = cls.tous_prix()

        if produit:
            prices = sorted(
                [p for p in prices if p.produit == produit and poids <= p.poids_max],
                key=lambda x: x.poids_max
            )
        
        if destination:
            prices = sorted(
                [p for p in prices if p.destination == destination and poids <= p.poids_max],
                key=lambda x: x.poids_max
            )
                
        if not prices:
            return None
            
        return prices[0]
    
    @classmethod
    def recherche_produit_exact(cls, produit: str, poids: float):
        # Recherche le tarif exact pour un produit et un poids donnés
        prices = [p for p in cls.tous_prix() if p.produit == produit and poids == p.poids_max]
        
        if not prices:
            return None
            
        return prices[0]
    
    @classmethod
    def liste_produits(cls):
        """
        Liste des produits = Nature des produits _ (LV_1000_sss => LV)
        """        
        from constants import NatureAffranchissementEnum
        
        produits = set()
        
        for nature in NatureAffranchissementEnum:
            if "_" in nature.value:
                produit = nature.value.split("_")[0]
                produits.add(produit)
        
        return produits