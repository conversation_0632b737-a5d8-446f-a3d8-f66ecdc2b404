from constants.affranchissements.nature import NATURES_AFFRANCHISSEMENT
from constants.enumerations import NatureAffranchissementEnum, TypeAffranchissementEnum, DeviseEnum, ValiditeAffranchissementEnum
from models.business import Affranchissement
from core.utils.prix import convertir_prix_devise_en_euros


class ServiceAffranchissementCommun:
    def __init__(self, affranchissement: Affranchissement):
        self.affranchissement = affranchissement

    def informations(self):
        return {
            "champs_manquants": self.champs_manquants(),
            "champs_requis": self.champs_requis(),
            "complet": len(self.champs_manquants()) == 0,
            "donnees": self.__class__.donnees(self.affranchissement.code),
            "label": f"{self.affranchissement.type}{self.affranchissement.origine if self.affranchissement.origine else ''}"
        }

    def prix_unite_euros(self):
        """
        Retourne le prix unitaire en euros
        """
        if self.affranchissement.prix_unite_devise is None or self.affranchissement.prix_unite_devise is None:
            return 0
        
        # if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
        #     return 0
        
        return convertir_prix_devise_en_euros(self.affranchissement.devise, self.affranchissement.prix_unite_devise)

    def valoriser(self):
        raise NotImplementedError("La méthode 'valoriser' doit être implémentée par la classe fille.")
    
    def champs_requis(self):
        """
        Retourne les champs requis pour l'affranchissement
        """
        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        # # REGLE_METIER: Si l'affranchissement est invalide, on ne demande pas la nature
        # if "nature" in champs_requis and self.affranchissement.statut != ValiditeAffranchissementEnum.VALIDE:
        #     champs_requis.remove("nature")

        return champs_requis

    def champs_manquants(self):
        """
        Retourne les champs manquants pour l'affranchissement
        """
        champs_requis = self.champs_requis()
        champs_manquants = [champ for champ in champs_requis if getattr(self.affranchissement, champ) is None]

        return champs_manquants

    def update(self, data: dict):
        """
        Met à jour l'affranchissement avec les données fournies en respectant les contraintes du modele
        """

        # L'affranchissement doit être valorisé par son service ?
        if self.affranchissement.modele_affranchissement.valorisation_complexe:
            if self.affranchissement.prix_unite_devise is None:
                self.valoriser()

        champs_requis = self.affranchissement.modele_affranchissement.champs_requis

        if data.get("quantite") is not None:
            self.affranchissement.quantite = data.get("quantite", 1) or 1
        
        if data.get("statut") is not None:
            self.affranchissement.statut = data.get("statut", self.affranchissement.statut)

        # Nature nécessaire ?
        if "nature" in champs_requis:

            # Invalide, la nature n'importe peu
            if self.affranchissement.statut == ValiditeAffranchissementEnum.INVALIDE:
                self.affranchissement.nature = NatureAffranchissementEnum.NON_DETERMINE
                self.affranchissement.prix_unite_devise = 0
                self.affranchissement.devise = DeviseEnum.EURO
        
            # Nature spécifiée ?
            if data.get("nature") is not None :
                nature = data.get("nature", self.affranchissement.nature)
                self.affranchissement.nature = nature
                self.affranchissement.prix_unite_devise = NATURES_AFFRANCHISSEMENT[nature].prix_unite or 0
                self.affranchissement.devise = DeviseEnum.EURO

        if data.get("prix_unite_devise") is not None and self.affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

        # Prix unitaire
        if data.get("prix_unite_devise") is not None and "prix_unite_devise" in champs_requis:
            self.affranchissement.prix_unite_devise = data.get("prix_unite_devise", self.affranchissement.prix_unite_devise)

    @classmethod
    def donnees(cls, code=None):
        """
        Retourne les données de l'affranchissement
        """
        return {}
