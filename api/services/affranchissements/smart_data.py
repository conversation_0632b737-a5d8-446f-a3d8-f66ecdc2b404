import re
from services.affranchissements.commun import ServiceAffranchissementCommun
from constants.enumerations import TypeAffranchissementEnum, SousTypeAffranchissementEnum


class ServiceAffranchissementSD(ServiceAffranchissementCommun):
    """
    Service d'affranchissement pour les Smart Data
    """
    TYPE = TypeAffranchissementEnum.SD
    
    @classmethod
    def donnees(cls, code=None):
        """
        Extrait les données du code SD en utilisant la REGEX définie dans la classe
        """
        # Pour SDN, on convertit en majuscules
        if hasattr(cls, 'UPPERCASE') and cls.UPPERCASE:
            code = code.upper()
            
        match = re.match(cls.REGEX, code)
        
        if not match:
            return None
        
        # Initialisation avec des valeurs par défaut
        content = {
            "cp": None,
            "origin": None,
            "number": None,
            "key": None,
            "socode": None,
            "country": None,
            "ascode": None,
            "customer": None
        }
        
        # Mapping des groupes de regex vers les champs du contenu
        mapping = cls.get_regex_mapping()
        
        # Remplissage du contenu selon le mapping
        for field, group_index in mapping.items():
            if group_index is not None and group_index <= len(match.groups()):
                content[field] = match.group(group_index)
                
        # Construction de l'ID et du format segmenté
        id = f"{content['origin']}{content['number']}"
        segmented = f"{content['origin']} {content['number']}"
        
        # Stockage des données extraites
        donnees = {
            "id": id,
            "segmented": segmented,
            "content": content
        }
        
        return donnees
    
    @classmethod
    def get_regex_mapping(cls):
        """
        Retourne le mapping entre les champs du contenu et les groupes de la regex
        À surcharger dans les classes filles si nécessaire
        """
        raise NotImplementedError("Cette méthode doit être implémentée par les classes filles")


class ServiceAffranchissementSDX(ServiceAffranchissementSD):
    """
    Service d'affranchissement pour les Smart Data format complet (SDX)
    """
    
    REGEX = r"^%([A-Za-z0-9]{7})([0-9]{2})([0-9]{12})([A-Za-z0-9]{3})([A-Za-z0-9]{3})([A-Za-z0-9]{3})\^(.{0,40})?$"
    UPPERCASE = False
    SOUS_TYPE = SousTypeAffranchissementEnum.SDX
    
    @classmethod
    def get_regex_mapping(cls):
        return {
            "cp": 1,
            "origin": 2,
            "number": 3,
            "key": None,
            "socode": 4,
            "country": 5,
            "ascode": 6,
            "customer": 7
        }


class ServiceAffranchissementSDN(ServiceAffranchissementSD):
    """
    Service d'affranchissement pour les Smart Data format court (SDN)
    """
    
    REGEX = r"^(SD|sd|Sd|sD)?([0-9]{2})([0-9]{12})([A-Za-z0-9]{1,1})$"
    UPPERCASE = True
    SOUS_TYPE = SousTypeAffranchissementEnum.SDN
    
    @classmethod
    def get_regex_mapping(cls):
        return {
            "cp": None,
            "origin": 2,
            "number": 3,
            "key": 4,
            "socode": None,
            "country": None,
            "ascode": None,
            "customer": None
        }
