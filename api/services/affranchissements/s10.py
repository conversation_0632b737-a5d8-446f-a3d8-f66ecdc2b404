import re
from services.affranchissements.commun import ServiceAffranchissementCommun
from constants.enumerations import TypeAffranchissementEnum, DeviseEnum, TypeRegleMetierEnum
from models.business import RegleMetier
from sqlalchemy import JSO<PERSON>, cast, func
from sqlmodel import select
from constants.variables import CONSTANTES_PRIX_AFFRANCHISSEMENT


class ServiceAffranchissementS10(ServiceAffranchissementCommun):
    """
    Service d'affranchissement pour les S10
    """

    REGEX = r"^([A-Za-z]{2})([0-9]{8})([0-9]{1})([A-Za-z]{2})$"
    TYPE = TypeAffranchissementEnum.S10
    
    @classmethod
    def donnees(cls, code=None):
        """
        Extrait les données du code S10 en utilisant la REGEX définie dans la classe
        """
        match = re.match(cls.REGEX, code)

        if not match:
            return None

        cpt = 1
        content = {
            "service": match.group(cpt).upper(),
            "number": match.group(cpt + 1),
            "key": match.group(cpt + 2),
            "country": match.group(cpt + 3).upper(),
        }

        id = f"{content['service']}{content['number']}{content['key']}{content['country']}"
        segmented = f"{content['service']} {content['number']} {content['key']} {content['country']}"

        return {"id": id, 
                "segmented": segmented, 
                "content": content}

    def valoriser(self):
        """
        Valorise l'affranchissement S10 selon les règles suivantes:
        1. Valeur par défaut: 0 EUR
        2. Si une séquence est associée et active:
           a. Utiliser la valeur définie dans la séquence si elle existe et est différente de 0
           b. Sinon, appliquer les règles métier spécifiques (ex: 2.80 EUR pour LE de Philaposte)
        """
        self.affranchissement.prix_unite_devise = 0
        self.affranchissement.devise = DeviseEnum.EURO

        sequence = self.sequence_associee()

        # Si pas de séquence ou séquence inactive, on garde la valeur par défaut (0)
        if not sequence or not sequence.active:
            return
        
        # 1. Vérifier si la séquence a une valeur définie
        valeur_sequence = sequence.valeur.get("valeur")
        try:
            valeur_float = float(valeur_sequence)
            if valeur_float >= 0:
                self.affranchissement.prix_unite_devise = valeur_float
                return
        except (TypeError, ValueError):
            # valeur_sequence n'est pas convertible en float, on ne fait rien
            pass
        
        # 2. Sinon, appliquer les règles métier spécifiques
        if sequence.valeur.get("emetteur", "").lower() == "philaposte" and self.affranchissement.donnees["content"]["service"] == "LE":
            self.affranchissement.prix_unite_devise = CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10

    def sequence_associee(self):
        """
        Retourne la séquence S10 associée à l'affranchissement
        Recherche parmi toutes les séquences (actives ou non)
        """
        donnees = self.affranchissement.donnees

        if donnees is None:
            return None

        service = donnees["content"]["service"]
        number = donnees["content"]["number"]

        from core.db import get_session
        with get_session() as session:
            statement = select(RegleMetier).where(
                RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
                RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE,
                # Ne pas filtrer sur active=True pour rechercher parmi toutes les séquences
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'service') == service),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'debut') <= number),
                (func.json_extract_path_text(cast(RegleMetier.valeur, JSON), 'fin') >= number)
            )
            
            return session.exec(statement).first()

    def champs_requis(self):
        if len(self.affranchissement.code or "") == 0:
            return ["nature"]
        
        return []
