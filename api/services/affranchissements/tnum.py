import re
from services.affranchissements.commun import ServiceAffranchissementCommun
from constants.enumerations import TypeAffranchissementEnum


class ServiceAffranchissementTNUM(ServiceAffranchissementCommun):
    """
    Service d'affranchissement pour les TNUM
    """

    REGEX = r"^([A-Za-z0-9]{2})([A-Za-z0-9]{2})([A-Za-z0-9]{2})([A-Za-z0-9]{2})$"
    TYPE = TypeAffranchissementEnum.TNUM
    
    @classmethod
    def donnees(cls, code=None):
        """
        Extrait les données du code TNUM en utilisant la REGEX définie dans la classe
        """
        match = re.match(cls.REGEX, code)

        if not match:
            return None

        content = {
            "car12": match.group(1).upper(),
            "car34": match.group(2).upper(),
            "car56": match.group(3).upper(),
            "car78": match.group(4).upper(),
        }

        id = f"{content['car12']}{content['car34']}{content['car56']}{content['car78']}"
        segmented = f"{content['car12']} {content['car34']} {content['car56']} {content['car78']}"

        return {"id": id, 
                "segmented": segmented, 
                "content": content}
