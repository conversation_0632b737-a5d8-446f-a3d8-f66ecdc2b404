
from constants.enumerations import NatureAffranchissementEnum, TypeAffranchissementEnum, CategorieAffranchissementEnum, DeviseEnum, SousTypeAffranchissementEnum, StatutVerificationEnum
from models.business import Affranchissement
from models.public import ModeleAffranchissement
from constants.affranchissements.affranchissement import CATEGORIES_AFFRANCHISSEMENTS
from core.utils.prix import convertir_prix_devise_en_euros
from constants.affranchissements.nature import NATURES_AFFRANCHISSEMENT
from constants.variables import CONSTANTES_PRIX_AFFRANCHISSEMENT
import re
from services.affranchissements.commun import ServiceAffranchissementCommun
from services.affranchissements.smart_data import ServiceAffranchissementSDN, ServiceAffranchissementSDX
from services.affranchissements.s10 import ServiceAffranchissementS10
from services.affranchissements.tnum import ServiceAffranchissementTNUM

class ServiceAffranchissement:

    # Définition du mapping des classes en fonction de la catégorie, type et sous-type
    CLASSES_MAPPING = {
        # Mapping par catégorie
        CategorieAffranchissementEnum.CODE: {
            # Mapping par type
            TypeAffranchissementEnum.S10: ServiceAffranchissementS10,
            TypeAffranchissementEnum.SD: {
                # Mapping par sous-type
                SousTypeAffranchissementEnum.SDN: ServiceAffranchissementSDN,
                SousTypeAffranchissementEnum.SDX: ServiceAffranchissementSDX
            },
            TypeAffranchissementEnum.TNUM: ServiceAffranchissementTNUM,
            None: ServiceAffranchissementCommun  # Fallback pour CODE sans type spécifié
        },
        CategorieAffranchissementEnum.MARI: ServiceAffranchissementCommun
    }
    
    @classmethod
    def identifier_type_affranchissement(cls, code=None) -> dict:
        """Identifie le type d'affranchissement à partir d'un code"""
        if not code:
            return None
            
        classes_existantes = set()
        cls._extraire_classes(cls.CLASSES_MAPPING, classes_existantes)

        for classe in classes_existantes:
            if getattr(classe, 'REGEX', None):
                donnees = classe.donnees(code)
                if donnees:
                    return {
                        "type": classe.TYPE,
                        "categorie": CategorieAffranchissementEnum.CODE,
                        "sous_type": getattr(classe, 'SOUS_TYPE', SousTypeAffranchissementEnum.NON_DETERMINE),
                        "donnees": donnees,
                        "origine": donnees.get("content", {}).get("origin")
                    }
        return None
    
    @staticmethod
    def _extraire_classes(mapping, classes_existantes):
        """Extrait toutes les classes de service du mapping"""
        if isinstance(mapping, dict):
            for key, value in mapping.items():
                if isinstance(value, dict):
                    ServiceAffranchissement._extraire_classes(value, classes_existantes)
                else:
                    classes_existantes.add(value)
        
    @classmethod
    def get_modele_affranchissement(cls, data: dict) -> dict:
        """Récupère le modèle d'affranchissement correspondant aux données"""
        data['categorie'] = CategorieAffranchissementEnum(data['categorie'])
        data['type'] = TypeAffranchissementEnum(data['type'])
        
        user_price = data.get('prix_unite_devise') or -1
        
        # Vérifier si la catégorie existe
        if data['categorie'] not in CATEGORIES_AFFRANCHISSEMENTS:
            raise ValueError(f"La catégorie {data['categorie']} n'existe pas")
            
        categorie = CATEGORIES_AFFRANCHISSEMENTS[data['categorie']]
        types = [t for t in categorie.types_affranchissements if t.type == data['type'] and t.origine == data.get('origine')]

        # Cas sans devise spécifiée (typiquement pour CODE)
        if data.get('devise') is None:
            if len(types) == 1:
                return types[0]
            raise ValueError("Aucun type d'affranchissement correspondant trouvé")

        # Avec devise spécifiée
        data['devise'] = DeviseEnum(data['devise'])
        types_devises = [t for t in types if t.devise == data['devise']]
        
        # Recherche par priorité: prix exact, puis valorisable
        types_non_valorisables = [t for t in types_devises if t.prix_unite_devise == user_price]
        types_valorisables = [t for t in types_devises if t.valorisable]
        
        # Retourne le premier type correspondant selon la priorité
        if types_non_valorisables:
            return types_non_valorisables[0]
        elif types_valorisables:
            return types_valorisables[0]
        elif len(types) == 1:
            return types[0]
            
        raise ValueError("Le type d'affranchissement n'a pas été identifié")
    
    @classmethod
    def create_affranchissement(cls, data: dict) -> Affranchissement:
        """Crée un affranchissement à partir des données fournies"""
        # Traitement du code s'il existe
        if code := data.get('code', ''):
            # Nettoyage des espaces avant et après le code
            code = code.strip()
            data['code'] = code
            
            identify_type = cls.identifier_type_affranchissement(code=code)
            
            if not identify_type:
                raise ValueError(f"Le code {code} n'a pas été identifié.")
            
            data['type'] = identify_type['type']
            data['categorie'] = identify_type['categorie']
            data['sous_type'] = identify_type['sous_type']
            data['origine'] = identify_type['origine']
            data['quantite'] = 1
            
        # Traitement de la nature
        try:
            data['nature'] = NatureAffranchissementEnum(data.get('nature'))
        except (ValueError, TypeError):
            data['nature'] = None

        # Récupération du modèle d'affranchissement
        modele_affranchissement = cls.get_modele_affranchissement(data)
        
        if modele_affranchissement is None:
            raise ValueError("Le type d'affranchissement n'a pas été identifié")
        
        if data.get("prix_unite_devise") is None and modele_affranchissement.valorisable:
            raise ValueError("Aucune valeur renseignée pour le prix de l'affranchissement")
        
        # Fusion des données et création de l'affranchissement
        final_data = {**data, **modele_affranchissement.dict()}
        
        if modele_affranchissement.valorisable:
            final_data["prix_unite_devise"] = data.get("prix_unite_devise")
        
        affranchissement = Affranchissement(**final_data)
        
        if affranchissement.type == TypeAffranchissementEnum.NON_DETERMINE:
            raise ValueError("Le type d'affranchissement n'a pas été identifié")
        
        # REGLE_METIER
        # Forcer le statut INVALIDE pour les affranchissements manuels sans code
        if (affranchissement.categorie == CategorieAffranchissementEnum.CODE and 
            (not affranchissement.code or affranchissement.code == "") and data.get("statut") != "VALIDE"):
            affranchissement.statut = StatutVerificationEnum.INVALIDE
            if "statut" in data:
                data.pop("statut")
        
        affranchissement.quantite = data.get('quantite', 1) or 1
        
        return affranchissement
    

    def __new__(cls, affranchissement: Affranchissement):
        """Factory method pour créer le service approprié selon le type d'affranchissement"""
        def get_service(mapping, keys=None, index=0):
            if keys is None:
                keys = [affranchissement.categorie, affranchissement.type, affranchissement.sous_type]
            
            # Si on a atteint une classe ou la fin des clés
            if not isinstance(mapping, dict) or index >= len(keys):
                return mapping
            
            # Récupérer la valeur pour la clé actuelle ou le fallback (None)
            value = mapping.get(keys[index], mapping.get(None, ServiceAffranchissementCommun))
            
            # Continuer la récursion si c'est un dictionnaire
            return get_service(value, keys, index + 1)
        
        # Récupérer et instancier la classe de service
        service_class = get_service(cls.CLASSES_MAPPING)
        
        return service_class(affranchissement)
