import csv
from collections import defaultdict

# Chemin vers le fichier CSV
file_path = "/data/cape_monde_output.csv"

# Dictionnaires pour stocker les comptes de valeurs valides et invalides par colonne
valid_counts = defaultdict(lambda: defaultdict(int))
invalid_counts = defaultdict(lambda: defaultdict(int))

# Variables pour compter le nombre total de lignes, valides et invalides
total_lines = 0
# Lire le fichier CSV
with open(file_path, newline='') as csvfile:
    reader = csv.DictReader(csvfile)
    
    # Lire chaque ligne du fichier CSV
    for row in reader:
        total_lines += 1
        statut = row['statut'].strip().upper()
        code = row['code'].strip().upper()
        
        # Vérifier le statut et incrémenter les compteurs appropriés
        for column, value in row.items():
            value = value.strip()
            value = value.replace(code[0:-3], "CODE")
            value = value.replace(code, "CODE")

            if statut == "VALIDE":
                valid_counts[column][value] += 1
            elif statut == "INVALIDE":
                invalid_counts[column][value] += 1

# Afficher les résultats
print("Comptes de valeurs valides et invalides pour chaque colonne:")
for column in valid_counts.keys():
    print(f"Colonne '{column}':")

    if column in ["code"]:
        continue
    
    print("  Valides:")
    for value, count in valid_counts[column].items():
        if "non reconnue" in value.lower():
            continue
        print(f"    {value}: {count}")

    print("  Invalides:")
    for value, count in invalid_counts[column].items():
        print(f"    {value}: {count}")

    print("\n")

# Afficher le nombre total de lignes et les ratios
if "statut" in valid_counts:
    valide_count = sum(valid_counts["statut"].values())
    invalide_count = sum(invalid_counts["statut"].values())
    print(f"Nombre total de lignes: {total_lines}")
    print(f"Nombre total de lignes valides: {valide_count} = {valide_count / total_lines * 100}%")
    print(f"Nombre total de lignes invalides: {invalide_count} = {invalide_count / total_lines * 100}%")

