from openpyxl import load_workbook
from pathlib import Path
from typing import List
from models.business import RegleMetier
from constants.enumerations import TypeRegleMetierEnum, TypeAffranchissementEnum
from datetime import datetime
import re

def import_sequence_s10_depuis_excel(file_path: str) -> List[RegleMetier]:
    """
    Lit un fichier Excel et crée des objets RegleMetier à partir des données.
    
    Args:
        file_path (str): Chemin vers le fichier Excel
        
    Returns:
        List[RegleMetier]: Liste des règles métier créées
    """
    try:
        # Vérifier si le fichier existe
        if not Path(file_path).exists():
            raise FileNotFoundError(f"Le fichier {file_path} n'existe pas")
            
        # Charger le workbook
        wb = load_workbook(filename=file_path, read_only=True)
        # Prendre la première feuille
        ws = wb.active
        
        regles_metier = []
        # Récupérer les en-têtes (première ligne)
        headers = [cell.value for cell in next(ws.rows)]

        i = 0
        
        # Parcourir chaque ligne à partir de la deuxième ligne
        for row in list(ws.rows)[1:]:

            # Créer un dictionnaire avec les valeurs de la ligne et convertir les clés en minuscules
            row_data = {headers[i].lower(): str(cell.value) for i, cell in enumerate(row) if headers[i] is not None}

            # Mapping des noms de colonnes
            column_mapping = {
                'type de support': 'type_support',
                'séquence': 'sequence',
                'libellé': 'libelle',
                'description': 'description',
                'borne maxi': 'fin',
                'borne mini': 'debut',
                'volume': 'volume',
                'date d\'attribution': 'date_attribution',
                'date de la demande': 'date_demande',
                'numéro de \ndébut de plage': 'debut',
                'numéro de \nfin de plage': 'fin',
                'date\nattribution': 'date_attribution',
                'code\nproduit': 'type_support',
                # Ajoutez d'autres mappings selon vos besoins
            }

            # Renommer les clés du dictionnaire
            row_data = {
                column_mapping.get(key, key): value 
                for key, value in row_data.items()
            }

            # Nettoyer les espaces et ajouter des zéros au début pour avoir 8 chiffres
            row_data["fin"] = row_data["fin"].replace(" ", "").zfill(8)
            row_data["debut"] = row_data["debut"].replace(" ", "").zfill(8)
            # Extraire le service (2 lettres) à partir du type_support
            
            if "type_support" in row_data:
                service_match = re.search(r'([A-Z]{2})', row_data["type_support"].upper())
                if service_match:
                    row_data["service"] = service_match.group(1)
                else:
                    raise Exception(f"Impossible d'extraire le service (2 lettres) du type de support: {row_data.get('type_support')}")
            else:
                raise Exception("La colonne 'type_support' est manquante dans le fichier Excel")

            if not row_data["service"].startswith("L"):
                raise Exception(f"Service non supporté: {row_data['service']}")
            
            if "date_attribution" not in row_data:
                raise Exception(f"Date d'attribution non supportée: {row_data['date_attribution']}")

            # Formater la date_attribution au format DD/MM/YYYY
            date_str = row_data["date_attribution"]
            try:
                # Essayer différents formats de date
                if "-" in date_str and ":" in date_str:  # Format "2011-05-23 00:00:00"
                    date_obj = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
                elif "-" in date_str:  # Format "07-09-2021"
                    if len(date_str.split("-")[0]) == 4:  # Si l'année est en premier (YYYY-MM-DD)
                        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                    else:  # Si le jour est en premier (DD-MM-YYYY)
                        date_obj = datetime.strptime(date_str, "%d-%m-%Y")
                elif "/" in date_str:  # Format "DD/MM/YYYY"
                    date_obj = datetime.strptime(date_str, "%d/%m/%Y")
                else:
                    raise ValueError(f"Format de date non reconnu: {date_str}")
                
                # Convertir au format DD/MM/YYYY
                row_data["date_attribution"] = date_obj.strftime("%d/%m/%Y")
            except Exception as e:
                raise Exception(f"Erreur lors du formatage de la date '{date_str}': {str(e)}")

            # print(row_data["date_attribution"])

            regle = RegleMetier(
                cle=f"S10_{row_data['service']}_{i}",
                type_affranchissement=TypeAffranchissementEnum.S10,
                type_regle=TypeRegleMetierEnum.SEQUENCE,
                valeur=row_data
            )
            regles_metier.append(regle)
            i += 1

        wb.close()

        return regles_metier
        
    except Exception as e:
        raise
        raise Exception(f"Erreur lors de l'importation du fichier Excel: {str(e)}")

# Nouveau code pour traiter tous les fichiers L*.xlsx
def import_all_L_sequences(directory: str) -> List[RegleMetier]:
    """
    Importe toutes les règles métier depuis les fichiers Excel commençant par 'L' dans le dossier spécifié.
    
    Args:
        directory (str): Chemin vers le dossier contenant les fichiers Excel
        
    Returns:
        List[RegleMetier]: Liste de toutes les règles métier importées
    """
    all_rules = []
    directory_path = Path(directory)
    
    # Recherche tous les fichiers .xlsx commençant par L
    for excel_file in directory_path.glob("L*.xlsx"):
        rules = import_sequence_s10_depuis_excel(str(excel_file))
        all_rules.extend(rules)
    
    return all_rules

def import_s10_sequence():
# Modification de l'appel principal
    rules = import_all_L_sequences("/data/riposte")

    from core.db import get_session
    with get_session() as session:

        # Supprimer toutes les règles existantes
        session.query(RegleMetier).filter(
            RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
            RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE
        ).delete()

        # Rechercher la règle d'expiration par clé et types
        regle_expiration = session.query(RegleMetier).filter(
            RegleMetier.cle == "S10_SEQUENCE_EXPIRATION_ANNEES",
            RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10,
            RegleMetier.type_regle == TypeRegleMetierEnum.VALEUR
        ).first()
        
        # Créer la règle si elle n'existe pas
        if not regle_expiration:
            regle_expiration = RegleMetier(
                cle="S10_SEQUENCE_EXPIRATION_ANNEES", 
                type_affranchissement=TypeAffranchissementEnum.S10,
                type_regle=TypeRegleMetierEnum.VALEUR, 
                valeur={"valeur": 2}
            )
            session.add(regle_expiration)

        session.add_all(rules)
        session.commit()



def deactiver_sequences(annees=2):
    from core.db import get_session
    from datetime import datetime, timedelta

    with get_session() as session:
        # Récupérer toutes les règles métier de type séquence
        sequences = session.query(RegleMetier).filter(
            RegleMetier.type_regle == TypeRegleMetierEnum.SEQUENCE
        ).all()

        # Date limite (2 ans avant aujourd'hui)
        date_limite = datetime.now() - timedelta(days=annees*365)

        for sequence in sequences:
            date_attribution = datetime.strptime(sequence.valeur['date_attribution'], "%d/%m/%Y")
            if date_attribution < date_limite:
                # Désactiver la séquence
                sequence.active = False
                session.add(sequence)
                print(sequence.cle)

        session.commit()
    print("Désactivation des séquences de plus de 2 ans terminée.")


if __name__ == "__main__":
    deactiver_sequences()