from core.utils.xlsx import extract_column_values
from sqlmodel import select, text
from core.db import get_session
from models.business import CodeAffranchissementFrauduleux
import threading


SIZE_BATCH = 100000


with get_session() as SESSION:
    # EXISTING_CODES = set(SESSION.exec(select(CodeAffranchissementFrauduleux.code)).all())
    def get_existing_codes_efficient():
        result = SESSION.execute(text("SELECT code FROM codeaffranchissementfrauduleux"))
        return {row[0] for row in result}  
    EXISTING_CODES = get_existing_codes_efficient()
    print(len(EXISTING_CODES))

    def process_batches_in_threads(batch):
        # Diviser le batch en sous-batches
        num_threads = 10
        sub_batches = [batch[i::num_threads] for i in range(num_threads)]
        
        threads = []
        for sub_batch in sub_batches:
            thread = threading.Thread(target=process_batch, args=(sub_batch,))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()

    def process_batch(file_path: str, sheet_id: int):
        """
        Optimisé pour PostgreSQL avec ON CONFLICT DO NOTHING.
        """
        from sqlalchemy import text
        
        # Lire les codes
        codes = extract_column_values(file_path, "ITEM_ID", sheet_id)
        codes = [c.strip() for c in codes if c and c.strip()]
        print("Total de codes lus: ", len(codes))

        # Complexité O(1) pour chaque test d'appartenance
        codes = [code for code in codes if code not in EXISTING_CODES]
        print("Total de codes non existants: ", len(codes))
        
        if len(codes) == 0:
            return 0
        
        # Requête ultra-optimisée avec ON CONFLICT pour gérer les doublons
        query = text("""
            INSERT INTO codeaffranchissementfrauduleux (code, type, created_at)
            SELECT unnest(CAST(:codes AS TEXT[])), 'AUTO', NOW()
            ON CONFLICT (code) DO NOTHING
            RETURNING code
        """)
        
        # Exécuter en une seule requête
        result = SESSION.execute(query, {"codes": codes})
        SESSION.commit()
        
        # Compter les lignes ajoutées
        added_codes = result.fetchall()
        print(f"Total de codes ajoutés: {len(added_codes)}")
        return len(added_codes)

    if __name__ == "__main__":

        print("Starting process")   
        FILES = [
            ("/data/codes_vus/cape kpg all 1022.xlsx", 1),
            ("/data/codes_vus/2024 LE LB LV LY monde .xlsx", 1),
            ("/data/codes_vus/2024 LE monde .xlsx", 1),
            ("/data/codes_vus/cape kpg all 0123.xlsx", 1),
            ("/data/codes_vus/cape kpg all 0124.xlsx", 1),
            ("/data/codes_vus/cape kpg all 0223.xlsx", 1),
            ("/data/codes_vus/cape kpg all 0323.xlsx", 1),
            ("/data/codes_vus/cape kpg all 0423.xlsx", 1),
            ("/data/codes_vus/cape kpg all 0523.xlsx", 1),
            ("/data/codes_vus/cape kpg all 0623.xlsx", 1),
            ("/data/codes_vus/cape kpg all 0723.xlsx", 1),
            ("/data/codes_vus/cape kpg all 0823.xlsx", 1),
            ("/data/codes_vus/cape kpg all 0922.xlsx", 1),
            ("/data/codes_vus/cape kpg all 0923.xlsx", 1),            
            ("/data/codes_vus/cape kpg all 1023.xlsx", 1),
            ("/data/codes_vus/cape kpg all 1122.xlsx", 1),
            ("/data/codes_vus/cape kpg all 1123.xlsx", 1),
            ("/data/codes_vus/cape kpg all 1222.xlsx", 1),
            ("/data/codes_vus/cape kpg all 1223.xlsx", 1),
            ("/data/codes_vus/cape RW monde 2024.xlsx", 1),
            ("/data/codes_vus/cape monde LB LE LY LV T1 2025.xlsx", 0),
        ]

        for file in FILES:
            print(f"Processing file: {file[0]}")
            process_batch(file[0], file[1])
