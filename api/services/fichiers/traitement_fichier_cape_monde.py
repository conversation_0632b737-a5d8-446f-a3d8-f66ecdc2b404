from core.utils.xlsx import process_xslx
from services.affranchissement import ServiceAffranchissement
import json
import csv
import os
import cProfile
existing_codes = set()
from services.validateurs.validateur_affranchissement import ValidateurAffranchissement
from models.business import Affranchissement
from constants.enumerations import TypeAffranchissementEnum, CategorieAffranchissementEnum, StatutVerificationEnum
# Lire les codes existants dans le fichier CSV

# /data/cape monde LB LE LY LV T1 2025.xlsx

import threading

SIZE_BATCH = 256

if os.path.exists("/data/cape_monde_output.csv"):
    with open("/data/cape_monde_output.csv", "r", newline='') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            existing_codes.add(row["code"])

def process_batches_in_threads(batch):
    # Diviser le batch en sous-batches
    num_threads = SIZE_BATCH
    sub_batches = [batch[i::num_threads] for i in range(num_threads)]
    
    threads = []
    for sub_batch in sub_batches:
        thread = threading.Thread(target=process_batch, args=(sub_batch,))
        threads.append(thread)
        thread.start()
    
    for thread in threads:
        thread.join()

def process_batch(batch: list[dict]):
    csv_rows = []  # Liste pour stocker les lignes du CSV
    
    for line in batch:
        code = line.get("item_id").strip()

        # Vérifier si le code existe déjà
        if code in existing_codes:
            print("by passs")
            continue

        # print(code)
        # continue

        affranchissement = Affranchissement(
            code=code,
            type=TypeAffranchissementEnum.S10,
            categorie=CategorieAffranchissementEnum.CODE,
            statut=StatutVerificationEnum.VALIDE
        )

        ValidateurAffranchissement.executer_validations(affranchissement)

        # Récupérer la dernière vérification
        last_verification = affranchissement.verifications[-1] if affranchissement.verifications else None

        if last_verification:
            csv_rows.append({
                "code": code,
                "statut": last_verification.statut,
                "verification_message": last_verification.message,
                "verification_type": last_verification.type
            })

    # Écrire les résultats dans le fichier CSV
    with open("/data/cape_monde_output.csv", "a", newline='') as csvfile:
        fieldnames = ["code", "statut", "verification_message", "verification_type"]
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

        # Écrire l'en-tête uniquement si le fichier est vide
        if csvfile.tell() == 0:
            writer.writeheader()

        writer.writerows(csv_rows)

if __name__ == "__main__":
    data = process_xslx("/data/cape monde LB LE LY LV T1 2025.xlsx", process_batches_in_threads, SIZE_BATCH)
