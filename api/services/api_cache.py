import json
import redis
import requests
import os
import base64
from typing import Any, Dict, Optional, Tuple
from services.redis_cache import RedisCache

class ApiCache:
    @staticmethod
    def get_json_data(
        api_url: str, 
        cache_key: str, 
        headers: Dict[str, str] = None,
        expiration_days: int = 30,
        no_cache: bool = False
    ) -> Tuple[Any, Optional[str]]:
        """
        Récupère des données JSON depuis une API avec mise en cache Redis.
        
        Args:
            api_url: URL de l'API à appeler
            cache_key: Clé unique pour stocker les données dans Redis
            headers: En-têtes HTTP optionnels pour la requête API
            expiration_days: Durée d'expiration du cache en jours (défaut: 30 jours)
            no_cache: Si True, ignore le cache et force l'appel API
            
        Returns:
            Tuple contenant (données JSON, message d'erreur éventuel)
        """
        # Construction clé de cache = cache_key + url base64
        full_cache_key = f"{cache_key}:{base64.b64encode(api_url.encode('utf-8')).decode('utf-8')}"
        
        try:
            # Utiliser RedisCache.get_or_set pour gérer le cache
            def fetch_api_data():
                try:
                    response = requests.get(api_url, headers=headers)
                    response.raise_for_status()
                    return response.json()
                except requests.exceptions.RequestException as e:
                    raise Exception(f"Erreur API: {str(e)}")
                except json.JSONDecodeError:
                    raise Exception("Réponse non-JSON reçue de l'API")
            
            # Récupérer les données avec cache
            json_data = RedisCache.get_or_set(
                key=full_cache_key,
                expiration_days=expiration_days,
                method=fetch_api_data,
                no_cache=no_cache
            )
            
            return json_data, None
            
        except redis.RedisError as e:
            # Erreur Redis, on continue sans cache
            try:
                response = requests.get(api_url, headers=headers)
                response.raise_for_status()
                return response.json(), None
            except requests.exceptions.RequestException as req_err:
                return None, f"Erreur API: {str(req_err)}"
                
        except Exception as e:
            return None, str(e)
