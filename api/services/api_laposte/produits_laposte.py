from services.api_cache import ApiCache
from services.redis_cache import RedisCache
import time
import json
import requests

def recuperer_produits_par_page(page: int = 0, page_size: int = 96):
    headers = {
        "Accept": "application/json, text/plain, */*",
        "Accept-Language": "fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Referer": "https://www.laposte.fr/pp/c/tous-les-produits?q=%3AnbStampsPerPresentation%3A1",
        "x-cacheable": "true",
        "User-Agent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0",
        "Connection": "keep-alive"
    }
    
    url = f"https://www.laposte.fr/ecom/occ/ecommerce/occ/v2/lpelPart/products/search?query=:relevance:allCategories:tous-les-produits&fields=FULL&currentPage={page}&pageSize={page_size}&rank=rank1"
    response = requests.get(url, headers=headers)
    response.raise_for_status()

    return response.json()

def recuperer_tous_les_produits():
    """
    Récupère tous les produits de La Poste en parcourant toutes les pages
    et retourne un objet contenant tous les produits.
    """
    def recuperer():
        all_products = []
        current_page = 0
        total_pages = None
        page_size = 96
            
        while total_pages is None or current_page < total_pages:        
            response = recuperer_produits_par_page(current_page, page_size)
            
            if total_pages is None:
                total_pages = response["pagination"]["totalPages"]
            
            # Ajouter les produits de la page courante à la liste complète
            all_products.extend(response["products"])
            
            # Passer à la page suivante
            current_page += 1

        print(all_products)
        
        return all_products
    


    return RedisCache.get_or_set(
        key="laposte_tous_produits",
        expiration_days=30,
        method=recuperer
    )

def recuperer_produit_par_code_produit(code_produit: str):
    produits = recuperer_tous_les_produits()
    return next((produit for produit in produits if produit["code"] == code_produit), None)

def recuperer_produit_par_mot_cle(mots_cle: list[str]):
    produits = recuperer_tous_les_produits()
    produits_filtres = [produit for produit in produits if all(mot.lower() in produit["description"].lower() for mot in mots_cle)]
    return produits_filtres[0] if produits_filtres else None

def recherche_avance_produit(mots_cle: list[str], poids: int):
    produits = recuperer_tous_les_produits()
    produits_filtres = [
        produit for produit in produits 
        if all(mot.lower() in produit["description"].lower() for mot in mots_cle) 
        and produit.get("maxSendingWeight") == poids
    ]
    return produits_filtres

def tous_timbres():
    produits = recuperer_tous_les_produits()
    
    timbres = [produit for produit in produits if produit.get("name", "").startswith("Timbre")]
    return timbres

def tous_timbres_categorise():
    timbres = recuperer_tous_les_produits()

    # Cherche prioritaire
    # p = [produit for produit in timbres if "prio" in produit["name"]]
    # print(p)
    # aaa

    # Garde ceux qui sont des timbres
    mots_cles_non_timbres = ["feuille", "carnet", "souvenir", 
                             "bloc", "philatélique", "lot de", 
                             "livre des", "collection"]

    # Enleve les feuilles
    # timbres = [produit for produit in timbres if not produit.get("code", "").startswith("F")]
    timbres = [produit for produit in timbres if not any(mot.lower() in produit.get("name", "").lower() for mot in mots_cles_non_timbres)]

    categories = {
        "MARIANNE": ["marianne"],
        "BEAU": []
    }

    timbres_categories = {}
    for cle in categories.keys():
        timbres_categories[cle] = []

    for timbre in timbres:
        for categorie, mots_cle in categories.items():            
            if any(mot.lower() in timbre["name"].lower() for mot in mots_cle):
                timbres_categories[categorie].append(timbre)
                break

            # Sinon derniere cat par défaut
            timbres_categories[list(categories.keys())[-1]].append(timbre)

    # Ensuite pour chaque catégorie, garde un seul timbre quand poids et prix sont similaires
    for categorie in timbres_categories:
        timbres_uniques = []
        poids_prix_vus = set()
        
        for timbre in timbres_categories[categorie]:
            poids = timbre.get("maxSendingWeight", 0)
            prix = timbre.get("basePrice", {}).get("value", 0)
            
            # Créer une clé unique pour ce couple poids/prix
            cle_poids_prix = f"{poids}_{prix}"
            
            # Si cette combinaison n'a pas encore été vue, ajouter le timbre
            if cle_poids_prix not in poids_prix_vus:
                timbres_uniques.append(timbre)
                poids_prix_vus.add(cle_poids_prix)
        
        # Remplacer la liste originale par la liste dédupliquée
        timbres_categories[categorie] = timbres_uniques
    
    return timbres_categories

def afficher_timbre_info(t):
    print(f"{t['code']} - {t['name']} - {t['basePrice']['value']} - {t['maxSendingWeight']}g - {t['type']}")
        

def recherche_par_prix(prix):
    timbres = recuperer_tous_les_produits()
    timbres = list(filter(lambda t: t['basePrice']['value'] == prix, timbres))
    for t in timbres:
         (afficher_timbre_info(t))
# Exemple d'utilisation
if __name__ == "__main__":
    # produits = recuperer_tous_les_produits()
    # produit = recherche_avance_produit(["lettre verte"], 100)
    # if produit:
    #     print(json.dumps(produit, indent=4, ensure_ascii=False))
    # else:
    #     print("Aucun produit trouvé pour le mot-clé 'lettre verte'")

    # print(len(tous_timbres()))
    # Affiche les noms des timbres
    timbres = tous_timbres_categorise()

    print(len(timbres["MARIANNE"]))

    for cle, timbres in timbres.items():
        print(f"{cle} - {len(timbres)}")
        for t in timbres:
            afficher_timbre_info(t)
            # print(json.dumps(t, indent=4, ensure_ascii=False))


    # for cle, timbres in tous_timbres_categorise().items():
    #     print(len(timbres))



# print("---------------------")
# print("---------------------")
# print("---------------------")
# recherche_par_prix(2.1)