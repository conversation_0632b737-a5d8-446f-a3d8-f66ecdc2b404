import os
import requests
from models.encaissement import MoyenPaiement, MoyenPaiementSelectionne
from models.business import PaiementLotExpediteur, TransactionPaiementLotExpediteur, StatutPaiementEnum


class ServiceEncaissement:
    """
    Service pour la gestion des encaissements
    API Laposte Scelius : /payment_means/
    """
    ACCESS_TOKEN = None

    def headers(self):
        return {
            "Authorization": f"Bearer {self.recuperer_token_access()}",
            "Content-Type": "application/json",
        }
    
    def recuperer_token_access(self):
        """
        Récupère un jeton d'accès pour la suite des appels
        """

        def recuperer_token_access():
            response = requests.post(
                "https://apim-gw-acc.net.extra.laposte.fr/token",
                headers={
                    "Authorization": f"Basic {os.environ['SCELIUS_API_TOKEN']}",
                    "Content-Type": "application/x-www-form-urlencoded"
                },
                data={"grant_type": "client_credentials"}
            )
            response.raise_for_status()
            token = response.json()["access_token"]

            return token
        
        return recuperer_token_access()
        
        # # Récupérer les données avec cache
        # json_data = RedisCache.get_or_set(
        #     key="apim_token",
        #     expiration_days=expiration_days,
        #     method=fetch_api_data,
        #     no_cache=no_cache
        # )
        # # Vérifier si le token existe dans Redis
        # redis_client = Redis()
        # token = redis_client.get("access_token")

        

    def get_moyens_paiement(self, paiement_lot_expediteur: PaiementLotExpediteur = None):
        """
        Récupère la liste des moyens de paiement disponibles
        """

        #TEMP: SACC_0000001_RIPOSTE
        # 
        params = {
            "customer_repository": "GUEST",       
            "typology_customer": "PART",
            "amount": "500",
            "currency": "EUR",
            "APIM-O2-ApplicationName": os.getenv("SCELIUS_APP_NAME"),
            "channel":os.getenv("SCELIUS_APP_CHANNEL")    
        }
        
        response = requests.get(
            f"{os.getenv('SCELIUS_BASE_URL')}/methods",
            headers=self.headers(),
            params=params
        )
        
        methods = response.json()

        return [
            MoyenPaiement(
                nom=method["method_name"],
                url=method["method_url"],
                label=method["french_label"],
                authorization_token=method["authorization_token"]
            ) for method in methods
        ]
    
    def payer(self, moyen_paiement: MoyenPaiementSelectionne, paiement_lot_expediteur: PaiementLotExpediteur):
        if not moyen_paiement:
            raise Exception("Moyen de paiement non trouvé")

        # Récupération des informations du paiement
        montant_en_centimes = int(paiement_lot_expediteur.montant_ttc)
        
        # Création d'un identifiant unique pour la commande
        order_id = paiement_lot_expediteur.id_paiement
        
        # Récupération de l'email de l'expéditeur si disponible
        donnees_expediteurs = {}

        if paiement_lot_expediteur.lot_expediteur and paiement_lot_expediteur.lot_expediteur.expediteur:
            expediteur = paiement_lot_expediteur.lot_expediteur.expediteur
            donnees_expediteurs = {
                "first_name": expediteur.nom,
                "last_name": "",
                "address1": expediteur.adresse,
                "address2": "",
                "city": expediteur.ville,
                "zip_code": expediteur.code_postal,
                "country": expediteur.pays
            }
        
        response = requests.post(
            moyen_paiement.url,
            headers=self.headers(),
            json={
                "authorization_token": moyen_paiement.jeton,
                "order_id": paiement_lot_expediteur.id_paiement,
                "order_details": {
                    "order_id": order_id,
                    "amount": montant_en_centimes,
                    "currency": "EUR"
                },
                "account_details": {
                    "billing_address": {
                        **donnees_expediteurs
                    }
                },
                "delivery_mode": "BILLING_ADDRESS"  #TODO en fonction du choix (si pas CAS C uniquement)
            }
        )

        return response.json()

    def suivre_transaction(self, transaction_id: str):
        """
        Récupère le statut d'une transaction en cours ou passée
        """
        url = f"https://apim-gw-vente-acc.net.extra.laposte.fr/payment_means/v3/transactions/{transaction_id}"
        
        response = requests.get(
            url,
            headers=self.headers()
        )

        response.raise_for_status()

        return response.json()