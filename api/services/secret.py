from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.backends import default_backend
import base64

class SecretService:
    
    @staticmethod
    def dechiffer(encrypted: str, password: str) -> str:
        """Déchiffre un texte"""
        backend = default_backend()

        encrypted_bytes = base64.b64decode(encrypted)

        iv = encrypted_bytes[:16]  # IV est les 16 premiers octets
        encrypted_text = encrypted_bytes[16:]

        key = hashes.Hash(hashes.SHA256(), backend)
        key.update(password.encode("utf-8"))
        key = key.finalize()

        cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=backend)
        decryptor = cipher.decryptor()

        decrypted_padded = decryptor.update(encrypted_text) + decryptor.finalize()
        # Retirer le padding
        pad_length = decrypted_padded[-1]
        
        return decrypted_padded[:-pad_length].decode("utf-8")
    
class Mod37_36:
    @classmethod    
    def checksum(cls, number, alphabet='0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'):
        """Calculate the checksum. A valid number should have a checksum of 1."""
        modulus = len(alphabet)
        check = modulus // 2
        for n in number:
            check = (((check or modulus) * 2) % (modulus + 1) + alphabet.index(n)) % modulus
        return check
    
    @classmethod
    def calc_check_digit(cls, number, alphabet='0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'):
        """Calculate the extra digit that should be appended to the number to
        make it a valid number."""
        modulus = len(alphabet)
        return alphabet[(1 - ((cls.checksum(number, alphabet) or modulus) * 2) % (modulus + 1)) % modulus]