from azure.storage.blob import BlobClient
from azure.storage.blob.aio import BlobServiceClient as AsyncBlobServiceClient
from core.config import settings
import uuid

class StorageService:

    @classmethod
    def upload_file(cls, file_contents: bytes, blob_path: str, content_type: str = None) -> str:
        """
        Upload un fichier vers Azure Blob Storage en utilisant une URL SAS
        """
        # Génération d'un UUID aléatoire pour rendre l'URL unique
        random_uuid = str(uuid.uuid4())[:8]
        
        # Ajout de l'UUID au chemin du blob
        path_parts = blob_path.rsplit('.', 1) if '.' in blob_path else [blob_path, '']
        if len(path_parts) == 2:
            unique_blob_path = f"{path_parts[0]}_{random_uuid}.{path_parts[1]}"
        else:
            unique_blob_path = f"{blob_path}_{random_uuid}"
        
        blob_service_client = BlobClient.from_blob_url(settings.AZURE_STORAGE_CONNECTION_URL.replace("{blob_path}", unique_blob_path))
    
        blob_service_client.upload_blob(
            file_contents,
            blob_type="BlockBlob",
            content_type=content_type,
            overwrite=True
        )

        return blob_service_client.url 
    
    @classmethod
    def delete_file(cls, url: str):
        """
        Supprime un fichier dans Azure Blob Storage
        # """
        blob_service_client = BlobClient.from_blob_url(url)
        blob_service_client.delete_blob()

        # List files
        # blob_service_client = BlobClient.from_blob_url(settings.AZURE_STORAGE_CONNECTION_URL)
        # return blob_service_client.list_blobs()
