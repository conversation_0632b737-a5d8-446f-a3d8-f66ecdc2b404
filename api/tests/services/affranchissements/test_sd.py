from services.affranchissement import ServiceAffranchissement
from constants.enumerations import NatureAffranchissementEnum, TypeAffranchissementEnum, CategorieAffranchissementEnum, DeviseEnum, SousTypeAffranchissementEnum
from models.business import Affranchissement
from models.public import ModeleAffranchissement
import pytest
from tests.fixtures.codes import CODE_SDN_87
from services.affranchissements.commun import ServiceAffranchissementCommun
from services.validateurs.validateur_affranchissement import ValidateurAffranchissement

class TestServiceAffranchissementSD:

    def test_identifier_type_affranchissement_valid_code(self):
        test_cases = [
            ("%008364686504030336206393840A18^", 
             TypeAffranchissementEnum.SD, 
             SousTypeAffranchissementEnum.SDX, 
             {"content": {"customer": "", "origin": "86"}}),

            ("%000000087001039283642377404A10^~140", TypeAffranchissementEnum.SD, SousTypeAffranchissementEnum.SDX, {"content": {"customer": "~140", "origin": "87"}}),

            ("SD86123456789125A", TypeAffranchissementEnum.SD, SousTypeAffranchissementEnum.SDN, {}),
            ("SD87123456789125A", TypeAffranchissementEnum.SD, SousTypeAffranchissementEnum.SDN, {}),
            ("SD88123456789125A", TypeAffranchissementEnum.SD, SousTypeAffranchissementEnum.SDN, {}),


            # Test avec SD en prefix ou sans
            (CODE_SDN_87, TypeAffranchissementEnum.SD, SousTypeAffranchissementEnum.SDN, {}),
            ("SD"+CODE_SDN_87, TypeAffranchissementEnum.SD, SousTypeAffranchissementEnum.SDN, {}),
            
        ]

        for test_case in test_cases:
            code = test_case[0]
            expected_type = test_case[1]
            expected_sous_type = test_case[2]
            expected_data = test_case[3]

            affranchissement = ServiceAffranchissement.create_affranchissement({
                "code": code
            })

            assert affranchissement is not None, f"Failed for code {code}"

            service = affranchissement.service

            assert service is not None, f"Failed for code {code}"
            assert service.affranchissement.type == expected_type, f"Failed for type {code} {service.affranchissement.type}"
            assert service.affranchissement.sous_type == expected_sous_type, f"Failed for sous_type {code} {service.affranchissement.sous_type}"

            donnees = service.informations()["donnees"]

            assert donnees is not None, f"Failed for code {service.donnees()}"

            print(donnees)
        
            for key, value in expected_data.items():
                if isinstance(value, dict):
                    # Pour les dictionnaires imbriqués
                    for sub_key, sub_value in value.items():
                        assert donnees[key][sub_key] == sub_value, f"Failed for {code}: expected {key}.{sub_key}={sub_value}, got {donnees[key][sub_key]}"
                else:
                    # Pour les valeurs simples
                    assert donnees[key] == value, f"Failed for {code}: expected {key}={value}, got {donnees[key]}"