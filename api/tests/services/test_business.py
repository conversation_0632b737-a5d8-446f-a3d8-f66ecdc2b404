from models import Enveloppe
from constants.enumerations import DestinationEnveloppeEnum
from sqlmodel import Session
from datetime import datetime
import pytest


class TestBusiness:
    
    @pytest.fixture
    def data(self, enveloppe):
        return enveloppe
    
    def test_data(self, data: Enveloppe):
        assert data.destination_enveloppe == DestinationEnveloppeEnum.METROPOLE
        
    