
# from services.ssu_api import SSUApi


# class TestSSUApi:
    
#     def test_real_call(self):
#         response, error = SSUApi.get_tracking("88000043687094Q")

#         print(response)
#         import json
#         print(json.dumps(response, indent=4))
#         kaaa
#         assert error is None
#         assert response is not None
#         assert response.get("shipment") is not None
#         assert response.get("shipment").get("event") is not None
#         assert len(response.get("shipment").get("event")) > 0
