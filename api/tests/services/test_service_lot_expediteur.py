import pytest
from datetime import datetime
from unittest.mock import MagicMock, patch
from sqlmodel import Session
from models.business import LotExpediteur, Site, PaiementLotExpediteur
from models.users import User
from services.lot_expediteur import ServiceLotExpediteur
from constants.enumerations import StatutLotExpediteurEnum, OptionTraitementLotExpediteurEnum

class TestServiceLotExpediteur:
    
    @pytest.fixture
    def mock_session(self):
        return MagicMock()
    
    @pytest.fixture
    def service(self, mock_session):
        return ServiceLotExpediteur(mock_session)
    
    @pytest.fixture
    def site_libourne(self):
        site = Site(id=1, nom="LIBOURNE")
        return site
    
    @pytest.fixture
    def site_roissy(self):
        site = Site(id=2, nom="ROISSY")
        return site
    
    @pytest.fixture
    def user_libourne(self, site_libourne):
        user = User(id=1, username="user_libourne", site=site_libourne)
        return user
    
    @pytest.fixture
    def user_roissy(self, site_roissy):
        user = User(id=2, username="user_roissy", site=site_roissy)
        return user
    
    def test_changer_statut_lot_transition_autorisee_libourne(self, service, user_libourne):
        """Test le changement de statut avec une transition autorisée pour un utilisateur de Libourne"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=1, statut=StatutLotExpediteurEnum.FERME)
        
        # Appeler la méthode avec un nouveau statut autorisé pour Libourne
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.NOTIFIE, 
            user=user_libourne
        )
        
        # Vérifier que le statut a été changé
        assert result is not None
        assert result.statut == StatutLotExpediteurEnum.NOTIFIE
        assert result.user_modification_id == user_libourne.id
        assert result.updated_at is not None
        
        # Vérifier que la session a été commit
        service.session.commit.assert_called_once()
    
    def test_changer_statut_lot_transition_autorisee_roissy(self, service, user_roissy):
        """Test le changement de statut avec une transition autorisée pour un utilisateur de Roissy"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=2, statut=StatutLotExpediteurEnum.TRAITE_LIBERABLE)
        
        # Appeler la méthode avec un nouveau statut autorisé pour Roissy
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.TRAITE_OPERATIONNEL, 
            user=user_roissy
        )
        
        # Vérifier que le statut a été changé
        assert result is not None
        assert result.statut == StatutLotExpediteurEnum.TRAITE_OPERATIONNEL
        assert result.user_modification_id == user_roissy.id
        assert result.updated_at is not None
        
    
    def test_changer_statut_lot_transition_non_autorisee(self, service, user_roissy):
        """Test le changement de statut avec une transition non autorisée"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=3, statut=StatutLotExpediteurEnum.FERME)
        
        # Appeler la méthode avec un nouveau statut non autorisé pour Roissy
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.NOTIFIE, 
            user=user_roissy
        )
        
        # Vérifier que le résultat est None (transition non autorisée)
        assert result is None
        
        # Vérifier que le statut n'a pas été changé
        assert lot.statut == StatutLotExpediteurEnum.FERME
        
        # Vérifier que la session n'a pas été commit
        service.session.commit.assert_not_called()
    
    def test_changer_statut_lot_meme_statut(self, service, user_libourne):
        """Test le changement de statut avec le même statut"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=4, statut=StatutLotExpediteurEnum.FERME)
        
        # Appeler la méthode avec le même statut
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.FERME, 
            user=user_libourne
        )
        
        # Vérifier que le lot est retourné sans modification
        assert result is lot
        assert result.statut == StatutLotExpediteurEnum.FERME
        
        # Vérifier que la session n'a pas été commit
        service.session.commit.assert_not_called()
    
    def test_changer_statut_lot_transition_inexistante(self, service, user_libourne):
        """Test le changement de statut avec une transition qui n'existe pas dans la matrice"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=5, statut=StatutLotExpediteurEnum.OUVERT)
        
        # Appeler la méthode avec un nouveau statut qui n'est pas dans la matrice
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.TRAITE_OPERATIONNEL, 
            user=user_libourne
        )
        
        # Vérifier que le résultat est None (transition non autorisée)
        assert result is None
        
        # Vérifier que le statut n'a pas été changé
        assert lot.statut == StatutLotExpediteurEnum.OUVERT
        
        # Vérifier que la session n'a pas été commit
        service.session.commit.assert_not_called()
    
    def test_changer_statut_lot_liberation_casiers(self, service, user_roissy):
        """Test le changement de statut avec libération des casiers"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=6, statut=StatutLotExpediteurEnum.TRAITE_LIBERABLE)
        
        # Mock de la méthode liberer_casiers
        service.liberer_casiers = MagicMock()
        
        # Appeler la méthode avec un statut qui déclenche la libération des casiers
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.TRAITE_OPERATIONNEL, 
            user=user_roissy
        )
        
        # Vérifier que le statut a été changé
        assert result is not None
        assert result.statut == StatutLotExpediteurEnum.TRAITE_OPERATIONNEL
        
        # Vérifier que la méthode liberer_casiers a été appelée
        service.liberer_casiers.assert_called_once_with(lot)
        
        # Vérifier que la session a été commit
        service.session.commit.assert_called_once()
    
    def test_changer_statut_lot_none(self, service, user_libourne):
        """Test le changement de statut avec un lot None"""
        # Appeler la méthode avec un lot None
        result = service.changer_statut_lot_avec_verification(
            lot=None, 
            nouveau_statut=StatutLotExpediteurEnum.FERME, 
            user=user_libourne
        )
        
        # Vérifier que le résultat est None
        assert result is None
        
        # Vérifier que la session n'a pas été commit
        service.session.commit.assert_not_called()
    
    @pytest.mark.parametrize("option,valorisation,expected", [
        (
            OptionTraitementLotExpediteurEnum.LIVRAISON_DESTINATAIRES,
            {"livraison": {"cout_total": 42}},
            42
        ),
        (
            OptionTraitementLotExpediteurEnum.RECUPERATION_SUR_SITE,
            {"collecte": {"cout_ttc": 15.5}},
            15.5
        ),
        (
            OptionTraitementLotExpediteurEnum.RENVOI_EXPEDITEUR,
            {"expédition": {"cout_ttc": 99.99}},
            99.99
        ),
    ])
    def test_creer_paiements_selon_option(self, mock_session, option, valorisation, expected):
        lot = LotExpediteur(
            id=123,
            paiements=[],
            option_recouvrement=option,
            montant_ttc=None
        )

        # Patch la méthode de valorisation pour retourner notre dict de test
        with patch.object(ServiceLotExpediteur, "calcul_somme_valorisation", return_value=valorisation):
            service = ServiceLotExpediteur(mock_session)
            lot.paiements = []

            result = service.creer_paiements(lot)

            # Vérifie que le montant TTC du lot est correct
            assert result.montant_ttc == expected

            # Vérifie qu'un paiement a été ajouté à la session avec le bon montant
            paiement = None
            for call in mock_session.add.call_args_list:
                arg = call[0][0]
                if isinstance(arg, PaiementLotExpediteur):
                    paiement = arg
                    break
            assert paiement is not None
            assert paiement.montant_ttc == expected
            assert paiement.pourcentage == 100
            
            # Vérifie que la session a bien commit et refresh
            mock_session.commit.assert_called_once()
            mock_session.refresh.assert_called_once_with(lot)