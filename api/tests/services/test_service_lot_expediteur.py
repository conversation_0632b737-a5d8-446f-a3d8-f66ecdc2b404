import pytest
from datetime import datetime
from unittest.mock import MagicMock, patch
from sqlmodel import Session
from fastapi import HTTPException
from models.business import LotExpediteur, Site, PaiementLotExpediteur, C<PERSON>er, Enveloppe, Expediteur
from models.users import User
from services.lot_expediteur import ServiceLotExpediteur
from constants.enumerations import StatutLotExpediteurEnum, OptionTraitementLotExpediteurEnum, DestinationEnveloppeEnum, StatutEnveloppeEnum

class TestServiceLotExpediteur:
    
    @pytest.fixture
    def mock_session(self):
        return MagicMock()
    
    @pytest.fixture
    def service(self, mock_session):
        return ServiceLotExpediteur(mock_session)
    
    @pytest.fixture
    def site_libourne(self):
        site = Site(id=1, nom="LIBOURNE")
        return site
    
    @pytest.fixture
    def site_roissy(self):
        site = Site(id=2, nom="ROISSY")
        return site
    
    @pytest.fixture
    def user_libourne(self, site_libourne):
        user = User(id=1, username="user_libourne", site=site_libourne)
        return user
    
    @pytest.fixture
    def user_roissy(self, site_roissy):
        user = User(id=2, username="user_roissy", site=site_roissy)
        return user
    
    def test_changer_statut_lot_transition_autorisee_libourne(self, service, user_libourne):
        """Test le changement de statut avec une transition autorisée pour un utilisateur de Libourne"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=1, statut=StatutLotExpediteurEnum.FERME)
        
        # Appeler la méthode avec un nouveau statut autorisé pour Libourne
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.NOTIFIE, 
            user=user_libourne
        )
        
        # Vérifier que le statut a été changé
        assert result is not None
        assert result.statut == StatutLotExpediteurEnum.NOTIFIE
        assert result.user_modification_id == user_libourne.id
        assert result.updated_at is not None
        
        # Vérifier que la session a été commit
        service.session.commit.assert_called_once()
    
    def test_changer_statut_lot_transition_autorisee_roissy(self, service, user_roissy):
        """Test le changement de statut avec une transition autorisée pour un utilisateur de Roissy"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=2, statut=StatutLotExpediteurEnum.TRAITE_LIBERABLE)
        
        # Appeler la méthode avec un nouveau statut autorisé pour Roissy
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.TRAITE_OPERATIONNEL, 
            user=user_roissy
        )
        
        # Vérifier que le statut a été changé
        assert result is not None
        assert result.statut == StatutLotExpediteurEnum.TRAITE_OPERATIONNEL
        assert result.user_modification_id == user_roissy.id
        assert result.updated_at is not None
        
    
    def test_changer_statut_lot_transition_non_autorisee(self, service, user_roissy):
        """Test le changement de statut avec une transition non autorisée"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=3, statut=StatutLotExpediteurEnum.FERME)
        
        # Appeler la méthode avec un nouveau statut non autorisé pour Roissy
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.NOTIFIE, 
            user=user_roissy
        )
        
        # Vérifier que le résultat est None (transition non autorisée)
        assert result is None
        
        # Vérifier que le statut n'a pas été changé
        assert lot.statut == StatutLotExpediteurEnum.FERME
        
        # Vérifier que la session n'a pas été commit
        service.session.commit.assert_not_called()
    
    def test_changer_statut_lot_meme_statut(self, service, user_libourne):
        """Test le changement de statut avec le même statut"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=4, statut=StatutLotExpediteurEnum.FERME)
        
        # Appeler la méthode avec le même statut
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.FERME, 
            user=user_libourne
        )
        
        # Vérifier que le lot est retourné sans modification
        assert result is lot
        assert result.statut == StatutLotExpediteurEnum.FERME
        
        # Vérifier que la session n'a pas été commit
        service.session.commit.assert_not_called()
    
    def test_changer_statut_lot_transition_inexistante(self, service, user_libourne):
        """Test le changement de statut avec une transition qui n'existe pas dans la matrice"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=5, statut=StatutLotExpediteurEnum.OUVERT)
        
        # Appeler la méthode avec un nouveau statut qui n'est pas dans la matrice
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.TRAITE_OPERATIONNEL, 
            user=user_libourne
        )
        
        # Vérifier que le résultat est None (transition non autorisée)
        assert result is None
        
        # Vérifier que le statut n'a pas été changé
        assert lot.statut == StatutLotExpediteurEnum.OUVERT
        
        # Vérifier que la session n'a pas été commit
        service.session.commit.assert_not_called()
    
    def test_changer_statut_lot_liberation_casiers(self, service, user_roissy):
        """Test le changement de statut avec libération des casiers"""
        # Créer un lot avec un statut initial
        lot = LotExpediteur(id=6, statut=StatutLotExpediteurEnum.TRAITE_LIBERABLE)
        
        # Mock de la méthode liberer_casiers
        service.liberer_casiers = MagicMock()
        
        # Appeler la méthode avec un statut qui déclenche la libération des casiers
        result = service.changer_statut_lot_avec_verification(
            lot=lot, 
            nouveau_statut=StatutLotExpediteurEnum.TRAITE_OPERATIONNEL, 
            user=user_roissy
        )
        
        # Vérifier que le statut a été changé
        assert result is not None
        assert result.statut == StatutLotExpediteurEnum.TRAITE_OPERATIONNEL
        
        # Vérifier que la méthode liberer_casiers a été appelée
        service.liberer_casiers.assert_called_once_with(lot)
        
        # Vérifier que la session a été commit
        service.session.commit.assert_called_once()
    
    def test_changer_statut_lot_none(self, service, user_libourne):
        """Test le changement de statut avec un lot None"""
        # Appeler la méthode avec un lot None
        result = service.changer_statut_lot_avec_verification(
            lot=None, 
            nouveau_statut=StatutLotExpediteurEnum.FERME, 
            user=user_libourne
        )
        
        # Vérifier que le résultat est None
        assert result is None
        
        # Vérifier que la session n'a pas été commit
        service.session.commit.assert_not_called()
    
    @pytest.mark.parametrize("option,valorisation,expected", [
        (
            OptionTraitementLotExpediteurEnum.LIVRAISON_DESTINATAIRES,
            {"livraison": {"cout_total": 42}},
            42
        ),
        (
            OptionTraitementLotExpediteurEnum.RECUPERATION_SUR_SITE,
            {"collecte": {"cout_ttc": 15.5}},
            15.5
        ),
        (
            OptionTraitementLotExpediteurEnum.RENVOI_EXPEDITEUR,
            {"expédition": {"cout_ttc": 99.99}},
            99.99
        ),
    ])
    def test_creer_paiements_selon_option(self, mock_session, option, valorisation, expected):
        lot = LotExpediteur(
            id=123,
            paiements=[],
            option_recouvrement=option,
            montant_ttc=None
        )

        # Patch la méthode de valorisation pour retourner notre dict de test
        with patch.object(ServiceLotExpediteur, "calcul_somme_valorisation", return_value=valorisation):
            service = ServiceLotExpediteur(mock_session)
            lot.paiements = []

            result = service.creer_paiements(lot)

            # Vérifie que le montant TTC du lot est correct
            assert result.montant_ttc == expected

            # Vérifie qu'un paiement a été ajouté à la session avec le bon montant
            paiement = None
            for call in mock_session.add.call_args_list:
                arg = call[0][0]
                if isinstance(arg, PaiementLotExpediteur):
                    paiement = arg
                    break
            assert paiement is not None
            assert paiement.montant_ttc == expected
            assert paiement.pourcentage == 100
            
            # Vérifie que la session a bien commit et refresh
            mock_session.commit.assert_called_once()
            mock_session.refresh.assert_called_once_with(lot)

    def test_deplacer_plis_lot_vers_casier_succes(self, mock_session):
        """Test le déplacement réussi des plis d'un lot vers un casier"""
        # Créer les objets de test
        site_source = Site(id=1, nom="Site Source")
        site_destination = Site(id=2, nom="Site Destination")
        expediteur = Expediteur(id=1, nom="Test Expediteur")

        # Casiers
        casier_source1 = Casier(id=1, numero="C001", site_id=1, site=site_source, lot_expediteur_id=1)
        casier_source2 = Casier(id=2, numero="C002", site_id=1, site=site_source, lot_expediteur_id=1)
        casier_destination = Casier(id=3, numero="C003", site_id=2, site=site_destination, lot_expediteur_id=None)

        # Enveloppes dans différents casiers
        enveloppe1 = Enveloppe(id=1, casier_id=1, site_id=1, lot_expediteur_id=1)
        enveloppe2 = Enveloppe(id=2, casier_id=1, site_id=1, lot_expediteur_id=1)
        enveloppe3 = Enveloppe(id=3, casier_id=2, site_id=1, lot_expediteur_id=1)
        enveloppe4 = Enveloppe(id=4, casier_id=None, site_id=1, lot_expediteur_id=1)  # Pas dans un casier

        # Lot avec ses enveloppes et casiers
        lot = LotExpediteur(
            id=1,
            expediteur_id=1,
            expediteur=expediteur,
            casier_id=1,
            enveloppes=[enveloppe1, enveloppe2, enveloppe3, enveloppe4],
            casiers=[casier_source1, casier_source2]
        )

        # Configuration des mocks
        mock_session.query.return_value.options.return_value.filter.return_value.first.return_value = lot
        mock_session.query.return_value.filter.return_value.first.return_value = casier_destination
        mock_session.query.return_value.filter.return_value.scalar.return_value = 0  # Casiers vides après déplacement

        service = ServiceLotExpediteur(mock_session)

        # Exécuter la méthode
        result = service.deplacer_plis_lot_vers_casier(lot_id=1, casier_destination_id=3)

        # Vérifications
        assert result["nb_plis_deplaces"] == 3  # 3 enveloppes dans des casiers
        assert result["lot_expediteur"]["id"] == 1
        assert result["casier_destination"]["id"] == 3
        assert result["casier_destination"]["numero"] == "C003"
        assert result["nb_casiers_sources_liberes"] == 2

        # Vérifier que les enveloppes ont été déplacées
        assert enveloppe1.casier_id == 3
        assert enveloppe2.casier_id == 3
        assert enveloppe3.casier_id == 3
        assert enveloppe4.casier_id is None  # Reste inchangée

        # Vérifier que le site des enveloppes a été mis à jour
        assert enveloppe1.site_id == 2
        assert enveloppe2.site_id == 2
        assert enveloppe3.site_id == 2

        # Vérifier que le casier de destination est associé au lot
        assert casier_destination.lot_expediteur_id == 1
        assert casier_destination.date_attribution is not None

        # Vérifier que le casier principal du lot a été mis à jour
        assert lot.casier_id == 3

        # Vérifier que la session a été commit
        mock_session.commit.assert_called_once()

    def test_deplacer_plis_lot_vers_casier_lot_inexistant(self, mock_session):
        """Test avec un lot inexistant"""
        mock_session.query.return_value.options.return_value.filter.return_value.first.return_value = None

        service = ServiceLotExpediteur(mock_session)

        with pytest.raises(HTTPException) as exc_info:
            service.deplacer_plis_lot_vers_casier(lot_id=999, casier_destination_id=1)

        assert exc_info.value.status_code == 404
        assert "Lot expéditeur non trouvé" in str(exc_info.value.detail)

    def test_deplacer_plis_lot_vers_casier_casier_inexistant(self, mock_session):
        """Test avec un casier de destination inexistant"""
        lot = LotExpediteur(id=1, enveloppes=[], casiers=[])

        # Mock pour retourner le lot mais pas le casier
        def mock_query_side_effect(*args):
            mock_query = MagicMock()
            if args[0] == LotExpediteur:
                mock_query.options.return_value.filter.return_value.first.return_value = lot
            else:  # Casier
                mock_query.filter.return_value.first.return_value = None
            return mock_query

        mock_session.query.side_effect = mock_query_side_effect

        service = ServiceLotExpediteur(mock_session)

        with pytest.raises(HTTPException) as exc_info:
            service.deplacer_plis_lot_vers_casier(lot_id=1, casier_destination_id=999)

        assert exc_info.value.status_code == 404
        assert "Casier de destination non trouvé" in str(exc_info.value.detail)

    def test_deplacer_plis_lot_vers_casier_casier_occupe(self, mock_session):
        """Test avec un casier de destination déjà occupé par un autre lot"""
        lot = LotExpediteur(id=1, enveloppes=[], casiers=[])
        casier_destination = Casier(id=3, numero="C003", lot_expediteur_id=2)  # Occupé par lot 2

        def mock_query_side_effect(*args):
            mock_query = MagicMock()
            if args[0] == LotExpediteur:
                mock_query.options.return_value.filter.return_value.first.return_value = lot
            else:  # Casier
                mock_query.filter.return_value.first.return_value = casier_destination
            return mock_query

        mock_session.query.side_effect = mock_query_side_effect

        service = ServiceLotExpediteur(mock_session)

        with pytest.raises(HTTPException) as exc_info:
            service.deplacer_plis_lot_vers_casier(lot_id=1, casier_destination_id=3)

        assert exc_info.value.status_code == 400
        assert "déjà associé à un autre lot" in str(exc_info.value.detail)

    def test_deplacer_plis_lot_vers_casier_aucun_pli(self, mock_session):
        """Test avec un lot sans plis dans des casiers"""
        enveloppe_sans_casier = Enveloppe(id=1, casier_id=None, lot_expediteur_id=1)
        lot = LotExpediteur(id=1, enveloppes=[enveloppe_sans_casier], casiers=[])
        casier_destination = Casier(id=3, numero="C003", lot_expediteur_id=None)

        def mock_query_side_effect(*args):
            mock_query = MagicMock()
            if args[0] == LotExpediteur:
                mock_query.options.return_value.filter.return_value.first.return_value = lot
            else:  # Casier
                mock_query.filter.return_value.first.return_value = casier_destination
            return mock_query

        mock_session.query.side_effect = mock_query_side_effect

        service = ServiceLotExpediteur(mock_session)

        with pytest.raises(HTTPException) as exc_info:
            service.deplacer_plis_lot_vers_casier(lot_id=1, casier_destination_id=3)

        assert exc_info.value.status_code == 400
        assert "Aucun pli trouvé dans les casiers du lot" in str(exc_info.value.detail)