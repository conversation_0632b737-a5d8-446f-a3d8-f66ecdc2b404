import pytest
from datetime import datetime
from fastapi import HTTPException
from models.business import <PERSON><PERSON><PERSON>, Site, Enveloppe, Expediteur
from models.users import User
from services.casier import ServiceCasier
from constants.enumerations import DestinationEnveloppeEnum


class TestServiceCasier:

    @pytest.fixture
    def site(self, db):
        site = Site(nom="Site Test")
        db.add(site)
        db.commit()
        db.refresh(site)
        return site

    @pytest.fixture
    def site_destination(self, db):
        site = Site(nom="Site Destination")
        db.add(site)
        db.commit()
        db.refresh(site)
        return site

    @pytest.fixture
    def user(self, db, site):
        user = User(
            email="<EMAIL>",
            hashed_password="hashed",
            site_id=site.id
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        return user

    @pytest.fixture
    def expediteur(self, db):
        expediteur = Expediteur(
            nom="Test Expediteur",
            adresse="123 Rue Test",
            ville="Paris",
            code_postal="75001"
        )
        db.add(expediteur)
        db.commit()
        db.refresh(expediteur)
        return expediteur

    def test_obtenir_casier_disponible(self, db, site, user):
        """Test l'obtention d'un casier disponible"""

        # Créer un casier disponible
        casier = Casier(
            site_id=site.id,
            numero="TEST-1",
        )
        db.add(casier)
        db.commit()

        # Tester l'obtention du casier
        casier_obtenu = ServiceCasier.obtenir_casier_disponible(site)

        # Vérifier que le casier obtenu est bien celui créé
        assert casier_obtenu is not None
        assert casier_obtenu.id == casier.id
        assert casier_obtenu.numero == "TEST-1"
        assert casier_obtenu.date_attribution is not None

        # Ajouter des enveloppes au casier
        for i in range(3):
            enveloppe = Enveloppe(
                casier_id=casier.id,
                site_id=site.id,
                user_id=user.id,
                destination_enveloppe=DestinationEnveloppeEnum.METROPOLE
            )
            db.add(enveloppe)
        db.commit()

        # Tester à nouveau l'obtention du casier
        casier_obtenu_apres = ServiceCasier.obtenir_casier_disponible(site)

        # Vérifier qu'aucun casier n'est retourné car le casier existant a des enveloppes
        assert casier_obtenu_apres is None

        # On supprime les enveloppes
        for enveloppe in casier.enveloppes:
            enveloppe.casier = None
            db.add(enveloppe)
        db.commit()

        # Casier libre
        casier_obtenu_apres = ServiceCasier.obtenir_casier_disponible(site)
        assert casier_obtenu.id == casier.id

    def test_deplacer_plis_vers_autre_site_success(self, db, site, site_destination, user, expediteur):
        """Test le déplacement réussi de plis entre casiers de sites différents"""

        # Créer casier source avec des enveloppes
        casier_source = Casier(
            site_id=site.id,
            numero="SOURCE-1",
        )
        db.add(casier_source)

        # Créer casier destination
        casier_destination = Casier(
            site_id=site_destination.id,
            numero="DEST-1",
        )
        db.add(casier_destination)
        db.commit()

        # Créer des enveloppes dans le casier source
        enveloppes = []
        for i in range(3):
            enveloppe = Enveloppe(
                casier_id=casier_source.id,
                site_id=site.id,
                user_id=user.id,
                destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
                poids=100.0
            )
            enveloppes.append(enveloppe)
            db.add(enveloppe)
        db.commit()

        # Effectuer le déplacement
        resultat = ServiceCasier.deplacer_plis_vers_autre_site(
            session=db,
            casier_source_id=casier_source.id,
            casier_destination_id=casier_destination.id
        )

        # Vérifier le résultat
        assert resultat["nb_plis_deplaces"] == 3
        assert resultat["casier_source"]["id"] == casier_source.id
        assert resultat["casier_destination"]["id"] == casier_destination.id

        # Vérifier que les enveloppes ont été déplacées
        db.refresh(casier_source)
        db.refresh(casier_destination)

        for enveloppe in enveloppes:
            db.refresh(enveloppe)
            assert enveloppe.casier_id == casier_destination.id
            assert enveloppe.site_id == site_destination.id

    def test_deplacer_plis_casier_source_inexistant(self, db, site_destination):
        """Test avec un casier source inexistant"""

        casier_destination = Casier(
            site_id=site_destination.id,
            numero="DEST-1"
        )
        db.add(casier_destination)
        db.commit()

        with pytest.raises(HTTPException) as exc_info:
            ServiceCasier.deplacer_plis_vers_autre_site(
                session=db,
                casier_source_id=999,
                casier_destination_id=casier_destination.id
            )

        assert exc_info.value.status_code == 404
        assert "Casier source non trouvé" in str(exc_info.value.detail)

    def test_deplacer_plis_casier_destination_inexistant(self, db, site):
        """Test avec un casier destination inexistant"""

        casier_source = Casier(
            site_id=site.id,
            numero="SOURCE-1"
        )
        db.add(casier_source)
        db.commit()

        with pytest.raises(HTTPException) as exc_info:
            ServiceCasier.deplacer_plis_vers_autre_site(
                session=db,
                casier_source_id=casier_source.id,
                casier_destination_id=999
            )

        assert exc_info.value.status_code == 404
        assert "Casier destination non trouvé" in str(exc_info.value.detail)

    def test_deplacer_plis_meme_site(self, db, site):
        """Test avec des casiers sur le même site"""

        casier_source = Casier(
            site_id=site.id,
            numero="SOURCE-1"
        )
        casier_destination = Casier(
            site_id=site.id,
            numero="DEST-1"
        )
        db.add(casier_source)
        db.add(casier_destination)
        db.commit()

        with pytest.raises(HTTPException) as exc_info:
            ServiceCasier.deplacer_plis_vers_autre_site(
                session=db,
                casier_source_id=casier_source.id,
                casier_destination_id=casier_destination.id
            )

        assert exc_info.value.status_code == 400
        assert "Les casiers doivent être sur des sites différents" in str(exc_info.value.detail)

    def test_deplacer_plis_casier_source_vide(self, db, site, site_destination):
        """Test avec un casier source vide"""

        casier_source = Casier(
            site_id=site.id,
            numero="SOURCE-1"
        )
        casier_destination = Casier(
            site_id=site_destination.id,
            numero="DEST-1"
        )
        db.add(casier_source)
        db.add(casier_destination)
        db.commit()

        with pytest.raises(HTTPException) as exc_info:
            ServiceCasier.deplacer_plis_vers_autre_site(
                session=db,
                casier_source_id=casier_source.id,
                casier_destination_id=casier_destination.id
            )

        assert exc_info.value.status_code == 400
        assert "Aucun pli trouvé dans le casier source" in str(exc_info.value.detail)

    def test_deplacer_plis_capacite_insuffisante(self, db, site, site_destination, user):
        """Test avec une capacité insuffisante dans le casier destination"""

        casier_source = Casier(
            site_id=site.id,
            numero="SOURCE-1"
        )
        casier_destination = Casier(
            site_id=site_destination.id,
            numero="DEST-1"
        )
        db.add(casier_source)
        db.add(casier_destination)
        db.commit()

        # On remplit le casier destination avec une enveloppe
        enveloppe = Enveloppe(
            casier_id=casier_destination.id,
            site_id=site_destination.id,
            user_id=user.id,
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
            poids=100.0
        )
        db.add(enveloppe)
        db.commit()

        # Créer 3 enveloppes dans le casier source
        for i in range(3):
            enveloppe = Enveloppe(
                casier_id=casier_source.id,
                site_id=site.id,
                user_id=user.id,
                destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
                poids=100.0
            )
            db.add(enveloppe)
        db.commit()

        with pytest.raises(HTTPException) as exc_info:
            ServiceCasier.deplacer_plis_vers_autre_site(
                session=db,
                casier_source_id=casier_source.id,
                casier_destination_id=casier_destination.id
            )

        # assert exc_info.value.status_code == 400
        assert "Casier destination non trouvé ou non disponible" in str(exc_info.value.detail)