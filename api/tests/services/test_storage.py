# import pytest
# from services.storage import StorageService
# from core.config import settings
# import re

# def test_upload_file():
#     # Préparation des données de test
#     test_content = b"Contenu du fichier de test"
#     test_blob_path = "test/test_file.txt"
#     content_type = "text/plain"
    
#     # Appel de la fonction à tester
#     url = StorageService.upload_file(
#         file_contents=test_content,
#         blob_path=test_blob_path,
#         content_type=content_type
#     )
    
#     # Vérifications
#     assert url is not None
#     assert url.startswith(settings.AZURE_STORAGE_CONNECTION_URL.split('{blob_path}')[0])
    
#     uuid_pattern = r'test/test_file_[0-9a-f]{8}\.txt'
#     assert re.search(uuid_pattern, url), f"L'URL {url} ne contient pas le format attendu test_file_{{uuid}}.txt"


#     from models.business import PhotoEnveloppe
#     photo = PhotoEnveloppe(url=url)
#     assert photo.public_url is not None

    
#     # Public_url should be getted from the url
#     import requests
#     response = requests.get(photo.public_url)
#     assert response.status_code == 200
#     assert response.content == test_content

#     # Clean and test
#     from azure.core.exceptions import HttpResponseError
#     with pytest.raises(HttpResponseError) as excinfo:
#         StorageService.delete_file(photo.public_url)
#     assert "not authorized" in str(excinfo.value).lower()

#     StorageService.delete_file(url)