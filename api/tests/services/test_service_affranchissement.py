from services.affranchissement import ServiceAffranchissement
from constants.enumerations import NatureAffranchissementEnum, TypeAffranchissementEnum, CategorieAffranchissementEnum, DeviseEnum, SousTypeAffranchissementEnum, ValiditeAffranchissementEnum
from models.business import Affranchissement
from models.public import ModeleAffranchissement
import pytest
from tests.fixtures.codes import CODE_SDN_87
from services.affranchissements.commun import ServiceAffranchissementCommun
from services.validateurs.validateur_affranchissement import ValidateurAffranchissement

class TestServiceAffranchissement:

    def test_identifier_type_affranchissement_valid_code(self):
        test_cases = [
            ("LL014699171FR", TypeAffranchissementEnum.S10, SousTypeAffranchissementEnum.NON_DETERMINE),
            ("ABCD1234", TypeAffranchissementEnum.TNUM, SousTypeAffranchissementEnum.NON_DETERMINE),
        ]

        for test_case in test_cases:
            code = test_case[0]
            expected_type = test_case[1]
            expected_sous_type = test_case[2]

            result = ServiceAffranchissement.identifier_type_affranchissement(code)
            assert result is not None, f"{code} => None"
            assert result['type'] == expected_type, f"Failed for type {code} {result['type']}"
            assert result['sous_type'] == expected_sous_type, f"Failed for sous_type {code} {result['sous_type']}"

            aff = Affranchissement(
                code=code,
                type=result['type'],
                sous_type=result['sous_type']
            )

            assert aff.donnees is not None, f"Failed for donnees {code} {aff.donnees}"

            # Vérifier les valeurs attendues dans les données extraites si spécifiées
            if len(test_case) > 3:
                expected_data = test_case[3]
                for key, value in expected_data.items():
                    if isinstance(value, dict):
                        # Pour les dictionnaires imbriqués
                        for sub_key, sub_value in value.items():
                            assert aff.donnees[key][sub_key] == sub_value, f"Failed for {code}: expected {key}.{sub_key}={sub_value}, got {aff.donnees[key][sub_key]}"
                    else:
                        # Pour les valeurs simples
                        assert aff.donnees[key] == value, f"Failed for {code}: expected {key}={value}, got {aff.donnees[key]}"
            
    def test_identifier_type_affranchissement_invalid_code(self):
        code = "INVALIDCODE"
        result = ServiceAffranchissement.identifier_type_affranchissement(code)
        assert result is None

    def test_get_modele_affranchissement_valid_data(self):
        data = {
            'categorie': CategorieAffranchissementEnum.CODE,
            'type': TypeAffranchissementEnum.SD,
            'devise': DeviseEnum.EURO,
            'prix_unite_devise': 10.0,
            'origine': '86'
        }
        result = ServiceAffranchissement.get_modele_affranchissement(data)
        assert isinstance(result, ModeleAffranchissement)
        assert result.dict() == {
            'afficher': True,
            'categorie': CategorieAffranchissementEnum.CODE,
            'type': TypeAffranchissementEnum.SD,
            'origine': '86',
            'devise': DeviseEnum.EURO,
            'prix_unite_devise': 1.26,
            'color': 'xtra_envelop_element_code',
            'code_produit': None,
            'variant': 'outlined',
            'valorisation_complexe': False,
            'informations': {
                'champs_requis': [],
                'complet': True,
                'label': 'SD86'
            }
        }

    def test_get_modele_affranchissement_unvalorisable_data(self):
        data = {
            'categorie': CategorieAffranchissementEnum.CODE,
            'type': TypeAffranchissementEnum.S10,
            'devise': DeviseEnum.EURO,

        }
        result = ServiceAffranchissement.get_modele_affranchissement(data)
        assert isinstance(result, ModeleAffranchissement)
        assert result.dict() == {
            'afficher': False,
            'categorie': CategorieAffranchissementEnum.CODE,
            'type': TypeAffranchissementEnum.S10,
            'devise': DeviseEnum.EURO,
            'prix_unite_devise': None,
            'valorisation_complexe': True,
            'color': 'xtra_envelop_element_code',
            'code_produit': None,
            'variant': 'outlined',
            'origine': None,
            'informations': {
                'champs_requis': ['nature'],
                'complet': False,
                'label': 'S10'
            }
        }

    def test_create_affranchissement_valid_data(self):
        data = {
            'code': "SD86123456789125A",
            'nature': NatureAffranchissementEnum.NON_DETERMINE,
            'categorie': CategorieAffranchissementEnum.CODE,
            'type': TypeAffranchissementEnum.SD,            
            'devise': DeviseEnum.EURO,
            'prix_unite_devise': 10.0
        }
        affranchissement = ServiceAffranchissement.create_affranchissement(data)
        assert isinstance(affranchissement, Affranchissement)
        assert affranchissement.type == TypeAffranchissementEnum.SD
        assert affranchissement.categorie == CategorieAffranchissementEnum.CODE
        assert affranchissement.origine == "86"
        

    def test_create_affranchissement_invalid_code(self):
        data = {
            'code': "INVALIDCODE",
            'nature': NatureAffranchissementEnum.NON_DETERMINE
        }
        with pytest.raises(ValueError):
            ServiceAffranchissement.create_affranchissement(data)

    def test_create_affranchissement_valorisable_1(self):
        data = {
            "categorie": "MARI",
            "type": "VAL",
            "devise": "EURO",
            "prix_unite_devise": 1.26
        }
        affranchissement = ServiceAffranchissement.create_affranchissement(data)
    
    def test_create_affranchissement_sd87_nature_obligatoire(self):
        affranchissement = ServiceAffranchissement.create_affranchissement(
            {
                "code": "87000763985319W"
            }
        )

        from models.public import AffranchissementItemPublic
        assert affranchissement.informations.champs_requis == ["nature"]
        assert affranchissement.informations.champs_manquants == ["nature"]


    def test_get_modele_affranchissement_sd87_2(self):
        data = {
            "categorie": "CODE",
            "type": "SD",
            "origine": "87"
        }
        result = ServiceAffranchissement.get_modele_affranchissement(data)
        assert result is not None
        assert result.type == TypeAffranchissementEnum.SD
        assert result.origine == "87"

    def test_champs_manquants_sd87(self):
        affranchissement = ServiceAffranchissement.create_affranchissement(
            {
                "code": "87000763985319W"
            }
        )
        assert affranchissement.informations.champs_manquants == ["nature"]

    from constants.affranchissements.affranchissement import CATEGORIES_AFFRANCHISSEMENTS

    MODELE1 = ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.CODE,
                type=TypeAffranchissementEnum.SD,
                color="xtra_envelop_element_code",
                variant="outlined",
                devise=DeviseEnum.EURO,
                prix_unite_devise=None,
            )

    def test_champs_manquants_avec_champs_manquants(self, db, user):
        affranchissement = Affranchissement(
                categorie=CategorieAffranchissementEnum.CODE,
                type=TypeAffranchissementEnum.SD,
                devise=DeviseEnum.EURO,
                user_id=user.id,
                origine="IND"
            )            
        db.add(affranchissement)
        db.commit()
        service = ServiceAffranchissementCommun(affranchissement)
        champs_manquants = service.champs_manquants()
        assert champs_manquants == ["nature"], "Les champs manquants devraient être 'nature' et 'quantite'."

    def test_update_affranchissement_avec_nature_non_determine(self, db, user):
        """
        Test que l'utilisateur peut saisir une nature NON_DETERMINE et que le prix reste modifiable
        """
        # Créer un affranchissement avec nature requise
        affranchissement = ServiceAffranchissement.create_affranchissement(
            {
                "code": "87000763985319W"
            }
        )
        affranchissement.user_id = user.id

        # Mettre à jour avec nature NON_DETERMINE
        affranchissement.service.update({
            "nature": NatureAffranchissementEnum.NON_DETERMINE,
            "statut": ValiditeAffranchissementEnum.INVALIDE
        })

        # Vérifier que la nature est bien NON_DETERMINE
        assert affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE
        # Vérifier que le prix est à 0 pour un affranchissement invalide
        assert affranchissement.prix_unite_devise == 0
        assert affranchissement.devise == DeviseEnum.EURO

        # Maintenant, l'utilisateur peut changer la nature vers une vraie valeur
        affranchissement.service.update({
            "nature": NatureAffranchissementEnum.LV_20,
            "statut": ValiditeAffranchissementEnum.VALIDE
        })

        # Vérifier que le prix a été calculé automatiquement
        assert affranchissement.nature == NatureAffranchissementEnum.LV_20
        assert affranchissement.prix_unite_devise is not None
        assert affranchissement.prix_unite_devise > 0

    def test_update_affranchissement_garde_nature_non_determine_si_valide(self, db, user):
        """
        Test que si l'utilisateur sélectionne NON_DETERMINE sur un affranchissement valide,
        le prix existant est conservé
        """
        # Créer un affranchissement avec nature requise
        affranchissement = ServiceAffranchissement.create_affranchissement(
            {
                "code": "87000763985319W"
            }
        )
        affranchissement.user_id = user.id

        # D'abord, définir une nature valide
        affranchissement.service.update({
            "nature": NatureAffranchissementEnum.LV_20,
            "statut": ValiditeAffranchissementEnum.VALIDE
        })

        prix_initial = affranchissement.prix_unite_devise
        assert prix_initial is not None and prix_initial > 0

        # Maintenant, changer vers NON_DETERMINE mais garder le statut valide
        affranchissement.service.update({
            "nature": NatureAffranchissementEnum.NON_DETERMINE,
            "statut": ValiditeAffranchissementEnum.VALIDE,
            "prix_unite_devise": prix_initial
        })

        # Vérifier que la nature est NON_DETERMINE mais le prix est conservé
        assert affranchissement.nature == NatureAffranchissementEnum.NON_DETERMINE
        # Le prix devrait être conservé car l'affranchissement est valide
        assert affranchissement.prix_unite_devise == prix_initial

