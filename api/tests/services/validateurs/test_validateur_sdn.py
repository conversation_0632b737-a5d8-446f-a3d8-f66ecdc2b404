import pytest
from models import TypeVerificationAffranchissementEnum, StatutVerificationEnum
from services.validateurs.types.validateur_sdn import ValidateurSDN
from unittest.mock import MagicMock
from models.business import Affranchissement
from constants.enumerations import CategorieAffranchissementEnum, TypeAffranchissementEnum, SousTypeAffranchissementEnum
from tests.fixtures.codes import CODE_SDN_87

class TestValidateurSDN:

    @pytest.fixture(autouse=True)
    def setup(self):
        self.validateur = ValidateurSDN(
            Affranchissement(
                code=CODE_SDN_87,
                categorie=CategorieAffranchissementEnum.CODE,
                type=TypeAffranchissementEnum.SD,
                sous_type=SousTypeAffranchissementEnum.SDN,
            )
        )

    
    def test_verification_signature(self):
        self.validateur.verification_signature()
        
        # Test avec différents codes et clés
        codes_test = [
            (CODE_SDN_87, True),  # Code valide
            ("870009274928485", False),
            ("SD"+CODE_SDN_87, True)
        ]

        for code, attendu in codes_test:
            self.validateur.affranchissement.code = code
            self.validateur.verification_signature()
            
            verification = self.validateur.affranchissement.verifications[-1]
            assert verification.statut == (StatutVerificationEnum.VALIDE if attendu else StatutVerificationEnum.INVALIDE), "Problème clé: "

    def test_verification_signature_invalide_donnees(self):
        self.validateur.affranchissement.code = "870009274928485"
        self.validateur.verification_signature()
        
        assert len(self.validateur.affranchissement.verifications) == 1
        assert self.validateur.affranchissement.verifications[0].type == TypeVerificationAffranchissementEnum.GRAMMAIRE_CLE
        assert self.validateur.affranchissement.verifications[0].statut == StatutVerificationEnum.INVALIDE
        assert self.validateur.affranchissement.verifications[0].donnees == {"cle_calculee": "S", "cle_code": "5"}
        