import pytest
from models import TypeVerificationAffranchissementEnum, StatutVerificationEnum
from services.validateurs.types.validateur_sdx import ValidateurSDX
from unittest.mock import MagicMock
from models.business import Affranchissement
from constants.enumerations import CategorieAffranchissementEnum, TypeAffranchissementEnum, SousTypeAffranchissementEnum, DeviseEnum   
from tests.fixtures.codes import CODE_SDX_87


class TestValidateurSDX:

    @pytest.fixture(autouse=True)
    def setup(self):
        self.validateur = ValidateurSDX(
            Affranchissement(
                code=CODE_SDX_87,
                categorie=CategorieAffranchissementEnum.CODE,
                type=TypeAffranchissementEnum.SD,
                sous_type=SousTypeAffranchissementEnum.SDX,
                prix_unite_devise=1.0,
                devise=DeviseEnum.EURO,
            )
        )

    def test_verification_signature(self):        
        self.validateur.verification_signature()
        
        assert len(self.validateur.affranchissement.verifications) == 1
        assert self.validateur.affranchissement.verifications[0].type == TypeVerificationAffranchissementEnum.GRAMMAIRE_SIGNATURE
        assert self.validateur.affranchissement.verifications[0].statut == StatutVerificationEnum.VALIDE
        