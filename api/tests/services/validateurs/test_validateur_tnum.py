import pytest
from models import TypeVerificationAffranchissementEnum, StatutVerificationEnum, Affranchissement
from services.validateurs.types.validateur_tnum import ValidateurTNUM
from tests.conftest import client as _client
from sqlmodel import Session, select
from tests.fixtures.affranchissements import TNUM

class TestValidateurTNUM:

    @pytest.fixture(autouse=True)
    def setup(self):
        self.processor = ValidateurTNUM(TNUM())

    def test_verification_grammaire_unknown(self):
        
        self.processor.verification_grammaire()
        
        # Vérifiez les résultats attendus
        assert self.processor.affranchissement.verifications[0].type == TypeVerificationAffranchissementEnum.GRAMMAIRE
        assert self.processor.affranchissement.verifications[0].statut == StatutVerificationEnum.NON_DETERMINE
        assert self.processor.affranchissement.verifications[0].message == "Non implémenté"
