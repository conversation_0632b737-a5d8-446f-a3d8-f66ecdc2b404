# import pytest
# from services.migration import ServiceMigration
# from models import Site, Expediteur
# from constants.enumerations import SourceEnveloppeEnum, CategorieAffranchissementEnum, TypeAffranchissementEnum, TypeVerificationAffranchissementEnum, StatutVerificationEnum, DeviseEnum


# class TestMigration:

#     def test_create_enveloppe(self, db):

#         db.add(Site(nom="WISSOUS"))
#         db.commit()

#         db.add(Expediteur(nom="MONOCAZ",
#                           id_migration="27GDJJMBSo0BLn0HoSE0",
#                            adresse="10 AVENUE AMPERE 78180 MONTIGNY LE BRETONNEUX", 
#                            siret="80472057100012"))

#         env_source = {
#             '_index': '&myapp.xtra_coll_control_main3',
#             '_id': 'ef3330737af3f5be3acd',
#             '_score': None,
#             '_source': {
#                 '@timestamp': '2024-10-24T13:45:47.236Z',
#                 'datetimeParis': '2024-10-24 15:45:47',
#                 'sender': {
#                     'name': 'MONOCAZ',
#                     'address': '10 AVENUE AMPERE 78180 MONTIGNY LE BRETONNEUX',
#                     'coclico': None,
#                     'siret': '80472057100012'
#                 },
#                 'destination': None,
#                 'main': {
#                     'idSender': '27GDJJMBSo0BLn0HoSE0',
#                     'idDestination': None,
#                     'idProduct': 'LINT',
#                     'weight': 103,
#                     'overweight': False,
#                     'outofsize': False,
#                     'elements': [
#                         {
#                             'idCategory': 'CODE',
#                             'idMention': 'S10',
#                             'color': 'xtra_envelop_element_code',
#                             'variant': 'outlined',
#                             'quantity': 1,
#                             'valuable': False,
#                             'unit': 'E',
#                             'valueEuro': 0,
#                             'fake': True,
#                             'idNature': None,
#                             'code': {
#                                 'raw': 'LV200537055FR',
#                                 'id': 'LV200537055FR',
#                                 'segmented': 'LV 20053705 5 FR',
#                                 'format': 'S10',
#                                 'content': {
#                                     'sd': None,
#                                     'tnum': None,
#                                     's10': {
#                                         'service': 'LV',
#                                         'number': '20053705',
#                                         'key': '5',
#                                         'country': 'FR'
#                                     }
#                                 },
#                                 'check': {
#                                     'grammar': {
#                                         'result': {
#                                             'status': 'failed',
#                                             'details': [
#                                                 'service doit être différent de LV,LY : KO',
#                                                 'key doit être égal à 5 : OK',
#                                                 'country doit être égal à FR : OK'
#                                             ]
#                                         }
#                                     },
#                                     'count': {
#                                         'data': {'count': 0},
#                                         'result': {
#                                             'status': 'success',
#                                             'details': ['Doit être scanné pour la 1ère fois : OK']
#                                         }
#                                     }
#                                 },
#                                 'fake': True,
#                                 'idControlCode': None,
#                                 'valueEuro': 2.8
#                             },
#                             'valueUnit': 0,
#                             'index': 0
#                         },
#                         {
#                             'idCategory': 'CODE',
#                             'idMention': 'SD87',
#                             'color': 'xtra_envelop_element_code',
#                             'variant': 'outlined',
#                             'quantity': 1,
#                             'valuable': False,
#                             'unit': 'E',
#                             'valueEuro': 4.03,
#                             'fake': False,
#                             'idNature': 'LINT_100',
#                             'code': {
#                                 'raw': '%000000087000915076713377276A10^1078b1c',
#                                 'id': '87000915076713',
#                                 'segmented': '87 000915076713',
#                                 'format': 'SD',
#                                 'content': {
#                                     'sd': {
#                                         'cp': '0000000',
#                                         'origin': '87',
#                                         'number': '000915076713',
#                                         'key': '7',
#                                         'socode': '377',
#                                         'country': '276',
#                                         'ascode': 'A10',
#                                         'customer': '1078B1C'
#                                     },
#                                     'tnum': None,
#                                     's10': None
#                                 },
#                                 'check': {
#                                     'grammar': {
#                                         'result': {
#                                             'status': 'success',
#                                             'details': ['origin doit appartenir à [86,87,88] : OK']
#                                         }
#                                     },
#                                     'count': {
#                                         'data': {'count': 0},
#                                         'result': {
#                                             'status': 'success',
#                                             'details': ['Doit être scanné pour la 1ère fois : OK']
#                                         }
#                                     }
#                                 },
#                                 'fake': False,
#                                 'idControlCode': None,
#                                 'valueEuro': 0
#                             },
#                             'valueUnit': 4.03,
#                             'index': 1
#                         }
#                     ],
#                     'idSite': 'WISSOUS'
#                 },
#                 '_kuzzle_info': {
#                     'author': None,
#                     'createdAt': 1733565020247,
#                     'updatedAt': 1733565020247,
#                     'updater': None
#                 },
#                 'valo': {
#                     'postage': {
#                         'postageDue': 12.350000000000001,
#                         'postageObserved': 4.03,
#                         'postageToRecover': 8.32,
#                         'postageFakeCount': 1,
#                         'postageFakeOne': True,
#                         'postageTaxOne': True
#                     },
#                     'deliver': {
#                         'postageToRecover': 8.32,
#                         'postageFakeOne': True,
#                         'deliverTaxCoeff': 0.1,
#                         'deliverTaxToRecover': 0.8320000000000001,
#                         'deliverTaxSet': 3,
#                         'deliverTaxTotal': 3.832,
#                         'deliverTotalToRecover': 12.152000000000001
#                     },
#                     'collect': {
#                         'collectFeesHT': 4.68,
#                         'collectTVACoeff': 0.2,
#                         'collectTotalToRecoverTVA': 0.9359999999999999,
#                         'collectTotalToRecoverHT': 4.68,
#                         'collectTotalToRecoverTTC': 5.616
#                     },
#                     'shipping': {
#                         'collectTotalToRecoverHT': 4.68,
#                         'collectTotalToRecoverTVA': 0.9359999999999999,
#                         'collectTotalToRecoverTTC': 5.616,
#                         'shippingPostageDue': 4.3,
#                         'shippingTotalToRecoverHT': 8.98,
#                         'shippingTotalToRecoverTVA': 0.9359999999999999,
#                         'shippingTotalToRecoverTTC': 9.916
#                     }
#                 }
#             },
#             'sort': [1729777547236]
#         }
#         env = ServiceMigration.create_enveloppe(env_source, session=db)
#         assert env.source == SourceEnveloppeEnum.IMPORT

#         assert len(env.affranchissements) == 2

#         assert env.affranchissements[0].categorie == CategorieAffranchissementEnum.CODE
#         assert env.affranchissements[0].type == TypeAffranchissementEnum.S10
#         assert env.affranchissements[1].categorie == CategorieAffranchissementEnum.CODE
#         assert env.affranchissements[1].type == TypeAffranchissementEnum.SD
#         assert env.affranchissements[1].origine == "87"

#         # Tests les verifications effectuées
#         # assert len(env.affranchissements[0].verifications) == 6
        
#         # Vérification que les checks de la source sont bien convertis en vérifications
#         # Pour l'affranchissement S10 (premier affranchissement)
#         aff_s10 = env.affranchissements[0]

#         assert len(aff_s10.verifications) == 4
        
#         # Vérification des checks de grammaire
#         grammar_verifs = [v for v in aff_s10.verifications if v.type in [
#             TypeVerificationAffranchissementEnum.GRAMMAIRE_SERVICE,
#             TypeVerificationAffranchissementEnum.GRAMMAIRE_CLE,
#             TypeVerificationAffranchissementEnum.GRAMMAIRE_COUNTRY
#         ]]
#         assert len(grammar_verifs) == 3
        
#         # Vérification spécifique du service (qui doit être invalide)
#         service_verif = next(v for v in grammar_verifs if v.type == TypeVerificationAffranchissementEnum.GRAMMAIRE_SERVICE)
#         assert service_verif.statut == StatutVerificationEnum.INVALIDE
#         assert "service doit être différent de LV,LY : KO" in service_verif.message
        
#         # Vérification de la clé (qui doit être valide)
#         key_verif = next(v for v in grammar_verifs if v.type == TypeVerificationAffranchissementEnum.GRAMMAIRE_CLE)
#         assert key_verif.statut == StatutVerificationEnum.VALIDE
#         assert "key doit être égal à 5 : OK" in key_verif.message
        
#         # Vérification du pays (qui doit être valide)
#         country_verif = next(v for v in grammar_verifs if v.type == TypeVerificationAffranchissementEnum.GRAMMAIRE_COUNTRY)
#         assert country_verif.statut == StatutVerificationEnum.VALIDE
#         assert "country doit être égal à FR : OK" in country_verif.message
        
#         # Vérification du count
#         count_verifs = [v for v in aff_s10.verifications if v.type == TypeVerificationAffranchissementEnum.COUNT]
#         assert len(count_verifs) == 1, "aie"
#         assert count_verifs[0].statut == StatutVerificationEnum.VALIDE
#         assert "Doit être scanné pour la 1ère fois : OK" in count_verifs[0].message
#         assert count_verifs[0].donnees == {'count': 0}

#         # Pour l'affranchissement SD87 (deuxième affranchissement)
#         aff_sd = env.affranchissements[1]
        
#         assert len(aff_sd.verifications) == 2
        
#         # Vérification des checks de grammaire
#         key_verif = next(v for v in aff_sd.verifications if v.type == TypeVerificationAffranchissementEnum.GRAMMAIRE_ORIGIN)
#         assert key_verif.statut == StatutVerificationEnum.VALIDE
#         assert "origin doit appartenir à [86,87,88] : OK" in key_verif.message

#          # Vérification du count pour SD87
#         sd_count_verifs = [v for v in aff_sd.verifications if v.type == TypeVerificationAffranchissementEnum.COUNT]
#         assert len(sd_count_verifs) == 1
#         assert sd_count_verifs[0].statut == StatutVerificationEnum.VALIDE
#         assert "Doit être scanné pour la 1ère fois : OK" in sd_count_verifs[0].message
#         assert sd_count_verifs[0].donnees == {'count': 0}


#         # Verification de la valorisation d'origine
#         assert env.valorisation == {
#             'postage': {
#                 'cout_enveloppe': 12.350000000000001,
#                 'cout_affranchissements_valide': 4.03,
#                 'nb_affranchissements_invalides': 1,
#                 'montant_sous_affranchissement': 8.32,
#                 'presence_affranchissements_invalide': True,
#                 'presence_taxe': True,
#                 'coeff_taxe_livraison': 0.1,
#             },
#             'livraison': {
#                 'taxe_livraison_a_recuperer': 0.8320000000000001,
#                 'taxe_livraison_fixe': 3,
#                 'taxe_livraison_totale': 3.832,
#                 'cout_total': 12.152000000000001
#             },
#             'collecte': {
#                 'frais_collecte_ht': 4.68,
#                 'coeff_collecte_tva': 0.2,
#                 'cout_ht': 4.68,
#                 'cout_tva': 0.9359999999999999,
#                 'cout_ttc': 5.616
#             },
#             'expédition': {
#                 'frais_expédition': 4.3,
#                 'cout_ht': 8.98,
#                 'cout_tva': 0.9359999999999999,
#                 'cout_ttc': 9.916
#             }
#         }


#     def test_create_enveloppe_2(self, db):
#         db.add(Site(nom="WISSOUS"))
#         db.commit()

#         db.add(Expediteur(nom="RESINET SARL",
#                           id_migration="9rGDJJMBSo0BLn0HoiH8",
#                           adresse="6 RUE DES JUIFS 68150 RIHBEAUVILLE", 
#                           siret="80417741800022"))
#         db.commit()

#         source = {
#             '_index': '&myapp.xtra_coll_control_main3',
#             '_id': '9542808d6b1769ffea05',
#             '_score': 1.0,
#             '_source': {
#                 '@timestamp': '2025-02-04T16:30:59.189Z',
#                 'datetimeParis': '2025-02-04 17:30:59',
#                 'sender': {
#                     'name': 'RESINET SARL',
#                     'address': '6 RUE DES JUIFS 68150 RIHBEAUVILLE',
#                     'coclico': None,
#                     'siret': '80417741800022'
#                 },
#                 'destination': None,
#                 'main': {
#                     'idSite': 'WISSOUS',
#                     'idProduct': 'LINT',
#                     'idSender': '9rGDJJMBSo0BLn0HoiH8',
#                     'idDestination': None,
#                     'weight': 190,
#                     'overweight': False,
#                     'outofsize': False,
#                     'elements': [
#                         {
#                             'index': 0,
#                             'idCategory': 'CODE',
#                             'idMention': 'S10',
#                             'idNature': None,
#                             'color': 'xtra_envelop_element_code',
#                             'variant': 'outlined',
#                             'quantity': 1,
#                             'valuable': False,
#                             'unit': 'E',
#                             'valueUnit': 0,
#                             'valueEuro': 0,
#                             'fake': True,
#                             'code': {
#                                 'raw': 'LV281793215FR',
#                                 'id': 'LV281793215FR',
#                                 'segmented': 'LV 28179321 5 FR',
#                                 'format': 'S10',
#                                 'content': {
#                                     'sd': None,
#                                     'tnum': None,
#                                     's10': {
#                                         'service': 'LV',
#                                         'number': '28179321',
#                                         'key': '5',
#                                         'country': 'FR'
#                                     }
#                                 },
#                                 'check': {
#                                     'grammar': {
#                                         'result': {
#                                             'status': 'failed',
#                                             'details': [
#                                                 'service différent de LV,LY : KO',
#                                                 'key égal à 5 : OK',
#                                                 'country égal à FR : OK'
#                                             ]
#                                         }
#                                     },
#                                     'count': {
#                                         'data': {'count': 0},
#                                         'result': {
#                                             'status': 'success',
#                                             'details': ['ID jamais scanné : OK']
#                                         }
#                                     },
#                                     'ssu': {
#                                         'data': {
#                                             'tracking': {'dates': ['2024-06-26']},
#                                             'purchase': {
#                                                 'idCommand': None,
#                                                 'date': None,
#                                                 'price': None,
#                                                 'weight': None,
#                                                 'rawPrice': None,
#                                                 'offerCode': None
#                                             }
#                                         },
#                                         'result': {
#                                             'status': 'undetermined',
#                                             'details': [
#                                                 'Evénements sur 1 jour maxi  : OK (2024-06-26)',
#                                                 'Achat trouvé : IND (ne concerne que SD87/SD88)'
#                                             ]
#                                         }
#                                     }
#                                 },
#                                 'valueEuro': 2.8,
#                                 'fake': True,
#                                 'idControlCode': None
#                             }
#                         },
#                         {
#                             'index': 1,
#                             'idCategory': 'VIGN',
#                             'idMention': 'AUTO',
#                             'idNature': None,
#                             'color': 'xtra_envelop_element_vign',
#                             'variant': 'outlined',
#                             'quantity': 1,
#                             'valuable': True,
#                             'unit': 'E',
#                             'valueUnit': 0.63,
#                             'valueEuro': 0.63,
#                             'fake': False,
#                             'code': None
#                         }
#                     ]
#                 },
#                 'valo': {
#                     'postage': {
#                         'postageDue': 12.649999999999999,
#                         'postageObserved': 0.63,
#                         'postageToRecover': 12.019999999999998,
#                         'postageFakeCount': 1,
#                         'postageFakeOne': True,
#                         'postageTaxOne': True
#                     },
#                     'deliver': {
#                         'postageToRecover': 12.019999999999998,
#                         'postageFakeOne': True,
#                         'deliverTaxCoeff': 0.1,
#                         'deliverTaxToRecover': 1.202,
#                         'deliverTaxSet': 3,
#                         'deliverTaxTotal': 4.202,
#                         'deliverTotalToRecover': 16.221999999999998
#                     },
#                     'collect': {
#                         'collectFeesHT': 4.68,
#                         'collectTVACoeff': 0.2,
#                         'collectTotalToRecoverTVA': 0.9359999999999999,
#                         'collectTotalToRecoverHT': 4.68,
#                         'collectTotalToRecoverTTC': 5.616
#                     },
#                     'shipping': {
#                         'collectTotalToRecoverHT': 4.68,
#                         'collectTotalToRecoverTVA': 0.9359999999999999,
#                         'collectTotalToRecoverTTC': 5.616,
#                         'shippingPostageDue': 4.3,
#                         'shippingTotalToRecoverHT': 8.98,
#                         'shippingTotalToRecoverTVA': 0.9359999999999999,
#                         'shippingTotalToRecoverTTC': 9.916
#                     }
#                 },
#                 '_kuzzle_info': {
#                     'author': None,
#                     'createdAt': 1738686659193,
#                     'updatedAt': 1738686659193,
#                     'updater': None
#                 }
#             }
#         }
        
#         env = ServiceMigration.create_enveloppe(source, session=db)
        
#         # Vérification des données de base
#         assert env.poids == 190
#         assert env.surpoids == False
#         assert env.surdimensionne == False
#         assert env.site.nom == "WISSOUS"
#         assert env.expediteur.nom == "RESINET SARL"
#         assert env.expediteur.siret == "80417741800022"
        
#         # Vérification des affranchissements
#         assert len(env.affranchissements) == 2
        
#         # Premier affranchissement (S10)
#         aff_s10 = next(a for a in env.affranchissements if a.type == TypeAffranchissementEnum.S10)
#         assert aff_s10.categorie == CategorieAffranchissementEnum.CODE
#         assert aff_s10.devise == DeviseEnum.EURO
#         assert aff_s10.prix_unite_devise == 2.8
#         assert aff_s10.prix_unite_euros == 0
#         assert aff_s10.quantite == 1
#         assert aff_s10.code == "LV281793215FR"
#         assert aff_s10.statut == StatutVerificationEnum.INVALIDE
        
#         # Deuxième affranchissement (VIGNETTE)
#         aff_vign = next(a for a in env.affranchissements if a.type == TypeAffranchissementEnum.AUTO)
#         assert aff_vign.categorie == CategorieAffranchissementEnum.VIGN
#         assert aff_vign.prix_unite_devise == 0.63
#         assert aff_vign.devise == DeviseEnum.EURO
#         assert aff_vign.prix_unite_euros == 0.63
#         assert aff_vign.statut == StatutVerificationEnum.VALIDE
        
#         # Vérification des vérifications pour S10
#         for v in aff_s10.verifications:
#             print(v.type)
#         assert len(aff_s10.verifications) == 6
        
        
#         # Vérifications de grammaire pour S10
#         grammar_verifs = [v for v in aff_s10.verifications if v.type in [
#             TypeVerificationAffranchissementEnum.GRAMMAIRE_SERVICE,
#             TypeVerificationAffranchissementEnum.GRAMMAIRE_CLE,
#             TypeVerificationAffranchissementEnum.GRAMMAIRE_COUNTRY
#         ]]
#         assert len(grammar_verifs) == 3
        
#         # Vérification du service (invalide)
#         service_verif = next(v for v in grammar_verifs if v.type == TypeVerificationAffranchissementEnum.GRAMMAIRE_SERVICE)
#         assert service_verif.statut == StatutVerificationEnum.INVALIDE
#         assert "service différent de LV,LY : KO" in service_verif.message
        
#         # Vérification de la clé (valide)
#         key_verif = next(v for v in grammar_verifs if v.type == TypeVerificationAffranchissementEnum.GRAMMAIRE_CLE)
#         assert key_verif.statut == StatutVerificationEnum.VALIDE
#         assert "key égal à 5 : OK" in key_verif.message
        
#         # Vérification du pays (valide)
#         country_verif = next(v for v in grammar_verifs if v.type == TypeVerificationAffranchissementEnum.GRAMMAIRE_COUNTRY)
#         assert country_verif.statut == StatutVerificationEnum.VALIDE
#         assert "country égal à FR : OK" in country_verif.message
        
#         # Vérification du count pour S10
#         count_verifs = [v for v in aff_s10.verifications if v.type == TypeVerificationAffranchissementEnum.COUNT]
#         assert len(count_verifs) == 1
#         assert count_verifs[0].statut == StatutVerificationEnum.VALIDE
#         assert "jamais scanné : OK" in count_verifs[0].message
#         assert count_verifs[0].donnees == {'count': 0}

#         # Verification du SSU
#         count_verifs = [v for v in aff_s10.verifications if v.type == TypeVerificationAffranchissementEnum.SSU_TRACKING]
#         assert len(count_verifs) == 1
#         assert count_verifs[0].statut == StatutVerificationEnum.VALIDE
#         assert "Evénements sur 1 jour maxi" in count_verifs[0].message

#         count_verifs = [v for v in aff_s10.verifications if v.type == TypeVerificationAffranchissementEnum.SSU_PURCHASE]
#         assert len(count_verifs) == 1
#         assert count_verifs[0].statut == StatutVerificationEnum.NON_DETERMINE
#         assert "Achat trouvé" in count_verifs[0].message
        
#         # Vérification de la valorisation
#         assert env.valorisation == {
#             'postage': {
#                 'cout_enveloppe': 12.649999999999999,
#                 'cout_affranchissements_valide': 0.63,
#                 'nb_affranchissements_invalides': 1,
#                 'montant_sous_affranchissement': 12.019999999999998,
#                 'presence_affranchissements_invalide': True,
#                 'presence_taxe': True,
#                 'coeff_taxe_livraison': 0.1,
#             },
#             'livraison': {
#                 'taxe_livraison_a_recuperer': 1.202,
#                 'taxe_livraison_fixe': 3,
#                 'taxe_livraison_totale': 4.202,
#                 'cout_total': 16.221999999999998
#             },
#             'collecte': {
#                 'frais_collecte_ht': 4.68,
#                 'coeff_collecte_tva': 0.2,
#                 'cout_ht': 4.68,
#                 'cout_tva': 0.9359999999999999,
#                 'cout_ttc': 5.616
#             },
#             'expédition': {
#                 'frais_expédition': 4.3,
#                 'cout_ht': 8.98,
#                 'cout_tva': 0.9359999999999999,
#                 'cout_ttc': 9.916
#             }
#         }
