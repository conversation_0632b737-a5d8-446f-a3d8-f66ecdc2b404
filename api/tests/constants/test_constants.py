# from constants.variables import CONS<PERSON>NTES_VALORISATION, CONSTANTES_PRIX_AFFRANCHISSEMENT
# from constants.enumerations import TypeAffranchissementEnum
# from tests.fixtures.affranchissements import TNUM, S10


# class TestConstants:
    
#     def test_constants(self):
#         assert CONSTANTES_VALORISATION is not None
#         assert CONSTANTES_PRIX_AFFRANCHISSEMENT is not None
        
#     def test_type_affranchissement_enum_identify_by_code(self):
#         # Test TNUM identification
#         identified_type = TypeAffranchissementEnum.identify(code=TNUM().code)
#         assert identified_type["type"] == TypeAffranchissementEnum.TNUM
        
#         identified_type = TypeAffranchissementEnum.identify(code=S10().code)
#         assert identified_type["type"] == TypeAffranchissementEnum.S10

#     def test_type_affranchissement_enum_identify_by_type(self):
#         identified_type = TypeAffranchissementEnum.identify(type=TypeAffranchissementEnum.TNUM)
#         assert identified_type["type"] == TypeAffranchissementEnum.TNUM

#         identified_type = TypeAffranchissementEnum.identify(type=TypeAffranchissementEnum.S10)
#         assert identified_type["type"] == TypeAffranchissementEnum.S10

