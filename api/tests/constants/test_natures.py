from constants.variables import CONS<PERSON>NTES_VALORISATION, CONSTANTES_PRIX_AFFRANCHISSEMENT
from constants.affranchissements.nature import NATURES_AFFRANCHISSEMENT
from constants.enumerations import NatureAffranchissementEnum
from constants.enumerations import TypeAffranchissement<PERSON>num
from tests.fixtures.affranchissements import TNUM, S10


class TestNatures:
    

    def test_natures(self):
        assert NATURES_AFFRANCHISSEMENT[NatureAffranchissementEnum.LINT_50].poids == 50
        assert NATURES_AFFRANCHISSEMENT[NatureAffranchissementEnum.LINT_50].produit != None
        assert NATURES_AFFRANCHISSEMENT[NatureAffranchissementEnum.LINT_50].produit.poids_max == 100

        assert NATURES_AFFRANCHISSEMENT[NatureAffranchissementEnum.LINT_100].poids == 100
        assert NATURES_AFFRANCHISSEMENT[NatureAffranchissementEnum.LINT_100].produit != None
        assert NATURES_AFFRANCHISSEMENT[NatureAffranchissementEnum.LINT_100].produit.poids_max == 100
