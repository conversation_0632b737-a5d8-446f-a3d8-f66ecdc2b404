from models.public import ModeleAffranchissement, ValeurModeleAffranchissement, CategorieAffranchissement
from constants.enumerations import TypeAffranchissementEnum, DeviseEnum, CategorieAffranchissementEnum

class TestAffranchissement:
            
    def test_type_affranchissement(self):
        multiples = ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.MARI,
                type=TypeAffranchissementEnum.VAL,
                color="xtra_envelop_element_mari",
                variant="contained",
                valeurs=[
                    ValeurModeleAffranchissement(devise=DeviseEnum.EURO),
                    ValeurModeleAffranchissement(devise=DeviseEnum.EURO, prix_unite_devise=0.05, color="xtra_envelop_element_5centimes"),
                    ValeurModeleAffranchissement(devise=DeviseEnum.EURO, prix_unite_devise=1, color="xtra_envelop_element_1euro")
                ]
            )

        assert isinstance(multiples, list) == True
        assert len(multiples) == 3
        assert multiples[0].prix_unite_devise == None
        assert multiples[0].devise == DeviseEnum.EURO
        assert multiples[0].color == "xtra_envelop_element_mari"

        assert multiples[1].prix_unite_devise == 0.05
        assert multiples[1].devise == DeviseEnum.EURO
        assert multiples[1].color == "xtra_envelop_element_5centimes"

        assert multiples[2].prix_unite_devise == 1
        assert multiples[2].devise == DeviseEnum.EURO
        assert multiples[2].color == "xtra_envelop_element_1euro"



    def test_categorie_affranchissement(self):
        categorie = CategorieAffranchissement(
            id=CategorieAffranchissementEnum.MARI,
            display1="Mari",
            display2="Mari",
            description="Mari",
            color="xtra_envelop_element_mari",
            variant="contained",
            types_affranchissements=[
                ModeleAffranchissement(
                    type=TypeAffranchissementEnum.VAL,
                    color="xtra_envelop_element_mari",
                    variant="contained",
                    valeurs=[
                        ValeurModeleAffranchissement(devise=DeviseEnum.EURO),
                        ValeurModeleAffranchissement(devise=DeviseEnum.EURO, prix_unite_devise=0.05, color="xtra_envelop_element_5centimes"),
                        ValeurModeleAffranchissement(devise=DeviseEnum.EURO, prix_unite_devise=1, color="xtra_envelop_element_1euro")
                        ]
                ),
                ModeleAffranchissement(
                    type=TypeAffranchissementEnum.DEWD1,
                    color="xtra_envelop_element_mari",
                    variant="contained"
                ),
            ]
        )

        assert isinstance(categorie, CategorieAffranchissement)
        assert categorie.id == CategorieAffranchissementEnum.MARI
        assert categorie.display1 == "Mari"
        assert categorie.display2 == "Mari"
        assert categorie.description == "Mari"

        assert len(categorie.types_affranchissements) == 4

        # assert categorie.dict() == {
        #     "id": CategorieAffranchissementEnum.MARI,
        #     "display1": "Mari",
        #     "display2": "Mari",
        #     "description": "Mari",
        #     "color": "xtra_envelop_element_mari",
        # }
