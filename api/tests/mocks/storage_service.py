
from unittest.mock import patch, MagicMock
import functools
import os

def mock_blob_client_operations(base_url="https://storage.example.com"):
    """
    Mock pour les opérations BlobClient dans StorageService.upload_file
    
    Args:
        base_url: URL de base pour la génération de l'URL complète
    """
    # Créer un mock pour BlobClient.from_blob_url
    mock_blob_client = MagicMock()
    
    # Fonction pour extraire le chemin du blob à partir de l'URL complète
    def from_blob_url_side_effect(url):
        # Extraire le chemin du blob à partir de l'URL
        # Supposons que l'URL est de la forme base_url/chemin_blob
        blob_path = url.replace(base_url, "").lstrip("/")
        if "{blob_path}" in url:
            # Si l'URL contient encore le placeholder, utilisez un chemin par défaut
            blob_path = "test_file.jpg"
        
        # Configurer l'URL du mock avec le chemin extrait
        mock_blob_client.url = f"{base_url}/{blob_path}"
        
        return mock_blob_client
    
    # Configurer le mock pour upload_blob
    mock_blob_client.upload_blob = MagicMock()
    
    # Patch pour BlobClient.from_blob_url avec side_effect
    blob_client_patch = patch('azure.storage.blob.BlobClient.from_blob_url', 
                             side_effect=from_blob_url_side_effect)
    
    return blob_client_patch

def patch_blob_operations(base_url=None):
    """
    Décorateur pour mocker les opérations BlobClient dans StorageService.upload_file
    
    Args:
        base_url: URL de base personnalisée (optionnel)
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            custom_base_url = base_url or os.getenv("AZURE_STORAGE_CONNECTION_URL", "https://storage.example.com")
            if "{blob_path}" in custom_base_url:
                custom_base_url = custom_base_url.split("{blob_path}")[0]
            
            # Créer un mock pour BlobClient
            mock_blob_client = MagicMock()
            mock_blob_client.url = f"{custom_base_url}/test_file.jpg"
            mock_blob_client.upload_blob = MagicMock()
            
            # Configurer le side effect pour from_blob_url
            def from_blob_url_side_effect(url):
                blob_path = url.replace(custom_base_url, "").lstrip("/")
                if "{blob_path}" in url:
                    blob_path = "test_file.jpg"
                mock_blob_client.url = f"{custom_base_url}/{blob_path}"
                return mock_blob_client
            
            # Appliquer le patch
            with patch('azure.storage.blob.BlobClient.from_blob_url', side_effect=from_blob_url_side_effect):
                return func(*args, **kwargs)
        
        return wrapper
    
    return decorator

