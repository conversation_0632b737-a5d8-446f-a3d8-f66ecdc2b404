import json
from unittest.mock import patch

def mock_ssu_api_get_tracking(sid, finalized=True):
    mock_response = {
        "shipment": {
            "isFinal": finalized,
            "idShip": sid,
            "product": "Colissimo",
            "event": [
                {
                    "date": "2023-05-15T10:30:00+02:00",
                    "label": "Votre colis est en cours de livraison",
                    "code": "DCHG"
                },
                {
                    "date": "2023-05-14T18:45:00+02:00",
                    "label": "Votre colis est arrivé sur notre site de distribution",
                    "code": "AARF"
                }
            ]
        }
    }
    print(mock_response)
    return mock_response, None


# Créer une fonction qui retourne un décorateur patch configuré
def patch_ssu_tracking(finalized=True):
    return patch('services.ssu_api.SSUApi.get_tracking', 
                side_effect=lambda sid: mock_ssu_api_get_tracking(sid, finalized=finalized))