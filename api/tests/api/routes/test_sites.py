import pytest
from sqlmodel import Session, select
from models import Site, User
from tests.conftest import client as _client

class TestSitesAPI:

    @pytest.fixture(autouse=True)
    def setup(self, user, admin_user):
        self.client = _client(user, "/sites/")
        self.super_client = _client(admin_user, "/sites/")

    def test_create_site(self, db: Session) -> None:
        data = {"nom": "New Site"}
        r = self.client.post("/", json=data)
        assert r.status_code == 200
        created_site = r.json()
        site = db.exec(select(Site).where(Site.id == created_site["id"])).first()
        assert site
        assert site.nom == data["nom"]

    def test_read_sites(self, db: Session) -> None:
        r = self.super_client.get("/")
        assert 200 <= r.status_code < 300
        sites = r.json()
        assert isinstance(sites, list)
        
    def test_read_site(self, db: Session, site: Site) -> None:
        r = self.super_client.get(f"/{site.id}")
        assert 200 <= r.status_code < 300
        api_site = r.json()
        assert api_site["nom"] == site.nom

    def test_update_site(self, db: Session, site: Site) -> None:
        data = {"nom": "Updated Site"}
        r = self.super_client.put(f"/{site.id}", json=data)
        assert 200 <= r.status_code < 300
        updated_site = r.json()
        assert updated_site["nom"] == data["nom"]

    # def test_delete_site(self, db: Session, site: Site) -> None:
    #     r = self.super_client.delete(f"/{site.id}")
    #     assert 200 <= r.status_code < 300
    #     deleted_site = r.json()
    #     assert deleted_site["id"] == site.id
    #     result = db.exec(select(Site).where(Site.id == site.id)).first()
    #     assert result is None 