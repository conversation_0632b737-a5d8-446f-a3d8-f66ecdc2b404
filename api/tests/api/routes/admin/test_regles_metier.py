import pytest
from sqlmodel import Session, select
from models.business import RegleMetier
from tests.conftest import client as _client
from constants.enumerations import TypeRegleMetierEnum, TypeAffranchissementEnum

class TestReglesMetierAPI:

    @pytest.fixture(autouse=True)
    def setup(self, user, admin_user):
        self.client = _client(user, "/admin/regles-metier/")
        self.super_client = _client(admin_user, "/admin/regles-metier/")

    def test_create_regle_metier(self, db: Session) -> None:
        """Test la création d'une règle métier par un admin"""
        data = {
            "cle": "MAX_POIDS", 
            "valeur": {"valeur": "5000"}, 
            "type_regle": "VALEUR",
            "active": True
        }
        r = self.super_client.post("/", json=data)
        assert 200 <= r.status_code < 300
        created_regle = r.json()
        regle = db.exec(select(RegleMetier).where(RegleMetier.id == created_regle["id"])).first()
        assert regle
        assert regle.cle == data["cle"]
        assert regle.valeur == data["valeur"]
        assert regle.type_regle == TypeRegleMetierEnum.VALEUR

    def test_create_regle_metier_unauthorized(self, db: Session) -> None:
        """Test qu'un utilisateur normal ne peut pas créer une règle métier"""
        data = {"cle": "MIN_POIDS", "valeur": {"valeur": "10"}, "type_regle": "VALEUR"}
        r = self.client.post("/", json=data)
        assert r.status_code == 403

    def test_read_regles_metier(self, db: Session) -> None:
        """Test la récupération des règles métier par un admin"""
        # Créer quelques règles métier pour le test
        regle1 = RegleMetier(cle="DELAI_TRAITEMENT", valeur={"valeur": "48"}, type_regle=TypeRegleMetierEnum.VALEUR)
        regle2 = RegleMetier(cle="PRIX_BASE", valeur={"valeur": "2.50"}, type_regle=TypeRegleMetierEnum.VALEUR)
        db.add(regle1)
        db.add(regle2)
        db.commit()
        
        r = self.super_client.get("/")
        assert 200 <= r.status_code < 300
        regles = r.json()["items"]
        assert isinstance(regles, list)
        assert len(regles) >= 2
        
        # Test de la recherche
        r = self.super_client.get("/?search=DELAI")
        assert 200 <= r.status_code < 300
        regles = r.json()["items"]
        assert len(regles) == 1
        assert regles[0]["cle"] == "DELAI_TRAITEMENT"
        
        # Test de la pagination
        r = self.super_client.get("/?skip=1&limit=1")
        assert 200 <= r.status_code < 300
        regles = r.json()["items"]
        assert len(regles) == 1

    def test_read_regles_metier_unauthorized(self, db: Session) -> None:
        """Test qu'un utilisateur normal ne peut pas lister les règles métier"""
        r = self.client.get("/")
        assert r.status_code == 403

    def test_read_regle_metier(self, db: Session) -> None:
        """Test la récupération d'une règle métier spécifique par un admin"""
        # Créer une règle métier pour le test
        regle = RegleMetier(cle="TAUX_TVA", valeur={"valeur": "20"}, type_regle=TypeRegleMetierEnum.VALEUR)
        db.add(regle)
        db.commit()
        db.refresh(regle)
        
        r = self.super_client.get(f"/{regle.id}")
        assert 200 <= r.status_code < 300
        api_regle = r.json()
        assert api_regle["cle"] == regle.cle
        assert api_regle["valeur"] == regle.valeur
        assert api_regle["type_regle"] == regle.type_regle.value

    def test_read_regle_metier_unauthorized(self, db: Session) -> None:
        """Test qu'un utilisateur normal ne peut pas récupérer une règle métier spécifique"""
        # Créer une règle métier pour le test
        regle = RegleMetier(cle="TAUX_TVA", valeur={"valeur": "20"}, type_regle=TypeRegleMetierEnum.VALEUR)
        db.add(regle)
        db.commit()
        db.refresh(regle)
        
        r = self.client.get(f"/{regle.id}")
        assert r.status_code == 403

    def test_update_regle_metier(self, db: Session) -> None:
        """Test la mise à jour d'une règle métier par un admin"""
        # Créer une règle métier pour le test
        regle = RegleMetier(cle="SEUIL_ALERTE", valeur={"valeur": "1000"}, type_regle=TypeRegleMetierEnum.VALEUR)
        db.add(regle)
        db.commit()
        db.refresh(regle)
        
        data = {"valeur": {"valeur": "1500"}, "type_affranchissement": TypeAffranchissementEnum.S10.value}
        r = self.super_client.put(f"/{regle.id}", json=data)
        assert 200 <= r.status_code < 300
        updated_regle = r.json()
        assert updated_regle["cle"] == regle.cle  # La clé ne change pas
        assert updated_regle["valeur"] == data["valeur"]
        assert updated_regle["type_affranchissement"] == data["type_affranchissement"]

    def test_update_regle_metier_unauthorized(self, db: Session) -> None:
        """Test qu'un utilisateur normal ne peut pas mettre à jour une règle métier"""
        # Créer une règle métier pour le test
        regle = RegleMetier(cle="SEUIL_ALERTE", valeur={"valeur": "1000"}, type_regle=TypeRegleMetierEnum.VALEUR)
        db.add(regle)
        db.commit()
        db.refresh(regle)
        
        data = {"valeur": {"valeur": "1500"}}
        r = self.client.put(f"/{regle.id}", json=data)
        assert r.status_code == 403

    def test_delete_regle_metier(self, db: Session) -> None:
        """Test la suppression d'une règle métier par un admin"""
        # Créer une règle métier pour le test
        regle = RegleMetier(cle="REGLE_TEMP", valeur={"valeur": "temp"}, type_regle=TypeRegleMetierEnum.VALEUR)
        db.add(regle)
        db.commit()
        db.refresh(regle)
        
        r = self.super_client.delete(f"/{regle.id}")
        assert 200 <= r.status_code < 300
        deleted_regle = r.json()
        assert deleted_regle["id"] == regle.id
        result = db.exec(select(RegleMetier).where(RegleMetier.id == regle.id)).first()
        assert result is None

    def test_delete_regle_metier_unauthorized(self, db: Session) -> None:
        """Test qu'un utilisateur normal ne peut pas supprimer une règle métier"""
        # Créer une règle métier pour le test
        regle = RegleMetier(cle="REGLE_TEMP", valeur={"valeur": "temp"}, type_regle=TypeRegleMetierEnum.VALEUR)
        db.add(regle)
        db.commit()
        db.refresh(regle)
        
        r = self.client.delete(f"/{regle.id}")
        assert r.status_code == 403
