import pytest
from sqlmodel import Session, select
from models.business import <PERSON>asier, Site
from models.users import User<PERSON>ole

from tests.conftest import client as _client

class TestCasiersAPI:

    @pytest.fixture(autouse=True)
    def setup(self, user, admin_user):
        self.client = _client(user, "/casiers/")
        self.super_client = _client(admin_user, "/casiers/")

    def test_filter_casiers_by_site_name(self, db: Session) -> None:
        """
        Test le filtrage des casiers par nom de site.
        Reproduit un bug où la recherche par nom de site ne fonctionne pas correctement.
        """
        # Créer deux sites avec des noms distincts
        site_roissy = Site(nom="ROISSY")
        site_aubagne = Site(nom="AUBAGNE")
        db.add(site_roissy)
        db.add(site_aubagne)
        db.commit()
        db.refresh(site_roissy)
        db.refresh(site_aubagne)

        # Créer un casier pour chaque site
        casier_roissy = Casier(
            numero="R001",
            emplacement="Zone A",
            site_id=site_roissy.id
        )
        casier_aubagne = Casier(
            numero="A001",
            emplacement="Zone B",
            site_id=site_aubagne.id
        )
        db.add(casier_roissy)
        db.add(casier_aubagne)
        db.commit()

        # Test de recherche pour le site ROISSY
        response = self.super_client.get("/?search=ROISSY")
        assert response.status_code == 200
        data = response.json()
        
        # Vérifier que seul le casier de ROISSY est retourné
        assert data["total_items"] == 1, "La recherche devrait retourner exactement 1 casier"
        assert data["items"][0]["site"]["nom"] == "ROISSY"
        assert data["items"][0]["numero"] == "R001"
        
        # Test de recherche pour le site AUBAGNE
        response = self.super_client.get("/?search=AUBAGNE")
        assert response.status_code == 200
        data = response.json()
        
        # Vérifier que seul le casier d'AUBAGNE est retourné
        assert data["total_items"] == 1, "La recherche devrait retourner exactement 1 casier"
        assert data["items"][0]["site"]["nom"] == "AUBAGNE"
        assert data["items"][0]["numero"] == "A001"
        
        # Test avec une recherche partielle
        response = self.super_client.get("/?search=ROI")
        assert response.status_code == 200
        data = response.json()
        
        # Vérifier que seul le casier de ROISSY est retourné
        assert data["total_items"] == 1, "La recherche partielle devrait fonctionner"
        assert data["items"][0]["site"]["nom"] == "ROISSY"