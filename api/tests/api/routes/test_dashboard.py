import pytest
import json
from datetime import datetime, timedelta
from sqlmodel import Session, select
from models.business import Enveloppe, Expediteur, Site, StatutEnveloppeEnum, User
from constants.enumerations import DestinationEnveloppeEnum
from tests.conftest import client as _client

class TestDashboardAPI:

    @pytest.fixture(autouse=True)
    def setup(self, user, admin_user):
        self.client = _client(user, "/dashboard/")
        self.super_client = _client(admin_user, "/dashboard/")

    @pytest.fixture
    def create_test_data(self, db: Session, site: Site, user: User):
        """Crée des données de test pour les métriques du dashboard"""
        # Créer des expéditeurs
        expediteur1 = Expediteur(
            nom="Expediteur Test 1",
            adresse="1 Rue de Test",
            coclico="COC123",
            siret="12345678901234",
            site_id=site.id
        )
        expediteur2 = Expediteur(
            nom="Expediteur Test 2",
            adresse="2 Rue de Test",
            coclico="COC456",
            siret="98765432109876",
            site_id=site.id
        )
        db.add(expediteur1)
        db.add(expediteur2)
        db.commit()
        db.refresh(expediteur1)
        db.refresh(expediteur2)
        
        # Créer des enveloppes avec valorisation
        today = datetime.now()
        yesterday = today - timedelta(days=1)
        
        # Enveloppes pour expediteur1
        enveloppe1 = Enveloppe(
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
            poids=100.0,
            expediteur_id=expediteur1.id,
            site_id=site.id,
            statut=StatutEnveloppeEnum.TERMINEE,
            created_at=today,
            valorisation={
                "postage": {"montant_sous_affranchissement": 1.5},
                "livraison": {"cout_total": 2.0},
                "collecte": {"cout_ht": 3.0, "cout_tva": 0.6, "cout_ttc": 3.6},
                "expédition": {"cout_ht": 4.0, "cout_tva": 0.8, "cout_ttc": 4.8}
            },
            user_id=user.id
        )

        
        enveloppe2 = Enveloppe(
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
            poids=100.0,
            expediteur_id=expediteur1.id,
            site_id=site.id,
            statut=StatutEnveloppeEnum.TERMINEE,
            created_at=yesterday,
            valorisation={
                "postage": {"montant_sous_affranchissement": 2.5},
                "livraison": {"cout_total": 3.0},
                "collecte": {"cout_ht": 4.0, "cout_tva": 0.8, "cout_ttc": 4.8},
                "expédition": {"cout_ht": 5.0, "cout_tva": 1.0, "cout_ttc": 6.0}
            },
            user_id=user.id
        )
        
        # Enveloppe pour expediteur2
        enveloppe3 = Enveloppe(
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
            poids=100.0,
            expediteur_id=expediteur2.id,
            site_id=site.id,
            statut=StatutEnveloppeEnum.TERMINEE,
            created_at=today,
            valorisation={
                "postage": {"montant_sous_affranchissement": 3.0},
                "livraison": {"cout_total": 4.0},
                "collecte": {"cout_ht": 5.0, "cout_tva": 1.0, "cout_ttc": 6.0},
                "expédition": {"cout_ht": 6.0, "cout_tva": 1.2, "cout_ttc": 7.2}
            },
            user_id=user.id
        )
        
        # Enveloppe en édition (ne devrait pas être comptée dans les enveloppes par jour)
        enveloppe4 = Enveloppe(
            destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
            poids=100.0,
            expediteur_id=expediteur2.id,
            site_id=site.id,
            statut=StatutEnveloppeEnum.EDITION,
            created_at=today,
            valorisation={
                "postage": {"montant_sous_affranchissement": 0.0},
                "livraison": {"cout_total": 0.0},
                "collecte": {"cout_ht": 0.0, "cout_tva": 0.0, "cout_ttc": 0.0},
                "expédition": {"cout_ht": 0.0, "cout_tva": 0.0, "cout_ttc": 0.0}
            },
            user_id=user.id
        )
        
        db.add(enveloppe1)
        db.add(enveloppe2)
        db.add(enveloppe3)
        db.add(enveloppe4)
        db.commit()
        
        return {
            "expediteurs": [expediteur1, expediteur2],
            "enveloppes": [enveloppe1, enveloppe2, enveloppe3, enveloppe4],
            "today": today,
            "yesterday": yesterday
        }

    def test_get_dashboard_metrics(self, db: Session, create_test_data):
        """Test la récupération des métriques du dashboard"""
        # Définir les dates pour le filtre
        start_date = (create_test_data["yesterday"] - timedelta(days=1)).strftime("%Y-%m-%d")
        end_date = (create_test_data["today"] + timedelta(days=1)).strftime("%Y-%m-%d")
        
        # Appel de l'API
        response = self.super_client.get(
            f"/metrics?start_date={start_date}&end_date={end_date}"
        )
        
        # Vérifications
        assert response.status_code == 200, response.json()
        data = response.json()
        
        # Vérifier la structure des données
        assert "expediteurs_sous_affranchissement" in data
        assert "enveloppes_par_jour" in data
        
        # Vérifier les données des expéditeurs
        expediteurs = data["expediteurs_sous_affranchissement"]
        assert len(expediteurs) == 2
        
        # Vérifier les totaux pour le premier expéditeur
        exp1 = next(e for e in expediteurs if e["nom"] == "Expediteur Test 1")
        assert exp1["plis"] == 2
        assert exp1["sous_aff"] == 4.0  # 1.5 + 2.5
        assert exp1["cas_a_deliver"] == 5.0  # 2.0 + 3.0
        assert exp1["cas_b_collect_ht"] == 7.0  # 3.0 + 4.0
        
        # Vérifier les données des enveloppes par jour
        enveloppes_par_jour = data["enveloppes_par_jour"]
        assert len(enveloppes_par_jour) == 2  # aujourd'hui et hier
        
        # Vérifier le nombre d'enveloppes pour aujourd'hui
        today_data = next(e for e in enveloppes_par_jour if e["jour"] == create_test_data["today"].strftime("%Y-%m-%d"))
        assert today_data["nombre"] == 2  # 2 enveloppes terminées aujourd'hui (pas celle en EDITION)
        
        # Vérifier le nombre d'enveloppes pour hier
        yesterday_data = next(e for e in enveloppes_par_jour if e["jour"] == create_test_data["yesterday"].strftime("%Y-%m-%d"))
        assert yesterday_data["nombre"] == 1  # 1 enveloppe terminée hier

    def test_get_dashboard_metrics_empty(self, db: Session):
        """Test la récupération des métriques quand il n'y a pas de données"""
        # Appel de l'API
        response = self.super_client.get("/metrics")
        
        # Vérifications
        assert response.status_code == 200
        data = response.json()
        
        # Vérifier que les listes sont vides
        assert len(data["expediteurs_sous_affranchissement"]) == 0
        assert len(data["enveloppes_par_jour"]) == 0

    def test_get_dashboard_metrics_unauthorized(self):
        """Test l'accès non autorisé aux métriques du dashboard"""
        # Créer un client sans authentification
        client = _client(None, "/dashboard/")
        
        # Appel de l'API
        response = client.get("/metrics")
        
        # Vérifier que l'accès est refusé
        assert response.status_code == 401