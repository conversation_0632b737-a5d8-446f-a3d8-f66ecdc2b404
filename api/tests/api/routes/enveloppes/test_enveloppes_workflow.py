from tests.conftest import client as _client
import pytest
from sqlmodel import Session
from models.business import Enveloppe, Affranchissement, PhotoEnveloppe, Expediteur
from datetime import datetime
from tests.fixtures import enveloppe, S10, user
from constants.enumerations import StatutLotExpediteurEnum, NatureAffranchissementEnum, DeviseEnum, StatutEnveloppeEnum, DestinationEnveloppeEnum
from fastapi import UploadFile
from unittest.mock import AsyncMock, MagicMock, patch
import io
from constants.enumerations import StatutEnveloppeEnum, CategorieAffranchissementEnum, TypeAffranchissementEnum
from tests.fixtures.codes import CODE_S10
from tests.fixtures.regles_metier import creer_sequence_s10
from tests.mocks.ssu_api import patch_ssu_tracking


class TestEnveloppesWorkflow:
    """
    Test les workflows d'enveloppes : tests d'intégration
    """

    @pytest.fixture(autouse=True)
    def setup(self, user, admin_user):
        self.user_client = _client(user, "/enveloppes")
        self.admin_user_client = _client(admin_user, "/enveloppes")
        self.guess_client = _client(None, "/enveloppes")

    @patch_ssu_tracking(finalized=False)
    def test_workflow_sd87(self, mock_verification_ssu_tracking, db: Session, enveloppe: Enveloppe) -> None:
        data = {
            "code": "87000763985319W",
        }

        # Ajout de l'affranchissement (sans nature, retour de l'affranchissement a completer non enregistré en base)
        response = self.user_client.post(f"/{enveloppe.id}/add", json=data)
        assert response.status_code == 200, response.json()
        assert db.query(Affranchissement).filter(Affranchissement.code == "87000763985319W").first() is None
        assert response.json()["affranchissement"]["informations"]["complet"] == False
        
        # Ajout de l'affranchissement complet
        data["nature"] = "LV_20"
        response = self.user_client.post(f"/{enveloppe.id}/add", json=data)
        assert response.status_code == 200
        assert db.query(Affranchissement).filter(Affranchissement.code == "87000763985319W").first() is not None


        data = response.json()
        for item in ["enveloppe", "affranchissement"]:
            assert item in data

        assert data["frontend_action"] == "RIEN"
        assert data["affranchissement"]["informations"]["complet"] == True

        enveloppe_json = data["enveloppe"]
        affranchissement_json = data["affranchissement"]
        affranchissement_id = affranchissement_json["id"]
        assert len(enveloppe_json["affranchissements"]) == 1
        assert affranchissement_json["statut"] == "VALIDE"
        
        # ICI, l'affranchissement est INCOMPLET car pas de prix renseigné

        # On ne peut pas terminer l'enveloppe
        response = self.user_client.post(f"/{enveloppe.id}/terminer")
        assert response.status_code == 400
        data = response.json()
        assert data["detail"] == "L'enveloppe n'est pas complète, certains affranchissements sont incomplets ou photos manquantes"


        # on doit lui donner une nature pour le rendre complet
        # on va donc le modifier
        response = self.user_client.put(f"/{enveloppe.id}/affranchissements/{affranchissement_id}", json={
            "nature": "LV_20",
            "prix_unite_devise": 100
        })
        data = response.json()
        assert response.status_code == 200
        # assert "id" in data
        # assert data["affranchissements"][0]["nature"] == "LV_20"

        # Load affranchissement
        aff = db.query(Affranchissement).first()
        assert aff.nature == NatureAffranchissementEnum.LV_20
        assert aff.prix_unite_euros == 1.35
        assert aff.prix_unite_devise == 1.35
        assert aff.devise == DeviseEnum.EURO
        assert aff.quantite == 1
        assert aff.statut == "VALIDE"
        # L'affranchissement est complet
        assert aff.informations.champs_manquants == []

         # + 3 photos
        for _ in range(3):
            enveloppe.photos.append(PhotoEnveloppe(format="jpg", url="https://storage.example.com/test.jpg"))
        db.add(enveloppe)
        db.commit()

        assert enveloppe.poids <= 20

        # L'enveloppe fait 20g ou moins et on a définit un SD87 de nature LV_20G donc OK
        
        # On peut terminer l'enveloppe
        from services.enveloppe import ServiceEnveloppe
        assert ServiceEnveloppe(enveloppe).prix_affranchissements_valide() == 1.35
        assert enveloppe.service.calcul_frais_postaux_dus() == 1.35
        response = self.user_client.post(f"/{enveloppe.id}/terminer")
        assert response.status_code == 200
        data = response.json()
        assert data["statut"] == "TERMINEE"
        assert data["valorisation"] is not None
        
    def test_workflow_code_invalide(self, db: Session, enveloppe: Enveloppe) -> None:
        data = {
            "code": "LOI3456789FR",  # Format inconnu
        }

        # Ajout de l'affranchissement
        response = self.user_client.post(f"/{enveloppe.id}/add", json=data)
        assert response.status_code == 400
        assert response.json()["detail"] == "Le code LOI3456789FR n'a pas été identifié."

        enveloppe = db.query(Enveloppe).filter(Enveloppe.id == enveloppe.id).first()
        assert len(enveloppe.affranchissements) == 0
    
    def test_workflow_aff_normal_invalide_non_identifie(self, db: Session, enveloppe: Enveloppe) -> None:
        data = {
            "categorie": "MARI",
            "type": "SD",
            "prix_unite_devise": 1.26,
            "devise": "EURO",
            "quantite": 1,
        }

        # Ajout de l'affranchissement
        response = self.user_client.post(f"/{enveloppe.id}/add", json=data)
        assert response.status_code == 400

        data = response.json()
        assert data["detail"] == "Le type d'affranchissement n'a pas été identifié"
        
        
    def test_workflow_aff_normal_valorisable(self, db: Session, enveloppe_complete: Enveloppe) -> None:
        data = {
            "categorie": "MARI",
            "type": "VAL",
            "prix_unite_devise": 1.26,
            "devise": "EURO",
            "quantite": 4,
            "statut": "INVALIDE"
        }

        enveloppe = enveloppe_complete

        # Ajout de l'affranchissement
        response = self.user_client.post(f"/{enveloppe.id}/add", json=data)

        assert response.status_code == 200
        data = response.json()
        affranchissement_id = data["affranchissement"]["id"]
        assert data["frontend_action"] == "RIEN"
        assert data["enveloppe"]["statut"] == "EDITION"
        assert data["affranchissement"]["prix_unite_devise"] == 1.26
        assert data["affranchissement"]["prix_unite_euros"] == 1.26
        assert data["affranchissement"]["quantite"] == 4
        assert data["affranchissement"]["statut"] == "INVALIDE"

        # finalement on update la quantité
        
        response = self.user_client.put(f'/{enveloppe.id}/affranchissements/{affranchissement_id}', json={
            "quantite": 3,
            "nature": "NON_DETERMINE",
            "prix_unite_devise": 100
        })

        assert response.status_code == 200, response.json()
        data = response.json()
        assert data["frontend_action"] == "RIEN"
        assert data["enveloppe"]["statut"] == "EDITION"
        assert data["affranchissement"]["quantite"] == 3
        assert data["affranchissement"]["prix_unite_devise"] == 100
        
        # On peut modifier l'enveloppe encore car par terminer
        response = self.user_client.put(f'/{enveloppe.id}/affranchissements/{affranchissement_id}', json={
            "quantite": 3
        })
        assert enveloppe.statut == StatutEnveloppeEnum.EDITION
        assert response.json()["affranchissement"]["statut"] == "INVALIDE"

        # On peut terminer l'enveloppe
        response = self.user_client.post(f"/{enveloppe.id}/terminer")
        assert response.status_code == 200
        data = response.json()
        assert data["statut"] == "FRAUDULEUSE"
        assert data["valorisation"] is not None

        # Enveloppe terminée, on ne peut plus modifier l'affranchissement
        response = self.user_client.put(f'/{enveloppe.id}/affranchissements/{affranchissement_id}', json={
            "quantite": 3
        })
        assert response.status_code == 200
        data = response.json()
        
    def test_creation_lot_expediteur_avec_casier(self, db: Session, user, site, expediteur) -> None:
        """
        Test la création automatique d'un LotExpediteur avec un Casier lorsqu'un expéditeur 
        a déjà plusieurs enveloppes frauduleuses.
        
        1. Créer 10 enveloppes frauduleuses pour un même expéditeur
        2. Récupérer l'enveloppe en édition et la mettre à jour avec l'expéditeur
        3. Ajouter un affranchissement et terminer l'enveloppe
        4. Vérifier qu'un LotExpediteur est créé automatiquement avec un casier assigné
        5. Vérifier que la nouvelle enveloppe est dans le casier
        """
        from models.business import Enveloppe, LotExpediteur, Casier
        from constants.enumerations import StatutEnveloppeEnum, StatutLotExpediteurEnum
        
        # Créer des Casiers disponibles
        for i in range(5):
            casier = Casier(
                site_id=site.id,
                emplacement=f"CASIER {i}",
                numero=f"{i}"
            )
            db.add(casier)
        db.commit()
        
        # Créer 10 enveloppes frauduleuses pour le même expéditeur
        for i in range(10):
            enveloppe = Enveloppe(
                destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
                expediteur_id=expediteur.id,
                statut=StatutEnveloppeEnum.FRAUDULEUSE,
                site_id=site.id,
                user_id=user.id
            )
            db.add(enveloppe)
        db.commit()
        
        # Vérifier qu'aucun LotExpediteur n'existe encore
        lots_avant = db.query(LotExpediteur).filter(LotExpediteur.expediteur_id == expediteur.id).all()
        assert len(lots_avant) == 0
    

        def creer_enveloppe_fraude_finaliser(expediteur_id):
            # Récupérer l'enveloppe en édition
            response = self.user_client.get("/edition")
            assert response.status_code == 200
            nouvelle_enveloppe_id = response.json()["id"]

            # Mettre à jour l'enveloppe avec l'expéditeur
            response = self.user_client.post(f"/", json={
                "id": nouvelle_enveloppe_id,
                "expediteur_id": expediteur_id,
                "site_id": site.id,
                "produit": "LV",
                "poids": 100.0,
                "surpoids": False,
                "surdimensionne": False
            })
            assert response.status_code == 200, response.json()
            
            # Ajouter un affranchissement invalide
            data = {
                "categorie": "BEAU",
                "type": "VAL",
                "prix_unite_devise": 2.50,
                "devise": "EURO",
                "quantite": 1,
                "statut": "INVALIDE"
            }
            response = self.user_client.post(f"/{nouvelle_enveloppe_id}/add", json=data)
            assert response.status_code == 200, response.json()

            # Ajouter une fausse photo
            db.add(PhotoEnveloppe(
                format="jpg",
                qualite="haute",
                largeur=800,
                hauteur=600,
                orientation="portrait",
                enveloppe_id=nouvelle_enveloppe_id
            ))
            db.commit()
            
            # Terminer l'enveloppe (ce qui devrait déclencher la création du lot)
            response = self.user_client.post(f"/{nouvelle_enveloppe_id}/terminer")
            assert response.status_code == 200, response.json()
            assert response.json()["statut"] == "FRAUDULEUSE"

            return nouvelle_enveloppe_id

        nouvelle_enveloppe_id = creer_enveloppe_fraude_finaliser(expediteur_id=expediteur.id)
        
        # Vérifier qu'un LotExpediteur a été créé
        lots_apres = db.query(LotExpediteur).filter(LotExpediteur.expediteur_id == expediteur.id).all()
        assert len(lots_apres) == 1
        
        lot = lots_apres[0]
        assert lot.statut == StatutLotExpediteurEnum.OUVERT
        
        # Vérifier que des casiers ont été assignés au lot
        assert len(lot.casiers) == 1
        
        # Vérifier que la nouvelle enveloppe est dans un casier du lot
        nouvelle_enveloppe = db.query(Enveloppe).filter(Enveloppe.id == nouvelle_enveloppe_id).first()
        assert nouvelle_enveloppe.casier_id is not None
        
        casier = db.query(Casier).filter(Casier.id == nouvelle_enveloppe.casier_id).first()
        assert casier is not None
        assert casier.enveloppes[0].lot_expediteur_id == lot.id
        
        # Vérifier que l'enveloppe est accessible via la relation lot.enveloppes
        enveloppes_dans_lot = lot.enveloppes
        assert len(enveloppes_dans_lot) > 0
        assert nouvelle_enveloppe_id in [e.id for e in enveloppes_dans_lot]

        # On recréer 4 nouvelle Enveloppe, un nouveau Casier doit être attribué
        for i in range(4):
            nouvelle_enveloppe_id = creer_enveloppe_fraude_finaliser(expediteur_id=expediteur.id)

            # Vérifier qu'un a toujours qu'un LotExpediteur
            lots_apres = db.query(LotExpediteur).filter(LotExpediteur.expediteur_id == expediteur.id).all()
            assert len(lots_apres) == 1

            lot = lots_apres[0]
            assert lot.statut == StatutLotExpediteurEnum.OUVERT

            assert len(lot.casiers) == 1
        
        # Casier doit être PLEIn + 1 + 4 = 5
        casier = db.query(Casier).filter(Casier.id == nouvelle_enveloppe.casier_id).first()
        # assert casier.statut == StatutCasierEnum.PLEIN
        # assert len(casier.enveloppes) == 5

        nouvelle_enveloppe_id = creer_enveloppe_fraude_finaliser(expediteur_id=expediteur.id)

        # On a donc 2 casier, car les casier on une capacité de 5
        assert len(lot.casiers) == 1

        # L'utitilisateur a donc 6 enveloppes dans son lot
        assert len(lot.enveloppes) == 6


        # # L'utilisateur veut changer de cassier pour la dernière enveloppe
        # response = self.user_client.post(f"/{nouvelle_enveloppe_id}/changer-casier")
        # assert response.status_code == 200, response.json()

        # lots = db.query(LotExpediteur).filter(LotExpediteur.expediteur_id == expediteur.id).all()
        # lot = lots[0]

        # assert len(lot.casiers[0].enveloppes) == 5
        # assert len(lot.casiers[1].enveloppes) == 1
        # assert len(lot.casiers[2].enveloppes) == 1
        # assert lot.casiers[2].enveloppes[0].id == nouvelle_enveloppe_id

        # On ajoute des enveloppes pour un autre expéditeur fraudeur
        expediteur_2= Expediteur(nom="fraudeur #2")
        db.add(expediteur_2)
        db.commit()

         # Créer 10 enveloppes frauduleuses pour le même expéditeur
        for i in range(10):
            enveloppe = Enveloppe(
                destination_enveloppe=DestinationEnveloppeEnum.METROPOLE,
                expediteur_id=expediteur_2.id,
                statut=StatutEnveloppeEnum.SOUS_AFFRANCHI,
                site_id=site.id,
                user_id=user.id
            )
            db.add(enveloppe)
        db.commit()

        nouvelle_enveloppe_id = creer_enveloppe_fraude_finaliser(expediteur_id=expediteur_2.id)
        assert len(db.query(LotExpediteur).filter(LotExpediteur.expediteur_id == expediteur_2.id).all()) == 1
        assert len(db.query(LotExpediteur).filter(LotExpediteur.expediteur_id == expediteur.id).all()) == 1
        assert len(db.query(LotExpediteur).all()) == 2

        # 2 casier occupées : casier qui ont des enveloppes
        assert len(db.query(Casier).filter(
                Casier.enveloppes.any(),
                Casier.site_id == site.id
            ).all()) == 2

        assert len(db.query(Casier).filter(
                ~Casier.enveloppes.any(),
                Casier.site_id == site.id
            ).all()) == 3

    def test_ajout_s10_manuel_sans_code(self, db: Session, enveloppe_complete: Enveloppe) -> None:
        data = {
            "categorie": "CODE",
            "type": "S10",
            "prix_unite_devise": 2.50,
            "devise": "EURO",
            "quantite": 1
        }

        response = self.user_client.post(f"/{enveloppe_complete.id}/add", json=data)
        assert response.status_code == 200, response.json()
        assert response.json()["enveloppe"]["statut"] == "EDITION"
        assert response.json()["affranchissement"]["statut"] == "INVALIDE"


    def test_ajout_sdm_manuel_sans_code(self, db: Session, enveloppe_complete: Enveloppe) -> None:
        data = {
            "categorie": "CODE",
            "type": "SD",
            "prix_unite_devise": None,
            "devise": "EURO",
            "quantite": 1,
            "nature": "LV_20",
            "origine": "MTEL"
        }

        response = self.user_client.post(f"/{enveloppe_complete.id}/add", json=data)

        assert response.status_code == 200, response.json()
        assert response.json()["enveloppe"]["statut"] == "EDITION"
        assert response.json()["affranchissement"]["statut"] == "INVALIDE"

    def test_get_enveloppe_edition_par_utilisateur(self, db: Session, user, admin_user, site) -> None:
        """
        Test que l'endpoint /enveloppes/-1 retourne l'enveloppe en EDITION 
        correspondant à l'utilisateur connecté
        """
        from models.business import Enveloppe
        from constants.enumerations import StatutEnveloppeEnum

        # Créer une enveloppe en EDITION pour le premier utilisateur
        enveloppe_user1 = Enveloppe(
            statut=StatutEnveloppeEnum.EDITION,
            user_id=user.id,
            site_id=site.id
        )
        db.add(enveloppe_user1)

        # Créer une enveloppe en EDITION pour le deuxième utilisateur
        enveloppe_user2 = Enveloppe(
            statut=StatutEnveloppeEnum.EDITION,
            user_id=admin_user.id,
            site_id=site.id
        )
        db.add(enveloppe_user2)
        db.commit()

        # Vérifier que chaque utilisateur obtient sa propre enveloppe
        response1 = self.user_client.get("/edition")
        assert response1.status_code == 200
        data1 = response1.json()
        assert data1["id"] == enveloppe_user1.id
        assert data1["user"]["id"] == user.id

        response2 = self.admin_user_client.get("/edition")
        assert response2.status_code == 200
        data2 = response2.json()
        assert data2["id"] == enveloppe_user2.id
        assert data2["user"]["id"] == admin_user.id

    
    def test_ajout_sd87_via_code_sans_nature(self, db: Session, enveloppe_complete: Enveloppe) -> None:
        """
        Test l'ajout d'un SD87 via un code et doit retourner l'affranchissement non enregistré en base 
        car le code n'a pas de nature encore renseignée
        """
        data = {
            "code": "SD87000999922741N"
        }

        response = self.user_client.post(f"/{enveloppe_complete.id}/add", json=data)
        assert response.status_code == 200, response.json()
        assert response.json()["affranchissement"]["code"] == "SD87000999922741N"
        assert response.json()["affranchissement"]["informations"]["complet"] == False

    def test_workflow_aff_sd88(self, db: Session, enveloppe: Enveloppe) -> None:
        """Test l'ajout d'un affranchissement SD avec origine 88"""
        data = {
            "categorie": "CODE",
            "type": "SD",
            "prix_unite_devise": 0.5,
            "devise": "EURO",
            "origine": "88"
        }

        # Ajout de l'affranchissement
        response = self.user_client.post(f"/{enveloppe.id}/add", json=data)
        assert response.status_code == 200, response.json()
        
        # Vérification des données retournées
        result = response.json()
        assert result["affranchissement"]["categorie"] == "CODE"
        assert result["affranchissement"]["type"] == "SD"
        assert result["affranchissement"]["origine"] == "88"
        assert result["affranchissement"]["prix_unite_devise"] == 0.5
        assert result["affranchissement"]["devise"] == "EURO"
        
        # Vérification en base de données
        aff = db.query(Affranchissement).filter(Affranchissement.enveloppe_id == enveloppe.id).first()
        assert aff is not None
        assert aff.categorie == CategorieAffranchissementEnum.CODE
        assert aff.type == TypeAffranchissementEnum.SD
        assert aff.origine == "88"
        assert aff.prix_unite_devise == 0.5
        assert aff.devise == DeviseEnum.EURO

    @patch_ssu_tracking(finalized=False)
    def test_ajout_code_s10_doit_etre_valoriser(self, mock, db: Session, enveloppe_complete: Enveloppe) -> None:
        """Test l'ajout d'un code S10 qui doit être valorisé"""
        from constants.enumerations import TypeAffranchissementEnum
        from models.business import RegleMetier

        # Suppression de séquence
        db.query(RegleMetier).filter(RegleMetier.type_affranchissement == TypeAffranchissementEnum.S10).delete()

        # Création de séquences
        creer_sequence_s10(db, "LE", "01469917", "01469918", "PHILAPOSTE")
        creer_sequence_s10(db, "LE", "01469919", "01479919", "Autre")

        data = {
            "code": "LE014699171FR"
        }

        response = self.user_client.post(f"/{enveloppe_complete.id}/add", json=data)
        assert response.status_code == 200, response.json()

        # Le code va être valoriser par son service => 2.80
        assert response.json()["affranchissement"]["prix_unite_devise"] == 2.80

        # REGLE_METIER: NON PHILAPOSTE => 0
        data = {
            "code": "LE014799181FR"
        }
        response = self.user_client.post(f"/{enveloppe_complete.id}/add", json=data)
        assert response.status_code == 200, response.json()
        assert response.json()["affranchissement"]["prix_unite_devise"] == 0

    def test_ajout_sd86_sans_code_et_sans_nature(self, db: Session, enveloppe: Enveloppe) -> None:
        """
        Test l'ajout d'un affranchissement SD86 sans code et sans nature.
        L'affranchissement doit être créé avec un statut INVALIDE par défaut.
        REGLE_METIER: On accepte un affranchissement INVALIDE sans nature (car on s'en fou)
        """
        data = {
            "categorie": "CODE",
            "type": "SD",
            "origine": "87",
            "devise": "EURO",
            "quantite": 1
        }

        # Ajout de l'affranchissement
        response = self.user_client.post(f"/{enveloppe.id}/add", json=data)
        assert response.status_code == 200, response.json()
        
        # Vérification des données retournées
        result = response.json()
        assert result["affranchissement"]["categorie"] == "CODE"
        assert result["affranchissement"]["type"] == "SD"
        assert result["affranchissement"]["origine"] == "87"
        assert result["affranchissement"]["devise"] == "EURO"
        assert result["affranchissement"]["nature"] == "NON_DETERMINE"
        assert result["affranchissement"]["statut"] == "INVALIDE"
        
        # Vérification en base de données
        aff = db.query(Affranchissement).filter(Affranchissement.enveloppe_id == enveloppe.id).first()
        assert aff is not None
        assert aff.categorie == CategorieAffranchissementEnum.CODE
        assert aff.type == TypeAffranchissementEnum.SD
        assert aff.origine == "87"
        assert aff.devise == DeviseEnum.EURO
        assert aff.nature == NatureAffranchissementEnum.NON_DETERMINE
        assert aff.statut == "INVALIDE"
        
    @patch_ssu_tracking(finalized=False)
    def test_valorisation_exemple_1(self, ssu , db: Session, user, expediteur) -> None:
        """
        Test la valorisation d'une enveloppe avec des exemples
        """
        enveloppe_edition = self.user_client.get("/edition").json()
        enveloppe_id = enveloppe_edition["id"]

        # Mise à jour enveloppe
        response = self.user_client.post(f"/", json={
            "id": enveloppe_id,
            "poids": 348,
            "surpoids": False,
            "surdimensionne": False,
            "destination_enveloppe": "METROPOLE",
            "expediteur_id": expediteur.id
        })
        assert response.status_code == 200, response.json()

        # Ajout de l'affranchissement
        response = self.user_client.post(f"/{enveloppe_id}/add", json={
            "code": "%000000088000092387838600250A18^"
        })

        # Ajoute 2 BEAU franc 
        response = self.user_client.post(f"/{enveloppe_id}/add", json={
            "categorie": "BEAU",
            "type": "VAL",
            "prix_unite_devise": 2.10,
            "devise": "FRANCS",
            "quantite": 2,
            "statut": "VALIDE"
        })

        enveloppe = db.query(Enveloppe).filter(Enveloppe.id == enveloppe_id).first()
        service = enveloppe.service

        assert enveloppe.poids == 348
        assert len(enveloppe.affranchissements) == 2

        # Rounded price
        assert round(service.prix_affranchissements_valide(), 2) == 1.14
        assert round(service.calcul_frais_postaux_dus(), 2) == 7.20


    @patch_ssu_tracking(finalized=False)
    def test_valorisation_exemple_2(self, ssu , db: Session, user, expediteur) -> None:
        """
        Test la valorisation d'une enveloppe avec des exemples
        """
        enveloppe_edition = self.user_client.get("/edition").json()
        enveloppe_id = enveloppe_edition["id"]

        # Mise à jour enveloppe
        response = self.user_client.post(f"/", json={
            "id": enveloppe_id,
            "poids": 72,
            "surpoids": False,
            "surdimensionne": False,
            "destination_enveloppe": "INTERNATIONAL",
            "expediteur_id": expediteur.id
        })
        assert response.status_code == 200, response.json()

        # Ajout de l'affranchissement
        response = self.user_client.post(f"/{enveloppe_id}/add", json={
            "code": "LE159178006FR"
        })
        response = self.user_client.post(f"/{enveloppe_id}/add", json={
            "code": "%000000087001144281996376004A10^b55d944"
        })
        data = response.json()
        aff = data["affranchissement"]
        aff["nature"] = "LINT_50"
        response = self.user_client.post(f"/{enveloppe_id}/add", json=aff)

        enveloppe = db.query(Enveloppe).filter(Enveloppe.id == enveloppe_id).first()
        service = enveloppe.service

        assert enveloppe.poids == 72
        assert len(enveloppe.affranchissements) == 2

        # Rounded price
        assert round(service.prix_affranchissements_valide(), 2) == 4.37
        assert round(service.calcul_frais_postaux_dus(), 2) == 4.37 +2.8

    def test_workflow_lot_expediteur_nouveau_casier(self, db: Session, admin_user, site, expediteur) -> None:
        """
        Test le workflow d'attribution d'un nouveau casier à un lot expéditeur via l'API.
        
        1. Créer des casiers disponibles
        2. Créer des enveloppes frauduleuses pour un expéditeur (ce qui crée un lot automatiquement)
        3. Demander un nouveau casier via l'API
        4. Ajouter une nouvelle enveloppe frauduleuse
        5. Vérifier que le lot a bien 2 casiers et que chaque casier contient l'enveloppe correspondante
        """
        from models.business import Enveloppe, LotExpediteur, Casier
        from constants.enumerations import StatutEnveloppeEnum, StatutLotExpediteurEnum
        
        # Créer des Casiers disponibles
        for i in range(5):
            casier = Casier(
                site_id=site.id,
                emplacement=f"CASIER {i}",
                numero=f"{i}"
            )
            db.add(casier)
        db.commit()
        
        # Fonction pour créer une enveloppe frauduleuse et la finaliser
        def creer_enveloppe_fraude_finaliser(expediteur_id):
            # Récupérer l'enveloppe en édition
            response = self.user_client.get("/edition")
            assert response.status_code == 200
            nouvelle_enveloppe_id = response.json()["id"]

            # Mettre à jour l'enveloppe avec l'expéditeur
            response = self.user_client.post(f"/", json={
                "id": nouvelle_enveloppe_id,
                "expediteur_id": expediteur_id,
                "site_id": site.id,
                "produit": "LV",
                "poids": 100.0,
                "surpoids": False,
                "surdimensionne": False
            })
            assert response.status_code == 200, response.json()
            
            # Ajouter un affranchissement invalide
            data = {
                "categorie": "BEAU",
                "type": "VAL",
                "prix_unite_devise": 2.50,
                "devise": "EURO",
                "quantite": 1,
                "statut": "INVALIDE"
            }
            response = self.user_client.post(f"/{nouvelle_enveloppe_id}/add", json=data)
            assert response.status_code == 200, response.json()

            # Ajouter une photo
            db.add(PhotoEnveloppe(
                format="jpg",
                qualite="haute",
                largeur=800,
                hauteur=600,
                orientation="portrait",
                enveloppe_id=nouvelle_enveloppe_id
            ))
            db.commit()
            
            # Terminer l'enveloppe
            response = self.user_client.post(f"/{nouvelle_enveloppe_id}/terminer")
            assert response.status_code == 200, response.json()
            assert response.json()["statut"] == "FRAUDULEUSE"

            return nouvelle_enveloppe_id
        
        # Appel l'API pour demander un LotExpediteur
        response = _client(admin_user, "").post(f"/lots-expediteurs?expediteur_id={expediteur.id}")
        assert response.status_code == 200, response.json()
        
        # Créer une première enveloppe frauduleuse (ce qui crée un lot automatiquement)
        premiere_enveloppe_id = creer_enveloppe_fraude_finaliser(expediteur_id=expediteur.id)
        
        # Récupérer le lot créé automatiquement
        lots = db.query(LotExpediteur).filter(LotExpediteur.expediteur_id == expediteur.id).all()
        assert len(lots) == 1
        lot = lots[0]
        
        # Vérifier que le lot a un casier
        assert lot.casier_id is not None
        premier_casier_id = lot.casier_id
        assert lot.casiers == [lot.casier]
        
        # Vérifier que l'enveloppe est dans le casier
        premiere_enveloppe = db.query(Enveloppe).filter(Enveloppe.id == premiere_enveloppe_id).first()
        assert premiere_enveloppe.casier_id == premier_casier_id
        
        # Demander un nouveau casier via l'API (en utilisant le client admin)
        response = _client(admin_user, "").post(f"/lots-expediteurs/{lot.id}/nouveau-casier")
        assert response.status_code == 200, response.json()
        
        # Vérifier que le lot a un nouveau casier
        db.refresh(lot)
        assert lot.casier_id is not None
        assert lot.casier_id != premier_casier_id
        nouveau_casier_id = lot.casier_id

        assert len(lot.casiers) == 2
        
        # Créer une deuxième enveloppe frauduleuse
        deuxieme_enveloppe_id = creer_enveloppe_fraude_finaliser(expediteur_id=expediteur.id)
        
        # Vérifier que la deuxième enveloppe est dans le nouveau casier
        deuxieme_enveloppe = db.query(Enveloppe).filter(Enveloppe.id == deuxieme_enveloppe_id).first()
        assert deuxieme_enveloppe.casier_id == nouveau_casier_id
        
        # Vérifier que le lot a toujours le même nombre d'enveloppes
        db.refresh(lot)
        assert len(lot.enveloppes) == 2
        
        # Vérifier que les enveloppes sont dans les bons casiers
        enveloppes_premier_casier = db.query(Enveloppe).filter(Enveloppe.casier_id == premier_casier_id).all()
        assert len(enveloppes_premier_casier) == 1
        assert enveloppes_premier_casier[0].id == premiere_enveloppe_id
        
        casier_1 = db.query(Casier).filter(Casier.id == premier_casier_id).first()
        assert(casier_1.lot_expediteur_id == lot.id)
        assert len(casier_1.enveloppes) == 1
        assert casier_1.enveloppes[0].id == premiere_enveloppe_id
        
        # 2eme enveloppe / 2 eme casier
        enveloppes_nouveau_casier = db.query(Enveloppe).filter(Enveloppe.casier_id == nouveau_casier_id).all()
        assert len(enveloppes_nouveau_casier) == 1
        assert enveloppes_nouveau_casier[0].id == deuxieme_enveloppe_id

        casier_2 = db.query(Casier).filter(Casier.id == nouveau_casier_id).first()
        assert len(casier_2.enveloppes) == 1
        assert casier_2.lot_expediteur_id == lot.id
        assert casier_2.enveloppes[0].id == deuxieme_enveloppe_id

        # Le lot a donc 2 casiers
        assert lot.casiers == [casier_1, casier_2]
    
        # # On met le Lot en ANNULE, doit relacher les casiers, enveloppes etc
        # response = _client(admin_user, "").patch(f"/lots-expediteurs/{lot.id}/statut?statut={StatutLotExpediteurEnum.ANNULE.value}")
        # assert response.status_code == 200, response.json()
        
        # # Vérifier que le lot a bien été mis à jour
        # db.refresh(lot)
        # assert lot.statut == StatutLotExpediteurEnum.ANNULE
        # assert lot.casier == None

        # # Vérifier que les casiers ont été relâchés
        # db.refresh(casier_1)
        # db.refresh(casier_2)
        # assert casier_1.lot_expediteur is None
        # assert casier_2.lot_expediteur is None
        # assert casier_1.statut == StatutCasierEnum.DISPONIBLE
        # assert casier_2.statut == StatutCasierEnum.DISPONIBLE
        # assert len(casier_1.enveloppes) == 0
        # assert len(casier_2.enveloppes) == 0

    def test_completed_at_is_set_on_first_status_change(self, db: Session, enveloppe_complete: Enveloppe) -> None:
        """Test que completed_at est défini lors du premier changement de statut depuis EDITION"""
        # Vérifier que l'enveloppe est en EDITION et que completed_at est None
        assert enveloppe_complete.statut == StatutEnveloppeEnum.EDITION
        assert enveloppe_complete.completed_at is None

        # Ajouter un premier affranchissement via l'API
        data = {
            "categorie": "MARI",
            "type": "VAL",
            "prix_unite_devise": 1.26,
            "devise": "EURO",
            "quantite": 1
        }
        response = self.user_client.post(f"/{enveloppe_complete.id}/add", json=data)
        assert response.status_code == 200, response.json()

        # Finaliser l'enveloppe
        response = self.user_client.post(f"/{enveloppe_complete.id}/terminer")
        assert response.status_code == 200, response.json()

        # Vérifier que le statut a changé et que completed_at est défini
        db.refresh(enveloppe_complete)
        assert enveloppe_complete.statut != StatutEnveloppeEnum.EDITION
        assert enveloppe_complete.completed_at is not None

        # Sauvegarder la date de complétion
        first_completed_at = enveloppe_complete.completed_at

        # Ajouter un deuxième affranchissement via l'API
        data = {
            "categorie": "MARI",
            "type": "VAL",
            "prix_unite_devise": 2.50,
            "devise": "EURO",
            "quantite": 1
        }
        response = self.user_client.post(f"/{enveloppe_complete.id}/add", json=data)
        assert response.status_code == 200, response.json()

        # Terminer à nouveau l'enveloppe
        response = self.user_client.post(f"/{enveloppe_complete.id}/terminer")
        assert response.status_code == 200, response.json()

        # Vérifier que completed_at n'a pas changé
        db.refresh(enveloppe_complete)
        assert enveloppe_complete.completed_at == first_completed_at

    def test_reassignation_enveloppe_apres_changement_expediteur(self, db: Session, user, site) -> None:
        """
        Test la réassignation d'une enveloppe à un nouveau lot après changement d'expéditeur.
        
        1. Créer deux expéditeurs A et B
        2. Créer un lot pour l'expéditeur A avec un casier
        3. Ajouter deux enveloppes frauduleuses pour l'expéditeur A dans ce lot
        4. Modifier la deuxième enveloppe pour changer l'expéditeur A vers B
        5. Vérifier que l'enveloppe est sortie du lot et du casier de l'expéditeur A
        """
        from models.business import Enveloppe, LotExpediteur, Casier, Expediteur
        from constants.enumerations import StatutEnveloppeEnum, StatutLotExpediteurEnum
        
        # Créer deux expéditeurs
        expediteur_a = Expediteur(nom="Expediteur A", email="<EMAIL>")
        expediteur_b = Expediteur(nom="Expediteur B", email="<EMAIL>")
        db.add_all([expediteur_a, expediteur_b])
        db.commit()
        db.refresh(expediteur_a)
        db.refresh(expediteur_b)
        
        # Créer des casiers disponibles
        casier = Casier(
            site_id=site.id,
            emplacement="CASIER TEST",
            numero="TEST"
        )
        db.add(casier)
        db.commit()
        
        # Créer un lot pour l'expéditeur A
        lot_expediteur_a = LotExpediteur(
            expediteur_id=expediteur_a.id,
            statut=StatutLotExpediteurEnum.OUVERT,
            site_id=site.id,
            casier=casier
        )
        db.add(lot_expediteur_a)
        db.commit()
        db.refresh(lot_expediteur_a)

        casier.lot_expediteur_id = lot_expediteur_a.id
        db.add(casier)
        db.commit()
        
        # Fonction pour créer et finaliser une enveloppe frauduleuse
        def creer_enveloppe_frauduleuse(expediteur_id):
            # Récupérer l'enveloppe en édition
            response = self.user_client.get("/edition")
            assert response.status_code == 200
            enveloppe_id = response.json()["id"]
            
            # Mettre à jour l'enveloppe avec l'expéditeur
            response = self.user_client.post("/", json={
                "id": enveloppe_id,
                "expediteur_id": expediteur_id,
                "site_id": site.id,
                "produit": "LV",
                "poids": 100.0,
                "surpoids": False,
                "surdimensionne": False
            })
            assert response.status_code == 200
            
            # Ajouter un affranchissement invalide
            data = {
                "categorie": "BEAU",
                "type": "VAL",
                "prix_unite_devise": 2.50,
                "devise": "EURO",
                "quantite": 1,
                "statut": "INVALIDE"
            }
            response = self.user_client.post(f"/{enveloppe_id}/add", json=data)
            assert response.status_code == 200
            
            # Ajouter une photo
            db.add(PhotoEnveloppe(
                format="jpg",
                qualite="haute",
                largeur=800,
                hauteur=600,
                orientation="portrait",
                enveloppe_id=enveloppe_id
            ))
            db.commit()
            
            # Terminer l'enveloppe
            response = self.user_client.post(f"/{enveloppe_id}/terminer")
            assert response.status_code == 200
            assert response.json()["statut"] == "FRAUDULEUSE"
            
            return enveloppe_id
        
        # Créer deux enveloppes frauduleuses pour l'expéditeur A
        enveloppe1_id = creer_enveloppe_frauduleuse(expediteur_a.id)
        enveloppe2_id = creer_enveloppe_frauduleuse(expediteur_a.id)
        
        # Vérifier que les deux enveloppes sont dans le lot et le casier de l'expéditeur A
        enveloppe1 = db.query(Enveloppe).filter(Enveloppe.id == enveloppe1_id).first()
        enveloppe2 = db.query(Enveloppe).filter(Enveloppe.id == enveloppe2_id).first()
        
        assert enveloppe1.lot_expediteur_id == lot_expediteur_a.id
        assert enveloppe2.lot_expediteur_id == lot_expediteur_a.id
        assert enveloppe1.casier_id == casier.id
        assert enveloppe2.casier_id == casier.id
        
        # Vérifier l'état initial du lot
        db.refresh(lot_expediteur_a)
        assert len(lot_expediteur_a.enveloppes) == 2

        # Le Lot est ouvert donc modifiable
        assert lot_expediteur_a.statut == StatutLotExpediteurEnum.OUVERT
        
        # Modifier l'expéditeur de la deuxième enveloppe (de A vers B)
        response = self.user_client.post("/", json={
            "id": enveloppe2_id,
            "expediteur_id": expediteur_b.id
        })
        assert response.status_code == 200
        
        # Vérifier que l'enveloppe 2 est sortie du lot et du casier de l'expéditeur A
        db.refresh(enveloppe2)
        assert enveloppe2.lot_expediteur_id != lot_expediteur_a.id
        assert enveloppe2.casier_id != casier.id
        
        # Vérifier que l'enveloppe 1 est toujours dans le lot et le casier de l'expéditeur A
        db.refresh(enveloppe1)
        assert enveloppe1.lot_expediteur_id == lot_expediteur_a.id
        assert enveloppe1.casier_id == casier.id
        
        # Vérifier que le lot de l'expéditeur A ne contient plus que l'enveloppe 1
        db.refresh(lot_expediteur_a)
        assert len(lot_expediteur_a.enveloppes) == 1
        assert lot_expediteur_a.enveloppes[0].id == enveloppe1_id
        
        # Vérifier que le casier contient toujours l'enveloppe 1
        db.refresh(casier)
        assert len(casier.enveloppes) == 1
        assert casier.enveloppes[0].id == enveloppe1_id
        assert casier.lot_expediteur is not None


        # Modifier l'expéditeur de la premiere enveloppe (de A vers B)
        response = self.user_client.post("/", json={
            "id": enveloppe1_id,
            "expediteur_id": expediteur_b.id
        })
        assert response.status_code == 200

        # Vérifier que le casier contient toujours l'enveloppe 1
        db.refresh(casier)
        assert len(casier.enveloppes) == 0

        # Le casier doit être libre 
        db.refresh(casier)

        assert casier.lot_expediteur_id is None

        # # Test via le service casier
        from services.casier import ServiceCasier
        casier_dispo = ServiceCasier().obtenir_casier_disponible(site)
        assert casier_dispo.id == casier.id

