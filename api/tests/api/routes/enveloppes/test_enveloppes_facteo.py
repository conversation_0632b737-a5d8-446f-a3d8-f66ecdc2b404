import pytest
import json
from sqlmodel import Session, select
from models.business import <PERSON>velop<PERSON>, PhotoEnveloppe
from constants.enumerations import SourceEnveloppeEnum, StatutEnveloppeEnum
from tests.conftest import client as _client
from tests.mocks.storage_service import patch_blob_operations


class TestEnveloppesFacteoAPI:

    @pytest.fixture(autouse=True)
    def setup(self, user, admin_user):
        self.client = _client(user, "/enveloppes/")
        self.guess_client = _client(None, "/enveloppes/", token="uiuofdifd")

    @patch_blob_operations()
    def test_create_enveloppe_facteo_success(self, session: Session):
        """Test la création réussie d'une enveloppe Facteo"""
        # Préparation des données
        
        files = [("photos", ("test_image_1.jpg", b"fake image content", "image/jpeg"))]
        data = {
            "poids": 150.5,
            "donnees": json.dumps(
                {"mentions":["lettre"],
                 "codema":"",
                 "destinataire":{
                     "nom": "",
                     "adresse": "30  RUE DE LA NATION",
                     "ville": "ISSOUDUN",
                     "code_postal": "36"
                 },
                 "expediteur": {
                     "nom": "MR CHAULIER JEAN",
                     "adresse": "5 LIEU DIT LE PLESSIS",
                     "ville": "CHAUZE SUR LOIRE",
                     "code_postal": "37140"
                 }                
                 
                 }
            )
        }
        
        # Exécution de la requête
        response = self.client.post(
            "/facteo", 
            files=files,
            data=data
        )
        
        # Vérification des résultats
        assert response.status_code == 200, response.json()
        response_data = response.json()
        
        # Vérification que l'enveloppe a été créée en base
        enveloppe = session.exec(
            select(Enveloppe).where(Enveloppe.id == response_data["id"])
        ).first()
        
        assert enveloppe is not None
        assert enveloppe.source == SourceEnveloppeEnum.FACTEO
        assert enveloppe.poids == 150.5
        assert enveloppe.statut == StatutEnveloppeEnum.EDITION

        # Verification Expediteur
        assert enveloppe.expediteur is not None
        assert enveloppe.expediteur.nom == "MR CHAULIER JEAN"
        assert enveloppe.expediteur.adresse == "5 LIEU DIT LE PLESSIS"
        assert enveloppe.expediteur.ville == "CHAUZE SUR LOIRE"
        assert enveloppe.expediteur.code_postal == "37140"
        
        # Vérification que la photo a été créée
        photos = session.exec(
            select(PhotoEnveloppe).where(PhotoEnveloppe.enveloppe_id == enveloppe.id)
        ).all()
        
        assert len(photos) == 1
        assert photos[0].format == "image/jpeg"
        assert "test" in photos[0].url
        # Vérification que le service de stockage a été appelé
        # mock_storage_service.assert_called_once()
    
    def test_create_enveloppe_facteo_failure(self, session: Session):
        """Test la création échouée d'une enveloppe Facteo"""
        # Préparation des données
        files = [("photos", ("test_image_1.jpg", b"fake image content", "image/jpeg"))]
        data = {
            "poids": 150.5,
            "donnees": json.dumps({"code_postal": "75001", "type": "lettre"})
        }
        
        # Exécution de la requête
        response = self.guess_client.post(
            "/facteo", 
            files=files,
            data=data
        )
        
        # Vérification des résultats
        assert response.status_code == 403, response.json()
    