import pytest
from sqlmodel import Session, select
from models import Destination
from tests.conftest import client as _client
from tests.fixtures.destinations import create_destination

class TestDestinationsAPI:

    @pytest.fixture(autouse=True)
    def setup(self, user, admin_user):
        self.client = _client(user, "/destinations/")
        self.super_client = _client(admin_user, "/destinations/")

    def test_create_destination(self, db: Session) -> None:
        data = {"nom_fr": "Allemagne", "nom_en": "Germany", "alpha2": "DE", "alpha3": "DEU"}
        r = self.super_client.post("/", json=data)
        assert 200 <= r.status_code < 300
        created_destination = r.json()
        destination = db.exec(select(Destination).where(Destination.id == created_destination["id"])).first()
        assert destination
        assert destination.nom_fr == data["nom_fr"]

    def test_read_destinations(self, db: Session) -> None:
        r = self.super_client.get("/")
        assert 200 <= r.status_code < 300
        destinations = r.json()
        assert isinstance(destinations, list)
        
    def test_read_destination(self, db: Session, destination: Destination) -> None:
        r = self.super_client.get(f"/{destination.id}")
        assert 200 <= r.status_code < 300
        api_destination = r.json()
        assert api_destination["nom_fr"] == destination.nom_fr

    def test_update_destination(self, db: Session, destination: Destination) -> None:
        data = {"nom_fr": "Updated Name"}
        r = self.super_client.put(f"/{destination.id}", json=data)
        assert 200 <= r.status_code < 300
        updated_destination = r.json()
        assert updated_destination["nom_fr"] == data["nom_fr"]

    def test_delete_destination(self, db: Session, destination: Destination) -> None:
        r = self.super_client.delete(f"/{destination.id}")
        assert 200 <= r.status_code < 300
        deleted_destination = r.json()
        assert deleted_destination["id"] == destination.id
        result = db.exec(select(Destination).where(Destination.id == destination.id)).first()
        assert result is None 