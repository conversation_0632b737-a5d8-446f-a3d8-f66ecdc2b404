import pytest
from sqlmodel import Session
from tests.conftest import client as _client
from constants.enumerations import ProduitEnum
from constants.affranchissements.affranchissement import CATEGORIES_AFFRANCHISSEMENTS
from constants.affranchissements.nature import NATURES_AFFRANCHISSEMENT

class TestEnumerationsAPI:

    @pytest.fixture(autouse=True)
    def setup(self, user, admin_user):
        self.client = _client(user, "/enumerations/")
        self.super_client = _client(admin_user, "/enumerations/")

    def test_get_produits(self, db: Session) -> None:
        r = self.client.get("/produits")
        assert 200 <= r.status_code < 300
        produits = r.json()
        assert isinstance(produits, list)
        assert len(produits) == len(ProduitEnum)
        for produit in produits:
            assert produit in [p.value for p in ProduitEnum]

    def test_get_affranchissements(self, db: Session) -> None:
        r = self.client.get("/affranchissements")
        assert 200 <= r.status_code < 300
        affranchissements = r.json()
        assert isinstance(affranchissements, list)
        assert len(affranchissements) == len(CATEGORIES_AFFRANCHISSEMENTS)

        assert "informations" in affranchissements[0]["types_affranchissements"][0]
        assert "champs_requis" in affranchissements[0]["types_affranchissements"][0]["informations"]

    def test_get_natures(self, db: Session) -> None:
        r = self.client.get("/natures")
        assert 200 <= r.status_code < 300
        natures = r.json()
        assert isinstance(natures, list)
        assert len(natures) == len(NATURES_AFFRANCHISSEMENT) 

        # Vérifie que les IDs des natures correspondent exactement à ceux de NATURES_AFFRANCHISSEMENT
        nature_ids = {nature["id"] for nature in natures}
        expected_ids = {str(nature.id).split(".")[-1] for nature in NATURES_AFFRANCHISSEMENT.values()}
        assert nature_ids == expected_ids

        lv_20 = [n for n in natures if n["id"] == "LV_20"][0]
        assert lv_20["prix_unite"] == 1.35