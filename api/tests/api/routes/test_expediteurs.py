import pytest
from sqlmodel import Session, select
from models import Expediteur
from tests.conftest import client as _client
from tests.fixtures.expediteurs import create_expediteur

class TestExpediteursAPI:

    @pytest.fixture(autouse=True)
    def setup(self, user, admin_user):
        self.client = _client(user, "/expediteurs/")
        self.super_client = _client(admin_user, "/expediteurs/")

    def test_create_expediteur(self, db: Session) -> None:
        data = {"nom": "Test Expediteur", "code": "EXP123"}
        r = self.super_client.post("/", json=data)
        assert 200 <= r.status_code < 300
        created_expediteur = r.json()
        expediteur = db.exec(select(Expediteur).where(Expediteur.id == created_expediteur["id"])).first()
        assert expediteur
        assert expediteur.nom == data["nom"]

    def test_read_expediteurs(self, db: Session) -> None:
        r = self.super_client.get("/")
        assert 200 <= r.status_code < 300
        expediteurs = r.json()
        assert isinstance(expediteurs, list)
        
    def test_read_expediteurs(self, db: Session) -> None:
        # Delete all existing expediteurs
        db.query(Expediteur).delete()

        # Create test expediteurs
        expediteur1 = create_expediteur(db=db, name="Lucas VAGNIER")
        expediteur2 = create_expediteur(db=db, name="Lois Expediteur")
        
        # Test without search
        r = self.super_client.get("/")
        assert 200 <= r.status_code < 300
        expediteurs = r.json()
        assert isinstance(expediteurs, list)
        assert len(expediteurs) >= 2

        # Test with search by name
        r = self.super_client.get("/?search=Luc")
        assert 200 <= r.status_code < 300
        expediteurs = r.json()
        assert len(expediteurs) == 1
        assert expediteurs[0]["id"] == expediteur1.id
        
        r = self.super_client.get("/?search=Lois")
        assert 200 <= r.status_code < 300
        expediteurs = r.json()
        assert len(expediteurs) == 1
        assert expediteurs[0]["id"] == expediteur2.id

        # Test pagination
        r = self.super_client.get("/?skip=1&limit=1")
        assert 200 <= r.status_code < 300
        expediteurs = r.json()
        assert len(expediteurs) == 1

    def test_read_expediteur(self, db: Session, expediteur: Expediteur) -> None:
        r = self.super_client.get(f"/{expediteur.id}")
        assert 200 <= r.status_code < 300
        api_expediteur = r.json()
        assert api_expediteur["nom"] == expediteur.nom

    def test_update_expediteur(self, db: Session, expediteur: Expediteur) -> None:
        data = {"nom": "Updated Name"}
        r = self.super_client.put(f"/{expediteur.id}", json=data)
        assert 200 <= r.status_code < 300
        updated_expediteur = r.json()
        assert updated_expediteur["nom"] == data["nom"]

    def test_delete_expediteur(self, db: Session, expediteur: Expediteur) -> None:
        r = self.super_client.delete(f"/{expediteur.id}")
        assert 200 <= r.status_code < 300
        deleted_expediteur = r.json()
        assert deleted_expediteur["id"] == expediteur.id
        result = db.exec(select(Expediteur).where(Expediteur.id == expediteur.id)).first()
        assert result is None

    def test_search_expediteurs(self, db: Session) -> None:
        """Test la recherche d'expéditeurs par nom ou adresse"""
        # Supprimer tous les expéditeurs existants
        db.query(Expediteur).delete()
        db.commit()
        
        # Créer des expéditeurs de test
        expediteur1 = Expediteur(
            nom="Martin Dupont",
            adresse="123 Rue de Paris",
            ville="Lyon",
            code_postal="69001"
        )
        expediteur2 = Expediteur(
            nom="Jean Martin",
            adresse="456 Avenue des Champs",
            ville="Paris",
            code_postal="75008"
        )
        expediteur3 = Expediteur(
            nom="Sophie Dubois",
            adresse="789 Boulevard Central",
            ville="Lyon",
            code_postal="69002"
        )
        
        db.add(expediteur1)
        db.add(expediteur2)
        db.add(expediteur3)
        db.commit()
        
        # Test recherche par nom uniquement
        data = {"nom": "Martin"}
        r = self.super_client.post("/search", json=data)
        assert 200 <= r.status_code < 300
        results = r.json()
        assert len(results) == 2
        assert any(exp["id"] == expediteur1.id for exp in results)
        assert any(exp["id"] == expediteur2.id for exp in results)
        
        # Test recherche par adresse complète
        data = {
            "adresse": "123 Rue de Paris",
            "ville": "Lyon",
            "code_postal": "69001"
        }
        r = self.super_client.post("/search", json=data)
        assert 200 <= r.status_code < 300
        results = r.json()
        print(results)
        assert len(results) == 1
        assert results[0]["id"] == expediteur1.id
        
        # Test recherche par adresse incomplète (devrait échouer)
        data = {
            "ville": "Lyon",
            "code_postal": "69001"
        }
        r = self.super_client.post("/search", json=data)
        assert r.status_code == 400  # Validation error
        
        # Test recherche combinée (nom et adresse complète)
        data = {
            "nom": "Sophie",
            "adresse": "789 Boulevard Central",
            "ville": "Lyon",
            "code_postal": "69002"
        }
        r = self.super_client.post("/search", json=data)
        assert 200 <= r.status_code < 300
        results = r.json()
        assert len(results) == 1
        assert results[0]["id"] == expediteur3.id
        
        # Test recherche sans résultat
        data = {"nom": "Inconnu"}
        r = self.super_client.post("/search", json=data)
        assert 200 <= r.status_code < 300
        results = r.json()
        assert len(results) == 0
        
        # Test recherche sans paramètres
        data = {}
        r = self.super_client.post("/search", json=data)
        assert 200 <= r.status_code < 300
        results = r.json()
        assert len(results) == 0
