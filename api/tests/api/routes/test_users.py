from unittest.mock import patch
from sqlmodel import Session, select
import crud
from core.config import settings
from core.security import verify_password
from models import User, UserCreate
from tests.utils.utils import random_email, random_lower_string
from tests.conftest import client as _client
from tests.fixtures.users import user
import pytest
from models import Site

class TestUsers:
    @pytest.fixture(autouse=True)
    def setup(self, user, admin_user):
        self.user_client = _client(user, "")
        self.admin_user_client = _client(admin_user, "")

    def test_get_users_superuser_me(self, admin_user) -> None:
        r = self.admin_user_client.get("/users/me")
        current_user = r.json()
        assert current_user
        assert current_user["is_active"] is True
        assert current_user["role"] == "admin"
        assert current_user["email"] == admin_user.email

    def test_get_users_normal_user_me(self, user) -> None:

        r = self.user_client.get("/users/me")
        current_user = r.json()
        assert current_user
        assert current_user["is_active"] is True
        assert current_user["role"] == "user"
        assert current_user["email"] == user.email
        assert current_user["site_id"] == user.site_id

    def test_create_user_new_email(self, db: Session) -> None:

        with (
            patch("app.utils.send_email", return_value=None),
            patch("app.core.config.settings.SMTP_HOST", "smtp.example.com"),
            patch("app.core.config.settings.SMTP_USER", "<EMAIL>"),
        ):
            username = random_email()
            password = random_lower_string()
            data = {"email": username, "password": password}
            r = self.admin_user_client.post(
                "/users/",
                json=data,
            )
            assert 200 <= r.status_code < 300
            created_user = r.json()
            user = crud.get_user_by_email(session=db, email=username)
            assert user
            assert user.email == created_user["email"]

    def test_get_existing_user(self, user) -> None:

        r = self.admin_user_client.get(
            f"/users/{user.id}"
        )

        assert 200 <= r.status_code < 300
        api_user = r.json()
        assert user.email == api_user["email"]

    def test_get_existing_user_current_user(self, user) -> None:
        r = self.user_client.get(
            f"/users/{user.id}",
        )
        assert 200 <= r.status_code < 300
        api_user = r.json()

        assert user.email == api_user["email"]

    def test_get_existing_user_permissions_error(self, db: Session) -> None:
        r = self.user_client.get(
            "/users/999999",
        )
        assert r.status_code == 403
        assert r.json() == {"detail": "The user doesn't have enough privileges"}

    def test_create_user_existing_username(self, db: Session, user) -> None:

        data = {"email": user.email, "password": "allamlamal"}
        r = self.admin_user_client.post(
            "/users/",
            json=data,
        )
        created_user = r.json()
        assert r.status_code == 400
        assert "_id" not in created_user

    def test_create_user_by_normal_user(self, db: Session) -> None:
        username = random_email()
        password = random_lower_string()
        data = {"email": username, "password": password}
        r = self.user_client.post(
            "/users/",
            json=data,
        )
        assert r.status_code == 403

    def test_retrieve_users(self, db: Session) -> None:
        r = self.admin_user_client.get("/users/")
        all_users = r.json()
        
        assert len(all_users["items"]) > 1
        assert "total_items" in all_users
        for item in all_users["items"]:
            assert "email" in item

    def test_retrieve_users_by_normal_user(self, db: Session) -> None:
        r = self.user_client.get("/users/")
        data = r.json()

        assert data == {'detail': "L'utilisateur n'a pas les privilèges suffisants : admin"}

    def test_update_user_me(self, db: Session, user) -> None:

        new_full_name = "Updated Name"
        new_email = random_email()
        data = {"full_name": new_full_name, "email": new_email}
        r = self.user_client.patch(
            "/users/me",
            json=data,
        )
        assert r.status_code == 200
        updated_user = r.json()
        assert updated_user["email"] == new_email
        assert updated_user["full_name"] == new_full_name

        assert user.email == new_email
        assert user.full_name == new_full_name

    def test_update_password_me(self, db: Session, user) -> None:

        new_password = random_lower_string()

        data = {
            "current_password": "thisisstrong",
            "new_password": new_password,
        }
        r = self.user_client.patch(
            "/users/me/password",
            json=data,
        )
        assert r.status_code == 200
        updated_user = r.json()
        assert updated_user["message"] == "Password updated successfully"

        assert verify_password(new_password, user.hashed_password)


    def test_update_password_me_incorrect_password(self) -> None:
        new_password = random_lower_string()
        data = {"current_password": new_password, "new_password": new_password}
        r = self.user_client.patch(
            "/users/me/password",
            json=data,
        )
        assert r.status_code == 400
        updated_user = r.json()
        assert updated_user["detail"] == "Incorrect password"

    def test_update_user_me_email_exists(self, db: Session, site) -> None:
        # Create a user whose email we'll use
        existing_email = random_email()
        password = random_lower_string()
        user_in = UserCreate(email=existing_email, password=password, site_id=site.id)
        user = crud.create_user(session=db, user_create=user_in)


        data = {"email": existing_email}
        r = self.user_client.patch(
            "/users/me",
            json=data,
        )
        assert r.status_code == 409
        assert r.json()["detail"] == "User with this email already exists"

    def test_update_password_me_same_password_error(self) -> None:

        data = {
            "current_password": "thisisstrong",
            "new_password": "thisisstrong",
        }
        r = self.admin_user_client.patch(
            "/users/me/password",
            json=data,
        )
        assert r.status_code == 400
        updated_user = r.json()
        assert (
                updated_user["detail"]
                == "New password cannot be the same as the current one"
        )

    # def test_register_user(self, db: Session) -> None:
    #     with patch("core.config.settings.USERS_OPEN_REGISTRATION", True):
    #         email = random_email()
    #         password = random_lower_string()
    #         data = {"email": email, "password": password}
    #         r = self.user_client.post(
    #             "/users/signup",
    #             json=data,
    #         )
    #         assert r.status_code == 200, r.json()
    #         created_user = r.json()
    #         assert created_user["email"] == email
    #         user_query = select(User).where(User.email == email)
    #         user_db = db.exec(user_query).first()
    #         assert user_db
    #         assert user_db.email == email
    #         assert verify_password(password, user_db.hashed_password)

    def test_register_user_forbidden_error(self) -> None:
        with patch("core.config.settings.USERS_OPEN_REGISTRATION", False):
            username = random_email()
            password = random_lower_string()
            full_name = random_lower_string()
            data = {"email": username, "password": password, "full_name": full_name}
            r = self.user_client.post(
                "/users/signup",
                json=data,
            )
            assert r.status_code == 403
            assert (
                    r.json()["detail"]
                    == "Open user registration is forbidden on this server"
            )

    def test_register_user_already_exists_error(self, db: Session, site) -> None:
        with patch("core.config.settings.USERS_OPEN_REGISTRATION", True):
            password = random_lower_string()
            full_name = random_lower_string()

            user_in = UserCreate(
                email=settings.FIRST_SUPERUSER, password=password, site_id=site.id
            )
            crud.create_user(session=db, user_create=user_in)

            data = {
                "email": settings.FIRST_SUPERUSER,
                "password": password,
                "full_name": full_name,
                "site_id": site.id
            }

            r = self.user_client.post(
                "/users/signup",
                json=data,
            )
            assert r.status_code == 400
            assert (
                    r.json()["detail"]
                    == "The user with this email already exists in the system"
            )

    def test_update_user(self, user) -> None:
        data = {"full_name": "Updated_full_name"}
        r = self.admin_user_client.patch(
            f"/users/{user.id}",
            json=data
        )
        assert r.status_code == 200
        updated_user = r.json()

        assert updated_user["full_name"] == "Updated_full_name"
        assert user.full_name == "Updated_full_name"

    def test_update_user_not_exists(self) -> None:

        data = {"full_name": "Updated_full_name"}
        r = self.admin_user_client.patch(
            "/users/99999999",
            json=data,
        )
        assert r.status_code == 404
        assert r.json()["detail"] == "The user with this id does not exist in the system"

    def test_update_user_email_exists(self, db: Session, site) -> None:
        username = random_email()
        password = random_lower_string()
        user_in = UserCreate(email=username, password=password, site_id=site.id)
        user = crud.create_user(session=db, user_create=user_in)

        username2 = random_email()
        password2 = random_lower_string()
        user_in2 = UserCreate(email=username2, password=password2, site_id=site.id)
        user2 = crud.create_user(session=db, user_create=user_in2)

        data = {"email": user2.email}
        r = self.admin_user_client.patch(
            f"/users/{user.id}",
            json=data,
        )
        assert r.status_code == 409
        assert r.json()["detail"] == "User with this email already exists"

    def test_delete_user_me(self, db: Session, user) -> None:

        r = self.user_client.delete(
            "/users/me",
        )
        assert r.status_code == 200
        deleted_user = r.json()
        assert deleted_user["message"] == "User deleted successfully"

        result = db.exec(select(User).where(User.id == user.id)).first()
        assert result is None

    def test_delete_user_me_as_superuser(self) -> None:

        r = self.admin_user_client.delete(
            "/users/me"
        )
        assert r.status_code == 403
        response = r.json()
        assert response["detail"] == "Super users are not allowed to delete themselves"

    def test_delete_user_admin_user(self, db: Session, site) -> None:

        username = random_email()
        password = random_lower_string()
        user_in = UserCreate(email=username, password=password, site_id=site.id)
        user = crud.create_user(session=db, user_create=user_in)
        user_id = user.id
        r = self.admin_user_client.delete(
            f"/users/{user_id}",
        )
        assert r.status_code == 200
        deleted_user = r.json()
        assert deleted_user["message"] == "User deleted successfully"
        result = db.exec(select(User).where(User.id == user_id)).first()
        assert result is None

    def test_delete_user_not_found(self) -> None:
        r = self.admin_user_client.delete(
            "/users/99999999",
        )
        assert r.status_code == 404
        assert r.json()["detail"] == "User not found"

    def test_delete_user_current_admin_user_error(self, admin_user) -> None:
        r = self.admin_user_client.delete(
            f"/users/{admin_user.id}"
        )
        assert r.status_code == 403
        assert r.json()["detail"] == "Super users are not allowed to delete themselves"

    def test_delete_user_without_privileges(self, db: Session, site) -> None:

        # Create another user to attempt to delete
        username = random_email()
        password = random_lower_string()
        user_in = UserCreate(email=username, password=password, site_id=site.id)
        user = crud.create_user(session=db, user_create=user_in)

        r = self.user_client.delete(
            f"/users/{user.id}"
        )
        assert r.status_code == 403
        assert r.json()["detail"] == "L'utilisateur n'a pas les privilèges suffisants : admin"

    def test_generate_qrcode(self, user) -> None:
        """
        Teste la génération d'un QR code pour un utilisateur.
        """
        # Ajouter un token_ajout_photo à l'utilisateur pour le test
        user.token_ajout_photo = "test_token_123"
        
        # Faire la requête pour générer le QR code
        r = self.user_client.get(
            f"/users/me/qrcode"
        )
        
        # Vérifier que la requête a réussi
        assert r.status_code == 200
        
        # Vérifier que le type de contenu est bien une image PNG
        assert r.headers["content-type"] == "image/png"
        
        # Vérifier que la réponse contient des données
        assert len(r.content) > 0

    def test_self_user_change_site(self, db: Session, user, site) -> None:
        """
        Teste si un utilisateur peut changer son site_id.
        """
        # Tentative de mise à jour du site_id de l'utilisateur
        new_site = Site(name="Test Site")
        db.add(new_site)
        db.commit()
        db.refresh(new_site)

        data = {"site_id": new_site.id}
        r = self.user_client.patch(
            "/users/me",
            json=data,
        )
        # Vérifie que l'opération est interdite
        assert r.status_code == 200
        user = db.exec(select(User).where(User.id == user.id)).first()
        assert user.site_id != new_site.id

    def test_admin_change_user_site(self, db: Session, user, site) -> None:
        """
        Teste si un administrateur peut changer le site_id d'un utilisateur.
        """
        new_site = Site(nom="Test Site")
        db.add(new_site)
        db.commit()
        db.refresh(new_site)

        data = {"site_id": new_site.id}
        r = self.admin_user_client.patch(
            f"/users/{user.id}",
            json=data,
        )
        assert r.status_code == 200
        updated_user = r.json()
        assert updated_user["site_id"] == new_site.id
        assert user.site_id == new_site.id
