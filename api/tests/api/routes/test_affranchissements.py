import pytest
from sqlmodel import Session, select
from models.public import AjoutAffranchissement
from tests.conftest import client as _client
from tests.fixtures.users import user, admin_user
from unittest.mock import patch, MagicMock
from tests.mocks.ssu_api import patch_ssu_tracking

class TestAffranchissementsAPI:
    
    @pytest.fixture(autouse=True)
    def setup(self, user, admin_user):
        self.client = _client(user, "/affranchissements/")
        self.super_client = _client(admin_user, "/affranchissements/")
    
    @patch_ssu_tracking(finalized=False)
    def test_valider_affranchissement_valide(self, ssu_mock):
        """
        Teste la validation d'un affranchissement valide.
        """
        from tests.fixtures.codes import CODE_S10

        data = {
            "code": CODE_S10
        }
        
        # Appel de l'API
        response = self.client.post("/verifier", json=data)
        
        # Vérifications
        assert response.status_code == 200
        result = response.json()
        assert result["is_valid"] == True

    @patch_ssu_tracking(finalized=True)
    def test_valider_affranchissement_invalide(self, ssu_mock):
        """
        Teste la validation d'un affranchissement valide.
        """
        from tests.fixtures.codes import CODE_S10

        data = {
            "code": "LL014699171UK"
        }
        
        # Appel de l'API
        response = self.client.post("/verifier", json=data)
        
        # Vérifications
        assert response.status_code == 200
        result = response.json()
        assert result["is_valid"] == False