import pytest
from unittest.mock import patch, MagicMock
from sqlmodel import Session
from fastapi.testclient import TestClient
from models.business import LotExpediteur, TransactionPaiementLotExpediteur
from models.encaissement import MoyenPaiement, MoyenPaiementSelectionne
from constants.enumerations import StatutLotExpediteurEnum, StatutPaiementEnum
from tests.conftest import client as _client
from services.api_laposte.encaissement import ServiceEncaissement


class TestEncaissementsAPI:
    """
    Tests d'intégration pour le workflow d'encaissement des lots expéditeurs fraudeurs.
    Simule l'ensemble du processus d'encaissement avec mocking des API externes.
    """

    @pytest.fixture(autouse=True)
    def setup(self, admin_user):
        """Configuration des clients de test"""
        self.client = _client(None, "/lots-expediteurs/")

    @patch.object(ServiceEncaissement, 'get_moyens_paiement')
    def test_workflow_encaissement_complet_succes(
        self, 
        mock_get_moyens_paiement,
        db: Session, 
        lot_expediteur_avec_enveloppes_frauduleuses: LotExpediteur
    ):
        """
        Test du workflow complet d'encaissement avec succès du paiement.
        
        Étapes testées :
        1. Visualisation du lot par l'expéditeur
        2. Initiation du paiement et récupération des moyens de paiement
        3. Sélection d'un moyen de paiement et effectuation du paiement
        4. Vérification des mises à jour en base de données
        """
        lot = lot_expediteur_avec_enveloppes_frauduleuses
        paiement = lot.paiements[0]
        
        # Mock des moyens de paiement disponibles
        moyens_paiement_mock = [
            MoyenPaiement(
                nom="carte_bancaire",
                url="/payment/card",
                label="Carte bancaire",
                authorization_token="auth_token_123",
                transaction_id="trans_123"
            ),
            MoyenPaiement(
                nom="paypal",
                url="/payment/paypal", 
                label="PayPal",
                authorization_token="auth_token_456",
                transaction_id="trans_456"
            )
        ]
        mock_get_moyens_paiement.return_value = moyens_paiement_mock
        
        # ÉTAPE 1: Visualisation du lot par l'expéditeur
        response = self.client.get(f"/{lot.id_public}/visualiser")
        assert response.status_code == 200, response.json()
        
        lot_data = response.json()
        assert lot_data["id_public"] == lot.id_public
        assert lot_data["statut"] == StatutLotExpediteurEnum.NOTIFIE.value
        assert len(lot_data["paiements"]) == 1
        assert lot_data["paiements"][0]["statut"] == StatutPaiementEnum.NON_INITIALISE.value
        
        # ÉTAPE 2: Initiation du paiement
        response = self.client.post(f"/{lot.id_public}/initier_paiement/{paiement.id_paiement}")
        assert response.status_code == 200
        
        moyens_disponibles = response.json()
        assert len(moyens_disponibles) == 2
        assert moyens_disponibles[0]["nom"] == "carte_bancaire"
        assert moyens_disponibles[1]["nom"] == "paypal"
        
        # Vérifier que la méthode mockée a été appelée
        mock_get_moyens_paiement.assert_called_once()
        
        # ÉTAPE 3: Sélection et paiement avec carte bancaire
        with patch.object(ServiceEncaissement, 'payer') as mock_payer:
            # Mock de la réponse de paiement réussi
            donnees_paiement_mock = {
                "transaction_id": "TXN_SUCCESS_789",
                "status": "AUTHORIZED",
                "payment_url": "https://payment.example.com/redirect",
                "amount": 2500,
                "currency": "EUR"
            }
            mock_payer.return_value = donnees_paiement_mock
            
            moyen_selectionne = MoyenPaiementSelectionne(
                nom="carte_bancaire",
                url="/payment/card",
                jeton="auth_token_123"
            )
            
            response = self.client.post(
                f"/{lot.id_public}/initier_paiement/{paiement.id_paiement}/payer",
                json=moyen_selectionne.model_dump()
            )
            assert response.status_code == 200
            
            donnees_reponse = response.json()
            assert donnees_reponse["transaction_id"] == "TXN_SUCCESS_789"
            assert donnees_reponse["status"] == "AUTHORIZED"
            
            # Vérifier que la méthode payer a été appelée avec les bons paramètres
            mock_payer.assert_called_once()
            args, kwargs = mock_payer.call_args
            assert args[0].nom == "carte_bancaire"
            assert args[0].jeton == "auth_token_123"
            
        # ÉTAPE 4: Vérification des mises à jour en base de données
        

        with patch.object(ServiceEncaissement, 'suivre_transaction') as mock_suivre_transaction:
            mock_suivre_transaction.return_value = {
                "transaction_id": "TXN_SUCCESS_789",
                "status": "CAPTURED",
                "amount": 2500,
                "currency": "EUR"
            }
            
            response = self.client.get(f"/{lot.id_public}/visualiser")
            assert response.status_code == 200, response.json()

        db.refresh(paiement)
        assert paiement.statut == StatutPaiementEnum.PAYE
        assert paiement.transactions[0].methode == "carte_bancaire"
        assert paiement.transactions[0].donnees["transaction_id"] == "TXN_SUCCESS_789"

    @patch.object(ServiceEncaissement, 'get_moyens_paiement')
    @patch.object(ServiceEncaissement, 'suivre_transaction')
    def test_workflow_encaissement_echec_paiement(
        self,
        mock_suivre_transaction,
        mock_get_moyens_paiement,
        db: Session,
        lot_expediteur_avec_enveloppes_frauduleuses: LotExpediteur
    ):
        """
        Test du workflow d'encaissement avec échec du paiement.
        
        Vérifie la gestion des erreurs lors du processus de paiement.
        """
        lot = lot_expediteur_avec_enveloppes_frauduleuses
        paiement = lot.paiements[0]

        #1; le paiement n'est pas initialise
        assert paiement.statut == StatutPaiementEnum.NON_INITIALISE
        
        # Mock des moyens de paiement
        moyens_paiement_mock = [
            MoyenPaiement(
                nom="carte_bancaire",
                url="/payment/card",
                label="Carte bancaire", 
                authorization_token="auth_token_123",
                transaction_id="trans_123"
            )
        ]
        mock_get_moyens_paiement.return_value = moyens_paiement_mock
        
        # Initiation du paiement
        response = self.client.post(f"/{lot.id_public}/initier_paiement/{paiement.id_paiement}")
        assert response.status_code == 200, response.json()

        # Payement
        moyen_selectionne = MoyenPaiementSelectionne(
                nom="carte_bancaire",
                url="/payment/card",
                jeton="auth_token_123"
            )
        
        with patch.object(ServiceEncaissement, 'payer') as mock_payer:
            # Mock de la réponse de paiement réussi
            donnees_paiement_mock = {
                "transaction_id": "TXN_SUCCESS_789",
                "status": "AUTHORIZED",
                "payment_url": "https://payment.example.com/redirect",
                "amount": 2500,
                "currency": "EUR"
            }
            mock_payer.return_value = donnees_paiement_mock

            response = self.client.post(
                f"/{lot.id_public}/initier_paiement/{paiement.id_paiement}/payer",
                json=moyen_selectionne.model_dump()
            )
            assert response.status_code == 200

        db.refresh(paiement)
        assert paiement.statut == StatutPaiementEnum.EN_ATTENTE
        assert paiement.transactions[0].methode == "carte_bancaire"

        # Simulation d'un échec de paiement via le service
        mock_suivre_transaction.return_value = {
            "transaction_id": "TXN_FAILED_789",
            "status": "FAILED",
            "amount": 2500,
            "currency": "EUR"
        }
        
        # Visualisation du lot qui déclenche la vérification du statut
        response = self.client.get(f"/{lot.id_public}/visualiser")
        assert response.status_code == 200, response.json()
            
        # Vérifier que le statut du paiement reste EN_COURS après l'échec
        db.refresh(paiement)
        assert paiement.statut == StatutPaiementEnum.ECHEC

    def test_acces_lot_inexistant(self):
        """Test d'accès à un lot inexistant"""
        response = self.client.get("/LOT_INEXISTANT_123/visualiser")
        assert response.status_code == 404
        assert "non trouvé" in response.json()["detail"]

    def test_initiation_paiement_lot_mauvais_statut(
        self, 
        db: Session,
        lot_expediteur_avec_enveloppes_frauduleuses: LotExpediteur
    ):
        """Test d'initiation de paiement sur un lot avec un statut incorrect"""
        lot = lot_expediteur_avec_enveloppes_frauduleuses
        paiement = lot.paiements[0]
        
        # Changer le statut du lot pour qu'il ne soit plus NOTIFIE
        lot.statut = StatutLotExpediteurEnum.OUVERT
        db.add(lot)
        db.commit()
        
        response = self.client.post(f"/{lot.id_public}/initier_paiement/{paiement.id_paiement}")
        assert response.status_code == 400
        assert "Le lot doit être notifié pour initier un paiement" in response.json()["detail"]

    def test_paiement_inexistant(
        self,
        lot_expediteur_avec_enveloppes_frauduleuses: LotExpediteur
    ):
        """Test d'accès à un paiement inexistant"""
        lot = lot_expediteur_avec_enveloppes_frauduleuses
        
        response = self.client.post(f"/{lot.id_public}/initier_paiement/PAIEMENT_INEXISTANT")
        assert response.status_code == 404
        assert "Paiement non trouvé" in response.json()["detail"]

    def test_paiement_mauvais_statut(
        self,
        db: Session,
        lot_expediteur_avec_enveloppes_frauduleuses: LotExpediteur
    ):
        """Test d'initiation de paiement sur un paiement avec un statut incorrect"""
        lot = lot_expediteur_avec_enveloppes_frauduleuses
        paiement = lot.paiements[0]
        
        # Changer le statut du paiement
        paiement.statut = StatutPaiementEnum.PAYE
        db.add(paiement)
        db.commit()
        
        response = self.client.post(f"/{lot.id_public}/initier_paiement/{paiement.id_paiement}")
        assert response.status_code == 400, response.json()
        assert "Le paiement n'est pas dans un état valide" in response.json()["detail"]

    @patch.object(ServiceEncaissement, 'suivre_transaction')
    def test_verification_statut_paiement(
        self,
        mock_suivre_transaction,
        db: Session,
        lot_expediteur_avec_enveloppes_frauduleuses: LotExpediteur
    ):
        """
        Test de la vérification du statut de paiement via l'API externe.
        
        Simule la vérification automatique du statut lors de la visualisation du lot.
        """
        lot = lot_expediteur_avec_enveloppes_frauduleuses
        paiement = lot.paiements[0]
        
        # Simuler un paiement en cours avec transaction_id
        db.add(
            TransactionPaiementLotExpediteur(
                id_transaction="TXN_123456",
                paiement_lot_expediteur_id=paiement.id,
                statut=StatutPaiementEnum.EN_ATTENTE
            )
        )
        
        db.commit()

        assert len(paiement.transactions) == 1
        
        # Mock de la réponse de suivi de transaction
        mock_suivre_transaction.return_value = {
            "transaction_id": "TXN_123456",
            "status": "AUTHORIZED",
            "amount": 2500,
            "currency": "EUR"
        }
        
        # Visualisation du lot qui déclenche la vérification du statut
        response = self.client.get(f"/{lot.id_public}/visualiser")
        assert response.status_code == 200, response.json()

        db.refresh(paiement)
        assert paiement.statut == StatutPaiementEnum.AUTORISE
        
        # Vérifier que la méthode de suivi a été appelée
        # Note: Cette vérification dépend de l'implémentation de verifier_statut_paiement()
        # dans le modèle PaiementLotExpediteur

    def test_multiple_transaction_statut(self,
        db: Session,
        lot_expediteur_avec_enveloppes_frauduleuses: LotExpediteur
    ):
        lot = lot_expediteur_avec_enveloppes_frauduleuses
        paiement = lot.paiements[0]
        
        # Simuler un paiement en cours avec transaction_id
        db.add(
            TransactionPaiementLotExpediteur(
                id_transaction="TXN_123456",
                paiement_lot_expediteur_id=paiement.id,
                statut=StatutPaiementEnum.PAYE
            )
        )
        db.add(
            TransactionPaiementLotExpediteur(
                id_transaction="TXN_123457",
                paiement_lot_expediteur_id=paiement.id,
                statut=StatutPaiementEnum.ECHEC
            )
        )
        
        db.commit()

        assert len(paiement.transactions) == 2
                
        # Visualisation du lot qui déclenche la vérification du statut
        response = self.client.get(f"/{lot.id_public}/visualiser")
        assert response.status_code == 200, response.json()

        db.refresh(paiement)
        assert paiement.statut == StatutPaiementEnum.PAYE

    def test_visualiser_lot_fraudeur_retourne_montants_et_champs_ok(
        self, db: Session, lot_expediteur_avec_enveloppes_frauduleuses: LotExpediteur
    ):
        """
        Vérifie que la route /{lot_id_public}/visualiser retourne les bons montants et tous les champs attendus.
        """
        lot = lot_expediteur_avec_enveloppes_frauduleuses
        paiement = lot.paiements[0]
        paiement.montant_ttc = 10
        db.add(paiement)
        
        # Simuler un paiement en cours avec transaction_id
        db.add(
            TransactionPaiementLotExpediteur(
                id_transaction="TXN_123456",
                paiement_lot_expediteur_id=paiement.id,
                statut=StatutPaiementEnum.PAYE
            )
        )        
        db.commit()

        response = self.client.get(f"/{lot.id_public}/visualiser")
        assert response.status_code == 200, response.json()
        data = response.json()

        # Vérification des champs principaux
        assert set(data.keys()) == {
            "id_public",
            "statut",
            "expediteur",
            "montant_ttc",
            "paiement_total_recu",
            "paiement_total_restant",
            "paiements",
            "enveloppes"
        }

        # Vérification des montants
        assert data["montant_ttc"] == lot.montant_ttc
        assert data["paiement_total_recu"] == 10  # Aucun paiement effectué à la création
        assert data["paiement_total_restant"] == lot.montant_ttc - 10

        # Vérification des paiements
        assert isinstance(data["paiements"], list)
        assert len(data["paiements"]) == 1
        paiement = data["paiements"][0]
        assert paiement["statut"] == StatutPaiementEnum.PAYE.value
        assert paiement["montant_ttc"] == lot.paiements[0].montant_ttc

        # Vérification des enveloppes
        assert isinstance(data["enveloppes"], list)
        assert len(data["enveloppes"]) == 2
        for env in data["enveloppes"]:
            assert "id" in env
            assert "affranchissements" in env
            assert "valorisation" in env
            assert "poids" in env
            assert "statut" in env
            assert "casier" in env
            assert "expediteur" in env