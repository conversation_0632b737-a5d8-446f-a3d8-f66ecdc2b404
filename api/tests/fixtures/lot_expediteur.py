import pytest
from sqlmodel import Session
from models import Expediteur
from models.business import <PERSON><PERSON><PERSON>, LotExpediteur, Enveloppe, Affranchissement, PaiementLotExpediteur
from constants.enumerations import (
    StatutLotExpediteurEnum, StatutEnveloppeEnum, StatutPaiementEnum,
    CategorieAffranchissementEnum, TypeAffranchissementEnum, DeviseEnum,
    StatutVerificationEnum, OptionTraitementLotExpediteurEnum
)

from datetime import datetime
from services.lot_expediteur import ServiceLotExpediteur

@pytest.fixture
def lot_expediteur_avec_enveloppes_frauduleuses(db: Session, site, user):
    """
    Fixture qui crée un LotExpediteur avec des enveloppes frauduleuses et sous-affranchies
    prêt pour le processus d'encaissement.
    """
    # Créer un expéditeur fraudeur
    expediteur_fraudeur = Expediteur(
        nom="Fraudeur Martin",
        prenom="<PERSON>",
        adresse="123 Rue de la Fraude",
        ville="Fraudeville",
        code_postal="75001",
        pays="France",
        siret="12345678901234"
    )
    db.add(expediteur_fraudeur)
    db.commit()
    db.refresh(expediteur_fraudeur)

    casier = Casier(
        site_id=site.id,
        numero="1",
        capacite_max=100
    )

    # Créer le lot expéditeur avec statut NOTIFIE pour permettre l'encaissement
    lot = LotExpediteur(
        expediteur_id=expediteur_fraudeur.id,
        site_id=site.id,
        casier=casier,
        statut=StatutLotExpediteurEnum.NOTIFIE,
        montant_ttc_du=2500,
        option_recouvrement=OptionTraitementLotExpediteurEnum.RENVOI_EXPEDITEUR
    )
    db.add(lot)
    db.commit()
    db.refresh(lot)

    # Enveloppe 1 frauduleuse
    env = Enveloppe(
        poids=20.0,
        expediteur_id=expediteur_fraudeur.id,
        site_id=site.id,
        user_id=user.id,
        lot_expediteur_id=lot.id,
        casier=casier
    )

    # Affranchissement
    aff = Affranchissement(
        categorie=CategorieAffranchissementEnum.MARI,
        type=TypeAffranchissementEnum.VAL,
        statut=StatutVerificationEnum.VALIDE,
        prix_unite_devise=0.1,
        enveloppe=env,
        user_id=user.id
    )

    db.add(env)
    db.add(aff)
    db.commit()

    # Enveloppe 2 frauduleuse
    env = Enveloppe(
            poids=20.0,
            expediteur_id=expediteur_fraudeur.id,
            site_id=site.id,
            user_id=user.id,
            lot_expediteur_id=lot.id,
            casier=casier
        )
    
    aff = Affranchissement(
        categorie=CategorieAffranchissementEnum.MARI,
        type=TypeAffranchissementEnum.VAL,
        statut=StatutVerificationEnum.INVALIDE,
        prix_unite_devise=0.1,
        enveloppe=env,
        user_id=user.id
    )

    db.add(env)
    db.add(aff)
    db.commit()

    # Paiement
    ServiceLotExpediteur(db).creer_paiements(lot)
    return lot