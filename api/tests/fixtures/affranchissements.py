from models.business import Affranchissement
from constants.enumerations import DeviseEnum, CategorieAffranchissementEnum, TypeAffranchissementEnum

def TNUM(type="TNUM"):
    return Affranchissement(code="AB12CD34",
                            categorie=CategorieAffranchissementEnum.CODE,
                            type=TypeAffranchissementEnum.TNUM, 
                            devise=DeviseEnum.EURO, 
                            prix_unite_devise=1.29)

def S10(type="S10", code = None, enveloppe_id=None, user_id=None):
    return Affranchissement(code=code or "LL014699171FR",
                            categorie=CategorieAffranchissementEnum.CODE,
                            type=type, 
                            devise=DeviseEnum.EURO,
                            enveloppe_id=enveloppe_id,
                            user_id=user_id)
