from sqlmodel import Session
import pytest
from models import User, Site, UserRole
from core.security import get_password_hash

def create_user(*, session: Session, user_create: User) -> User:
    session.add(user_create)
    session.commit()
    session.refresh(user_create)
    return user_create

@pytest.fixture(scope="function")
def user(db: Session, site: Site):
    return create_user(session=db, user_create=User(
        email="<EMAIL>",
        hashed_password=get_password_hash("thisisstrong"),
        site_id=site.id,
        role=UserRole.USER
    ))

@pytest.fixture(scope="function")
def admin_user(db: Session, site: Site):
    return create_user(session=db, user_create=User(
        email="<EMAIL>",
        hashed_password=get_password_hash("thisisstrong"),
        site_id=site.id,
        role=UserRole.ADMIN
    ))


@pytest.fixture(scope="function")
def manager_user(db: Session, site: Site):
    return create_user(session=db, user_create=User(
        email="<EMAIL>",
        hashed_password=get_password_hash("thisisstrong"),
        site_id=site.id,
        role=UserRole.MANAGER
    ))

