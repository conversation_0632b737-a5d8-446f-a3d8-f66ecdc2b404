from models.business import Enveloppe, PhotoEnveloppe
from constants.enumerations import ProduitEnum
from sqlmodel import Session
from datetime import datetime
import pytest
from tests.fixtures.sites import create_site
from tests.fixtures.users import create_user


def create_enveloppe(db: Session, user = None, site = None):
    enveloppe = Enveloppe(created_at=datetime.now(), updated_at=datetime.now(), 
                          produit = ProduitEnum.LINT,
                          site = site or create_site(db),
                          user = user or create_user(db))
    
    db.add(enveloppe)
    db.commit()
    db.refresh(enveloppe)

    return enveloppe

@pytest.fixture
def enveloppe(db: Session, user, site):
    enveloppe = create_enveloppe(db, user, site)
    return enveloppe

@pytest.fixture
def enveloppe_complete(db: Session, user, site, enveloppe):

    photos = [
        PhotoEnveloppe(
            format="jpg",
            qualite="haute",
            largeur=800,
            hauteur=600,
            orientation="portrait",
            enveloppe=enveloppe
        ) for _ in range(3)
    ]
    
    db.add_all(photos)
    db.commit()
    db.refresh(enveloppe)
    
    return enveloppe

@pytest.fixture
def enveloppe_complete_frauduleuse(db: Session, user, site):
    # Création de l'enveloppe
    enveloppe = create_enveloppe(db, user, site)
    
    # Ajout d'un affranchissement frauduleux
    from models.business import Affranchissement
    from constants.enumerations import StatutAffranchissementEnum, DeviseEnum, NatureAffranchissementEnum
    
    affranchissement = Affranchissement(
        code="87000763985319W",
        type="SD87",
        statut=StatutAffranchissementEnum.INVALIDE,
        nature=NatureAffranchissementEnum.LV_20,
        prix_unite_devise=1.26,
        devise=DeviseEnum.EURO,
        quantite=1,
        enveloppe=enveloppe
    )
    
    db.add(affranchissement)
    
    # Ajout de 3 photos
    photos = [
        PhotoEnveloppe(
            format="jpg",
            qualite="haute",
            largeur=800,
            hauteur=600,
            orientation="portrait",
            enveloppe=enveloppe
        ) for _ in range(3)
    ]
    
    db.add_all(photos)
    
    # Marquer l'enveloppe comme frauduleuse
    from constants.enumerations import StatutEnveloppeEnum
    enveloppe.statut = StatutEnveloppeEnum.FRAUDULEUSE
    
    db.commit()
    db.refresh(enveloppe)
    
    return enveloppe
