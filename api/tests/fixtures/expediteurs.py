import pytest
from sqlmodel import Session
from models import Expediteur

def create_expediteur(db: Session, default=True, name=None, adresse=None, ville=None, code_postal=None, pays=None):
    expediteur = Expediteur(
        nom=name or "Test Expediteur",
        adresse=adresse or "123 Test Street",
        ville=ville or "Test City",
        code_postal=code_postal or "12345",
        pays=pays or "France"
    )
    db.add(expediteur)
    db.commit()
    db.refresh(expediteur)

    return expediteur

@pytest.fixture
def expediteur(db: Session, admin_user):
    return create_expediteur(db=db, default=True)

