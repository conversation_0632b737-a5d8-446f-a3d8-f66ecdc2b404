# from sqlmodel import Session
# import pytest
# from app.models import OpenaiAssistant
#
# def create_assistant(db : Session, default = True, name = None, user = None):
#     assistant = OpenaiAssistant(
#         name=name or "Test Assistant",
#         model="gpt-3.5-turbo",
#         instructions="This is a test assistant",
#         user_prompt="this is test user prompt",
#         created_by=user.id,
#         default=default
#     )
#     db.add(assistant)
#     db.commit()
#     db.refresh(assistant)
#
#     return assistant
#
# @pytest.fixture
# def assistant(db : Session, admin_user):
#     return create_assistant(user=admin_user, db=db, default=True)
#
