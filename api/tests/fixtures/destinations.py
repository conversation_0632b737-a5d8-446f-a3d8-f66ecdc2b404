import pytest
from sqlmodel import Session
from models import Destination

def create_destination(db: Session, name_fr=None, name_en=None, alpha2=None, alpha3=None):
    destination = Destination(
        nom_fr=name_fr or "France",
        nom_en=name_en or "France",
        alpha2=alpha2 or "FR",
        alpha3=alpha3 or "FRA"
    )
    db.add(destination)
    db.commit()
    db.refresh(destination)

    return destination

@pytest.fixture
def destination(db: Session, admin_user):
    return create_destination(db=db) 