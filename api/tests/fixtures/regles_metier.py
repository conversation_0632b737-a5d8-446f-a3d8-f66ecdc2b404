import pytest
from models.business import RegleMetier
from constants.enumerations import TypeAffranchissementEnum, TypeRegleMetierEnum
import random

@pytest.fixture(scope="function", autouse=True)
def setup_regles_metiers(db):
    """
    Régler métier nécessaire pour que les checks S10 passe
    """
    db.add(RegleMetier(cle="S10_LL", 
                            type_affranchissement=TypeAffranchissementEnum.S10,
                            type_regle=TypeRegleMetierEnum.SEQUENCE, 
                            valeur={"debut": "01469910", "fin": "01469919", 
                                    "service": "LL", 
                                    "date_attribution": "01/10/2025"}))
    db.commit()

def creer_sequence_s10(db, service, debut, fin, emetteur=""):
    """
    Régler métier nécessaire pour que les checks S10 passe
    """
    random_id = random.randint(1, 1000000)
    db.add(RegleMetier(cle=f"S10_{service}_{random_id}",
                            active=True,
                            type_affranchissement=TypeAffranchissementEnum.S10,
                            type_regle=TypeRegleMetierEnum.SEQUENCE, 
                            valeur={"debut": debut, "fin": fin, 
                                    "service": service, 
                                    "emetteur": emetteur,
                                    "date_attribution": "01/10/2025"}))
    db.commit()