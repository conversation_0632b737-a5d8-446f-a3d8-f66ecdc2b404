from fastapi.testclient import TestClient
from sqlmodel import Session
from core import security
import crud
from core.config import settings
from models import User, UserCreate, UserUpdate, Token
from tests.utils.utils import random_email, random_lower_string
from datetime import timedelta


def create_random_user(db: Session) -> User:
    email = random_email()
    password = random_lower_string()
    user_in = UserCreate(email=email, password=password)
    user = crud.create_user(session=db, user_create=user_in)
    return user


def get_token_for_user(user : User, mode = "normal", expires_delta = None) -> dict[str, str]:
    if expires_delta is None:
        expires_delta = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

    return security.create_access_token(
        user.id, expires_delta=expires_delta
    )