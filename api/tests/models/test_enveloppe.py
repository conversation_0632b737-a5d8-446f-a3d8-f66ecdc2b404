import pytest
from sqlmodel import Session, select, Boolean
from datetime import datetime
from models.business import Enveloppe, Site, User, Expediteur, Destination
from constants.enumerations import StatutEnveloppeEnum, ProduitEnum
from uuid import UUID


class TestEnveloppeModel:
       
    def test_informations_computed(self, enveloppe, session):
        """Test la propriété hybride informations_computed"""
        # Vérifier que la propriété informations_computed retourne un dictionnaire
        infos = enveloppe.informations
        assert isinstance(infos, dict)

        # Test des données retournées
        assert set(infos.keys()) == set(["complet", "modifiable", "nombre_affranchissements_invalides", "prix_affranchissements_valide"])

        #  Test access
        assert enveloppe.informations.complet == False

        # Modifier informations_data
        enveloppe.informations_data = {
            "test": "test"
        }
        session.add(enveloppe)
        session.commit()

        # Rafraîchir l'instance depuis la base de données
        session.refresh(enveloppe)
        
        # Vérifier que la requête retourne bien les données de informations_data
        result = session.exec(select(Enveloppe).where(Enveloppe.id == enveloppe.id)).first()
        assert result.informations_data == enveloppe.informations
        assert result.informations_data == infos
        
        # Vérifier que la propriété hybride informations retourne bien les données de informations_data
        # lors d'une requête directe sur cette propriété
        result_info = session.exec(select(Enveloppe.informations).where(Enveloppe.id == enveloppe.id)).first()
        assert result_info == enveloppe.informations

        # Alternative avec une syntaxe plus simple
        from sqlalchemy import text
        
        # Utiliser une requête SQL brute
        assert session.exec(
            select(Enveloppe).where(
                text("(informations_data->>'complet')::boolean = false")
            )
        ).first().id == enveloppe.id
