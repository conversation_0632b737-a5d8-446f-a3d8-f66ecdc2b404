import pytest
from models.business import PhotoEnveloppe
from core.config import settings

class TestPhotoEnveloppe:
    @pytest.fixture
    def photo_enveloppe(self):
        """Fixture pour créer une instance de PhotoEnveloppe avec des données de test"""
        return PhotoEnveloppe(
            format="JPEG",
            qualite="HD",
            largeur=100.0,
            hauteur=150.0,
            orientation="portrait",
            url="https://storage.azure.com/test/photo.jpg?sas-token",
            commentaire="Test photo"
        )

    def test_model_serialization(self, photo_enveloppe):
        """Test que l'URL est exclue de la sérialisation"""
        serialized_data = photo_enveloppe.model_dump()

        # Vérification que l'url est exclue
        assert "url" not in serialized_data
        
        # Vérification que les autres champs sont présents
        assert serialized_data["format"] == "JPEG"
        assert serialized_data["qualite"] == "HD"
        assert serialized_data["largeur"] == 100.0
        assert serialized_data["hauteur"] == 150.0
        assert serialized_data["orientation"] == "portrait"
        assert serialized_data["commentaire"] == "Test photo"

    def test_url_access(self, photo_enveloppe):
        """Test que l'URL reste accessible sur l'instance"""
        assert photo_enveloppe.url == "https://storage.azure.com/test/photo.jpg?sas-token"

    def test_public_url_generation(self, photo_enveloppe):
        """Test la génération de l'URL publique"""
        # Vérification que l'URL publique est générée correctement
        base_url = photo_enveloppe.url.split('?')[0]
        expected_public_url = f"{base_url}?{settings.AZURE_STORAGE_PUBLIC_SAS_TOKEN}"
        assert photo_enveloppe.public_url == expected_public_url

    def test_public_url_with_no_url(self):
        """Test le comportement de public_url quand url est None"""
        photo = PhotoEnveloppe(
            format="JPEG",
            qualite="HD"
        )
        assert photo.public_url is None

    # def test_model_validation(self):
    #     """Test les validations du modèle"""
    #     # Test avec des valeurs valides
    #     photo = PhotoEnveloppe(
    #         format="JPEG",
    #         qualite="HD",
    #         largeur=100.0,
    #         hauteur=150.0,
    #         orientation="portrait",
    #         url="https://storage.azure.com/test/photo.jpg",
    #         commentaire="Test"
    #     )
    #     assert photo.format == "JPEG"
    #     assert photo.largeur == 100.0

    #     # Test avec une largeur négative
    #     with pytest.raises(ValueError):
    #         PhotoEnveloppe(
    #             format="JPEG",
    #             largeur=-100.0
    #         ) 