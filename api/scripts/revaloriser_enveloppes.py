#!/usr/bin/env python3
"""
Script pour revaloriser toutes les enveloppes qui ne sont pas en statut EDITION.
"""
from datetime import datetime
from sqlalchemy import and_
from core.db import get_session
from models.business import Enveloppe, StatutEnveloppeEnum
from services.validateurs.validateur_affranchissement import ValidateurAffranchissement
from tqdm import tqdm
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    # filename='revalorisation_enveloppes.log'
)
logger = logging.getLogger(__name__)

def revaloriser_enveloppes():
    """Revalorise toutes les enveloppes qui ne sont pas en statut EDITION."""
    # Récupérer toutes les enveloppes qui ne sont pas en statut EDITION
    with get_session() as session:
        enveloppes = session.query(Enveloppe).filter(
            Enveloppe.statut != StatutEnveloppeEnum.EDITION
        ).all()
        
        logger.info(f"Début de la revalorisation de {len(enveloppes)} enveloppes")
        print(f"Début de la revalorisation de {len(enveloppes)} enveloppes")
        
        reussites = 0
        echecs = 0
        
        # Récupérer tous les IDs d'enveloppes
        enveloppe_ids = [e.id for e in enveloppes]
        
        # Traiter les enveloppes par lots de 20
        batch_size = 50
        for i in tqdm(range(0, len(enveloppe_ids), batch_size)):
            batch_ids = enveloppe_ids[i:i+batch_size]
            # print(f"Traitement du lot {i//batch_size + 1}/{(len(enveloppe_ids) + batch_size - 1)//batch_size}")
            
            # Traiter chaque enveloppe du lot avec une seule session
            for enveloppe_id in batch_ids:
                try:
                    # Récupérer l'enveloppe dans la session courante
                    enveloppe = session.query(Enveloppe).filter(
                        Enveloppe.id == enveloppe_id
                    ).first()
                    
                    if not enveloppe:
                        logger.warning(f"Enveloppe {enveloppe_id} non trouvée")
                        continue
                    
                    print(f"Traitement de l'enveloppe {enveloppe.id}")
                    enveloppe.service.valoriser()
                    reussites += 1
                    
                except Exception as e:
                    echecs += 1
                    logger.error(f"Erreur lors de la revalorisation de l'enveloppe {enveloppe_id}: {str(e)}")
                    print(f"Erreur sur l'enveloppe {enveloppe_id}: {str(e)}")
            
            # Commit après chaque lot
            session.commit()
        
        logger.info(f"Revalorisation terminée. Réussites: {reussites}, Échecs: {echecs}")
        print(f"Revalorisation terminée. Réussites: {reussites}, Échecs: {echecs}")


if __name__ == "__main__":
    revaloriser_enveloppes()
