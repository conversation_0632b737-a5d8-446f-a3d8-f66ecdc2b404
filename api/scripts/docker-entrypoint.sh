#!/usr/bin/env bash

# Run database migrations
alembic upgrade head

# Initial fixture data
if [ "$ENVIRONMENT" == "local" ] && [ -f "/app/initial_fixture_data.py" ]; then
    echo "Utilisation des fixtures SQL"
    python /app/initial_fixture_data.py
fi

# Check if the environment variable is set to "production"
if [ "$ENVIRONMENT" == "production" ]; then
    # Run Uvicorn with production settings
    poetry run uvicorn main:app --host 0.0.0.0 --port 80 --workers 4 --forwarded-allow-ips="*"
else
    echo "Run server for DEV"
    # Run Uvicorn with development settings
    poetry run uvicorn main:app --host 0.0.0.0 --port 80 --reload
fi