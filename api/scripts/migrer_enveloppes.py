#!/usr/bin/env python3
"""
Script pour migrer des enveloppes d'un environnement à un autre.
Utilisation: python migrer_enveloppes.py --ids 1,2,3 --source-env test --target-env prod
"""
from sqlmodel import Session, create_engine, select
import argparse
import os
import sys
import base64
from datetime import datetime
import logging
from sqlalchemy import create_engine, select
from sqlmodel import Session
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv
from azure.storage.blob import BlobServiceClient
import requests

from azure.storage.blob import BlobClient
from azure.storage.blob.aio import BlobServiceClient as AsyncBlobServiceClient
from core.config import settings


BLOB_PROD = "https://storageinnovationaca.blob.core.windows.net/riposte-production/"
BLOB_SOURCE = "https://storageaccountinnovation.blob.core.windows.net/riposte-staging/"
BLOB_PROD_SAS_TOKEN = ""

# Configurer le logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(f"migration_enveloppes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("migration_enveloppes")

# Ajouter le répertoire parent au path pour pouvoir importer les modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models import Enveloppe, Destination, Affranchissement, Expediteur, Site, PhotoEnveloppe, VerificationAffranchissement, User


def create_session(connection_string):
    """Crée une session SQLAlchemy à partir d'une chaîne de connexion."""
    engine = create_engine(connection_string)
    return Session(engine)

def migrer_enveloppes():
    """Migre les enveloppes spécifiées d'un environnement à un autre."""
    
    
    source_session = create_session("postgresql+psycopg://")
    target_session = create_session("postgresql+psycopg://")
        

    reussites = 0
    echecs = 0

    # TASSA in prod
    expediteur_tassa = target_session.query(Expediteur).filter(Expediteur.nom == "Sarl Tassa").first()
    site = target_session.query(Site).filter(Site.nom == "ROISSY").first()
    
    enveloppes = source_session.query(Enveloppe).join(Enveloppe.expediteur).filter(Expediteur.nom.ilike('%sarl tassa%')).order_by(Enveloppe.id.desc()).all()

    print(f"Nombre d'enveloppes à migrer: {len(enveloppes)}")

    # Continuer ?
    if input("Continuer ? (y/n)") != "y":
        sys.exit(0)

    # Delete all existings


    if not expediteur_tassa:
        logger.error("Impossible de trouver l'expéditeur TASSA dans l'environnement cible")
        sys.exit(1)

    if not site:
        logger.error("Impossible de trouver le site ROISSY dans l'environnement cible")
        sys.exit(1)
    
    for enveloppe in enveloppes:
        try:
            
            # Enveloppe existante en prod ?
            existing_enveloppe = target_session.query(Enveloppe).filter(Enveloppe.uuid == enveloppe.uuid).first()
            
            if existing_enveloppe:
                logger.warning(f"Enveloppe {enveloppe.uuid} déjà migrée vers l'ID {existing_enveloppe.id}")
                echecs += 1
                continue

            user = enveloppe.user.email

            user = target_session.query(User).filter(User.email == user).first()

            if not user:
                logger.warning(f"Utilisateur {user} introuvable dans l'environnement cible")
                echecs += 1
                continue

            print(user)

            destination = None

            if enveloppe.destination:
                destination = target_session.query(Destination).filter(Destination.nom_fr == enveloppe.destination.nom_fr).first()
                
                if not destination and enveloppe.destination:
                    logger.warning(f"Destination {enveloppe.destination.nom_fr} introuvable dans l'environnement cible")
                    echecs += 1
                    continue

            # Créer une nouvelle enveloppe dans l'environnement cible
            nouvelle_enveloppe = Enveloppe(
                statut=enveloppe.statut,
                uuid=enveloppe.uuid,
                poids=enveloppe.poids,
                surpoids=enveloppe.surpoids,
                surdimensionne=enveloppe.surdimensionne,
                created_at=enveloppe.created_at,
                updated_at=enveloppe.updated_at,
                completed_at=enveloppe.completed_at,
                destination_enveloppe=enveloppe.destination_enveloppe,
                destination=destination,
                id_migration=enveloppe.id_migration,
                source=enveloppe.source,
                valorisation=enveloppe.valorisation,
                expediteur_id=expediteur_tassa.id,
                site_id=site.id,
                informations_data=enveloppe.informations_data,
                casier_id=17,
                lot_expediteur_id=17,
                user_id=user.id
            )
           
            # Ajouter l'enveloppe à la session cible
            target_session.add(nouvelle_enveloppe)
            
            # Migrer les photos
            for photo in enveloppe.photos:
                
                # On change l'url et le token
                correct_url = photo.url.replace(BLOB_SOURCE, BLOB_PROD)
                correct_url = correct_url.split('?')[0]
                correct_url = correct_url + f"?{BLOB_PROD_SAS_TOKEN}"

                # Créer la nouvelle photo dans la cible
                nouvelle_photo = PhotoEnveloppe(
                    format=photo.format,
                    qualite=photo.qualite,
                    largeur=photo.largeur,
                    hauteur=photo.hauteur,
                    orientation=photo.orientation,
                    url=correct_url,
                    commentaire=photo.commentaire,
                    enveloppe=nouvelle_enveloppe
                )

                # Download image via l'URL
                image_data = requests.get(photo.url).content
                
                blob_service_client = BlobClient.from_blob_url(nouvelle_photo.url)

                blob_service_client.upload_blob(
                    image_data,
                    blob_type="BlockBlob",
                    content_type=nouvelle_photo.format,
                    overwrite=True
                )

                target_session.add(nouvelle_photo)
                
            # Migrer les affranchissements
            for aff in enveloppe.affranchissements:
                nouvel_aff = Affranchissement(
                    code=aff.code,
                    categorie=aff.categorie,
                    type=aff.type,
                    sous_type=aff.sous_type,
                    origine=aff.origine,
                    nature=aff.nature,
                    prix_unite_devise=aff.prix_unite_devise,
                    prix_unite_euros=aff.prix_unite_euros,
                    devise=aff.devise,
                    quantite=aff.quantite,
                    poids_max=aff.poids_max,
                    statut=aff.statut,
                    enveloppe=nouvelle_enveloppe,
                    user_id=user.id
                )
                target_session.add(nouvel_aff)
                
                # Migrer les vérifications d'affranchissement
                for verif in aff.verifications:
                    nouvelle_verif = VerificationAffranchissement(
                        type=verif.type,
                        statut=verif.statut,
                        message=verif.message,
                        donnees=verif.donnees,
                        affranchissement=nouvel_aff
                    )
                    target_session.add(nouvelle_verif)
                
            logger.info(f"Enveloppe {enveloppe.uuid} migrée avec succès vers l'ID {nouvelle_enveloppe.id}")
            reussites += 1

            # Comparer les deux enveloppes (nb photos, affranchissements, etc)
            logger.info(f"Vérification de la migration de l'enveloppe {enveloppe.uuid}")
            
            target_session.commit()
            target_session.refresh(nouvelle_enveloppe)


            print(len(enveloppe.affranchissements))
            print(len(nouvelle_enveloppe.affranchissements))
            print("---")
            print(len(enveloppe.photos))
            print(len(nouvelle_enveloppe.photos))
   
        except Exception as e:
            target_session.rollback()
            raise e
            logger.error(f"Erreur lors de la migration de l'enveloppe {enveloppe.uuid}: {str(e)}")
            echecs += 1
    
    logger.info(f"Migration terminée: {reussites} réussites, {echecs} échecs")
    return reussites, echecs

def main():
    
    reussites, echecs = migrer_enveloppes()
    
    if echecs > 0:
        sys.exit(1)
    
    sys.exit(0)

if __name__ == "__main__":
    main()
