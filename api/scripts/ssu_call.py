# is_final
import os
import sys
import json
from pathlib import Path

from services.ssu_api import SSUApi

def test_ssu_api():
    # Configuration des variables d'environnement requises
    if not os.getenv("OKAPI_KEY"):
        print("Erreur: La variable d'environnement OKAPI_KEY n'est pas définie")
        return
    
    if not os.getenv("SSU_BASE_URL"):
        print("Erreur: La variable d'environnement SSU_BASE_URL n'est pas définie")
        return
    
    # Vérifier si un SID est fourni en argument
    if len(sys.argv) < 2:
        print("Erreur: Aucun SID fourni. Utilisation: python ssu_call.py <SID>")
        return
    
    # Utiliser le premier argument comme SID
    sid_test = sys.argv[1]
    
    # Test de get_tracking
    print(f"\nTest de get_tracking avec SID: {sid_test}")
    tracking_data, tracking_error = SSUApi.get_tracking(sid_test)
    
    if tracking_error:
        print(f"Erreur lors de la récupération du suivi: {tracking_error}")
    else:
        print("Succès de récupération du suivi:")
        print(json.dumps(tracking_data, indent=2, ensure_ascii=False))
    
    # Test de get_purchase
    print(f"\nTest de get_purchase avec SID: {sid_test}")
    purchase_data, purchase_error = SSUApi.get_purchase(sid_test)
    
    if purchase_error:
        print(f"Erreur lors de la récupération des informations d'achat: {purchase_error}")
    else:
        print("Succès de récupération des informations d'achat:")
        print(json.dumps(purchase_data, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    test_ssu_api()
