# Use a slimmer official Python runtime as a parent image
FROM python:3.10-slim

# Set environment variables for Poetry and virtual environment location
ENV PYTHONUNBUFFERED=1 \
    POETRY_VERSION=1.8.1 \
    POETRY_HOME="/root/.local" \
    POETRY_VIRTUALENVS_PATH="/opt/venv"

# Add Poetry's bin directory and the virtual environment bin directory to PATH
ENV PATH="$POETRY_HOME/bin:/opt/venv/bin:$PATH"

# Set the working directory in the container
WORKDIR /app

# Install system dependencies (for building some Python packages)
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install --no-cache-dir "poetry==$POETRY_VERSION"

# Copy the project files into the container
COPY ./pyproject.toml ./poetry.lock* /app/

# Install dependencies and clean up Poetry cache
RUN poetry config virtualenvs.create false && poetry install --no-root --no-interaction --no-ansi \
    && rm -rf /root/.cache/pypoetry

# Copy the remaining project files
COPY ./ /app/

# Expose the port FastAPI will run on
EXPOSE 8000

ENV PYTHONPATH=/app

# Set an environment variable to make sure the model cache is available
ENV TRANSFORMERS_CACHE=/root/.cache/huggingface/transformers

# Preload the tokenizer and model
RUN python -c "from transformers import CamembertTokenizer, CamembertModel; \
    tokenizer = CamembertTokenizer.from_pretrained('dangvantuan/sentence-camembert-large'); \
    model = CamembertModel.from_pretrained('dangvantuan/sentence-camembert-large')"


# Command to run the FastAPI app with Uvicorn
CMD ["/app/scripts/docker-entrypoint.sh"]
