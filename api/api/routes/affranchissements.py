from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session, select
from typing import List, Dict, Any
from api.deps import SessionDep, CurrentUser
from models.public import CategorieAffranchissement, AjoutAffranchissement
from constants.affranchissements.affranchissement import CATEGORIES_AFFRANCHISSEMENTS
from services.affranchissement import ServiceAffranchissement
from models.business import Affranchissement
from constants.enumerations import StatutVerificationEnum
from pydantic import BaseModel
from models.public import AffranchissementItemPublic
router = APIRouter()

class ValidationResponse(BaseModel):
    is_valid: bool
    affranchissement: AffranchissementItemPublic | None
    message: str | None

@router.post("/verifier", response_model=ValidationResponse)
def valider_affranchissement(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    data: AjoutAffranchissement
) -> ValidationResponse:
    """
    Teste la validité d'un CODE affranchissement (S10, etc.) sans le sauvegarder.
    Crée un modèle d'affranchissement temporaire et exécute les validateurs.
    """
    try:
        # Création d'un affranchissement temporaire (non sauvegardé)
        affranchissement = ServiceAffranchissement.create_affranchissement(data.dict())
        affranchissement.user_id = current_user.id
        
        # Exécution des validations
        from services.validateurs.validateur_affranchissement import ValidateurAffranchissement
        ValidateurAffranchissement.executer_validations(affranchissement)

        # Utilisation de any() pour vérifier si au moins une validation est invalide
        has_invalid_validations = any(
            verification.statut == StatutVerificationEnum.INVALIDE 
            for verification in affranchissement.verifications
        )

        return ValidationResponse(
            is_valid=not has_invalid_validations,
            affranchissement=affranchissement,
            message=None
        )
        
    except Exception as e:
        # En cas d'erreur de validation, on retourne les détails
        return ValidationResponse(
            is_valid=False,
            affranchissement=None,
            message=str(e)
        )

# @router.get("/prix", response_model=List[CategorieAffranchissement])
# def get_prix_disponibles(
#     *,
#     session: SessionDep,
#     current_user: CurrentUser,
#     skip: int = 0,
#     limit: int = 10,
#     search: str = None
# ) -> List[CategorieAffranchissement]:
#     """
#     """
#     elements = []

#     for i, item in CATEGORIES_AFFRANCHISSEMENTS.items():
#         elements.append(item)

#     return elements
