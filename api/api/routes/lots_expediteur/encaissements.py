from fastapi import APIRouter, HTTPException, Depends
from sqlmodel import Session, select
from typing import Dict, Any, List
from api.deps import SessionDep
from models.business import LotExpediteur, TransactionPaiementLotExpediteur
from models.public import LotExpediteurPublicPourExpediteur, ChoixExpediteurPourLot
from models.encaissement import MoyenPaiement, MoyenPaiementSelectionne
from constants.enumerations import StatutLotExpediteurEnum, StatutPaiementEnum, OptionTraitementLotExpediteurEnum
from services.api_laposte.encaissement import ServiceEncaissement
from sqlalchemy.orm import joinedload
from services.lot_expediteur import ServiceLotExpediteur

router = APIRouter()

def get_lot_by_id(
    lot_id_public: str,
    session: SessionDep
) -> LotExpediteur:
    """
    Recupere un Lot Expediteur par son id_public
    """
    # Construction de la requête de base
    lot_expediteur = session.query(LotExpediteur).options(
        joinedload(LotExpediteur.paiements),
    ).filter(LotExpediteur.id_public == lot_id_public).first()

    if not lot_expediteur:
        raise HTTPException(status_code=404, detail="Lot non trouvée")
    
    return lot_expediteur

# @router.post("/{lot_id_public}/initier_paiement", response_model=LotExpediteur)
# def creer_paiement(
#     *,
#     session: SessionDep,
#     current_user: CurrentManagerUser,
#     lot: LotExpediteur = Depends(get_lot_by_id),
#     # fractions: List[FractionnementPaiement]
# ) -> LotExpediteur:
#     """
#     Génere les objets PaiementLotExpediteur pour un LotExpediteur, afin que l'utilisateur puisse ensuite les sélectionner et payer
#     """
#     # Le Lot est-il dans un etat valide ?
#     if lot.statut != StatutLotExpediteurEnum.NOTIFIE:
#         raise HTTPException(status_code=400, detail="Le lot doit être notifié pour initier un paiement")

#     return ServiceLotExpediteur(session).creer_paiements(lot)

# Routes sans authentification nécessaires :
# GET /public/lots/{id_public}/visualiser
# POST /public/lots/{id_public}/initier_paiement/{paiement_id}
# POST /public/lots/{id_public}/initier_paiement/{paiement_id}/payer

@router.post("/{lot_id_public}/initier_paiement/{paiement_id}", response_model=List[MoyenPaiement])
def initier_paiement(
    *,
    session: SessionDep,
    lot: LotExpediteur = Depends(get_lot_by_id),
    paiement_id: str,
) -> List[MoyenPaiement]:
    """
    Pour un LotExpediteur et un PaiementLotExpediteur, initialise le paiement via l'API Laposte
    et retourne la liste des moyens de paiement disponibles
    """

    if lot.option_recouvrement is None:
        raise HTTPException(status_code=400, detail="Aucune option de recouvrement a été selectionnée")

    # Le Lot est-il dans un etat valide ?
    if lot.statut != StatutLotExpediteurEnum.NOTIFIE:
        raise HTTPException(status_code=400, detail="Le lot doit être notifié pour initier un paiement")

    # Le paiement existe-t-il ?
    paiement = next((p for p in lot.paiements if p.id_paiement == paiement_id), None)

    if not paiement:
        raise HTTPException(status_code=404, detail="Paiement non trouvé")
    
    # Verification du statut du paiement
    if paiement.statut != StatutPaiementEnum.NON_INITIALISE:
        raise HTTPException(status_code=400, detail="Le paiement n'est pas dans un état valide")

    # On initialise le paiement via l'API Laposte #TODO add ammount etc
    service = ServiceEncaissement()
    moyens = service.get_moyens_paiement()

    return moyens

@router.post("/{lot_id_public}/initier_paiement/{paiement_id}/payer")
def effectuer_paiement(
    *,
    session: SessionDep,
    lot: LotExpediteur = Depends(get_lot_by_id),
    paiement_id: str,
    moyen_paiement: MoyenPaiementSelectionne
):
    """
    L'utilisateur a choisi un moyen de paiement, on redirige vers le paiement
    """

    if lot.option_recouvrement is None:
        raise HTTPException(status_code=400, detail="Aucune option de recouvrement a été selectionnée")

    # Le Lot est-il dans un etat valide ?
    if lot.statut != StatutLotExpediteurEnum.NOTIFIE:
        raise HTTPException(status_code=400, detail="Le lot doit être notifié pour initier un paiement")

    # Le paiement existe-t-il ?
    paiement = next((p for p in lot.paiements if p.id_paiement == paiement_id), None)

    if not paiement:
        raise HTTPException(status_code=404, detail="Paiement non trouvé")
    
    # Verification du statut du paiement
    if paiement.statut != StatutPaiementEnum.NON_INITIALISE:
        raise HTTPException(status_code=400, detail="Le paiement n'est pas dans un état valide")
    
    paiement.statut = StatutPaiementEnum.EN_ATTENTE
    
    # On initialise le paiement via l'API Laposte
    donnees_paiement = ServiceEncaissement().payer(moyen_paiement, paiement)

    # On créer une Transaction
    transaction = TransactionPaiementLotExpediteur(
        methode=moyen_paiement.nom,
        paiement_lot_expediteur_id=paiement.id,
        statut=StatutPaiementEnum.EN_ATTENTE,
        id_transaction=donnees_paiement.get("transaction_id"),
        donnees=donnees_paiement
    )

    from core.db import get_session
    with get_session() as session:
        session.add(transaction)
        session.commit()

    return donnees_paiement

@router.get("/{lot_id_public}/visualiser", response_model=LotExpediteurPublicPourExpediteur)
def visualiser_lot_fraudeur(
    *,
    session: SessionDep,
    lot: LotExpediteur = Depends(get_lot_by_id),
) -> LotExpediteurPublicPourExpediteur:
    """
    Visualise un lot expéditeur
    """
    paiements = lot.paiements

    # On récupère les informations de paiement pour chaque paiement et chaque sous transactions
    for paiement in paiements:
        paiement.verifier_statut_paiement(session)

    # Paiements
    total_recu = sum(p.montant_ttc for p in paiements if p.statut == StatutPaiementEnum.PAYE)
    total_restant = lot.montant_ttc - total_recu

    return LotExpediteurPublicPourExpediteur(
        id_public=lot.id_public,
        statut=lot.statut,
        expediteur=lot.expediteur,
        montant_ttc=lot.montant_ttc,
        paiement_total_recu=total_recu,
        paiement_total_restant=total_restant,
        paiements=paiements,
        enveloppes=lot.enveloppes
    )

@router.post("/{lot_id_public}/choix", response_model=LotExpediteurPublicPourExpediteur)
def choix_option_lot_expediteur(
    *,
    session: SessionDep,
    lot: LotExpediteur = Depends(get_lot_by_id),
    choix: ChoixExpediteurPourLot
) -> LotExpediteurPublicPourExpediteur:
    """
    Permet à l'expéditeur de soumettre son choix d'option pour le lot (A, B ou C), stocke le choix et crée les paiements si nécessaire.
    """

    # Appliquer le choix de l'expéditeur
    service = ServiceLotExpediteur(session)
    lot = service.appliquer_option_recouvrement(lot.id, choix.option, choix.adresse_renvoi)

    if not lot:
        raise HTTPException(status_code=400, detail="Impossible d'appliquer le choix")

    # Créer les paiements si pas déjà fait
    if not lot.paiements or len(lot.paiements) == 0:
        lot = service.creer_paiements(lot)

    session.refresh(lot)

    return LotExpediteurPublicPourExpediteur(
        id_public=lot.id_public,
        statut=lot.statut,
        expediteur=lot.expediteur,
        montant_ttc=lot.montant_ttc,
        paiement_total_recu=0,
        paiement_total_restant=lot.montant_ttc,
        paiements=lot.paiements,
        enveloppes=lot.enveloppes
    )

@router.get("/{lot_id_public}/options", response_model=List[OptionTraitementLotExpediteurEnum])
def get_options_possibles_lot_expediteur(
    *,
    session: SessionDep,
    lot: LotExpediteur = Depends(get_lot_by_id),
) -> List[OptionTraitementLotExpediteurEnum]:
    """
    Retourne la liste des options possibles pour un lot donné selon les règles métier.
    """

    service = ServiceLotExpediteur(session)
    return service.options_possibles_pour_lot(lot)
