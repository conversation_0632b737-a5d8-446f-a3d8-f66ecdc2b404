# from fastapi import APIRouter, Depends, HTTPException
# from sqlmodel import Session, select
# from typing import List
# from api.deps import SessionDep, CurrentUser
# from models.public import CategorieAffranchissement
# from constants.affranchissements.nature import NATURES_AFFRANCHISSEMENT, NatureAffranchissementType
# router = APIRouter()



# @router.get("/", response_model=List[NatureAffranchissementType])
# def get_natures(
#     *,
#     session: SessionDep,
#     current_user: CurrentUser,
#     skip: int = 0,
#     limit: int = 10,
#     search: str = None
# ) -> List[CategorieAffranchissement]:
#     """
#     Retrieve a list of Destinations with optional search filter
#     """
#     return list(NATURES_AFFRANCHISSEMENT.values())

