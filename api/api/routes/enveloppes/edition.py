from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from sqlmodel import Session, select
from datetime import datetime
from typing import List
from api.deps import SessionDep, CurrentUser
from models.business import Enveloppe, Affranchissement, EnveloppePublic, StatutEnveloppeEnum, PhotoEnveloppe 
from sqlalchemy.orm import joinedload
from services.enveloppe import ServiceEnveloppe
from services.affranchissement import ServiceAffranchissement
from services.storage import StorageService
from models.public import EnveloppePublic, AjoutAffranchissement, ModificationAffranchissement
from constants.enumerations import FrontendActionEnum
from models.public import CreationEnveloppe, EnveloppeItemPublic
from models.users import  UserRole
from services.lot_expediteur import ServiceLotExpediteur

router = APIRouter()


def get_enveloppe_by_id(
    enveloppe_id: int,
    session: SessionDep,
    current_user: CurrentUser
) -> Enveloppe:
    """
    Récupère une enveloppe par son ID.
    Si l'utilisateur est un utilisateur normal, on ne récupère que les enveloppes de l'utilisateur
    """
    # Construction de la requête de base
    query = session.query(Enveloppe).options(
        joinedload(Enveloppe.affranchissements),
        joinedload(Enveloppe.photos),
        joinedload(Enveloppe.site),
        joinedload(Enveloppe.destination),
        joinedload(Enveloppe.expediteur),
        joinedload(Enveloppe.user),
        joinedload(Enveloppe.casier)
    ).filter(Enveloppe.id == enveloppe_id).order_by(Enveloppe.created_at.desc())

    # If not superuser, filter on user_id
    if current_user.role == UserRole.USER:
        query = query.filter(Enveloppe.user_id == current_user.id)

    enveloppe = query.first()

    if not enveloppe:
        raise HTTPException(status_code=404, detail="Enveloppe non trouvée")
    
    # Tri des affranchissements par ID pour garantir un ordre cohérent
    enveloppe.affranchissements.sort(key=lambda x: x.id)
    
    return enveloppe


def get_modifiable_enveloppe(
    enveloppe_id: int | None,
    session: SessionDep,
    current_user: CurrentUser
) -> Enveloppe:
    """
    Récupère une enveloppe et vérifie qu'elle est encore modifiable.
    """
    enveloppe = get_enveloppe_by_id(enveloppe_id=enveloppe_id, session=session, current_user=current_user)
    
    #TODO:METIER: a quel moment une enveloppe devient-elle modifiable / immuable ?
    if not enveloppe.informations.modifiable:
        raise HTTPException(
            status_code=400,
            detail="Cette enveloppe n'est plus en mode EDITION"
        )

    return enveloppe


@router.get("/edition", response_model=EnveloppeItemPublic)
def get_enveloppe_edtion(
    *,
    session: SessionDep,
    current_user: CurrentUser
) -> EnveloppeItemPublic:
    """
    Récupère l'enveloppe en cours d'édition pour l'utilisateur courant
    Ou créer une enveloppe en mode edition
    """
    enveloppe = ServiceEnveloppe.enveloppe_en_edition(current_user, session)
    session.refresh(enveloppe)
    return enveloppe


@router.get("/{enveloppe_id}", response_model=EnveloppeItemPublic)
def get_enveloppe(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    enveloppe: Enveloppe = Depends(get_enveloppe_by_id)
) -> EnveloppeItemPublic:
    """
    Récupère une enveloppe existante.
    """
    return enveloppe

@router.post("/", response_model=EnveloppeItemPublic)
def update_enveloppe(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    data: CreationEnveloppe
) -> EnveloppeItemPublic:
    """
    Modifie une enveloppe existante ou crée une nouvelle
    """
    if data.site_id is None:
        data.site_id = current_user.site_id

    # Si pas d'id, on crée une nouvelle enveloppe
    if data.id is None:
        raise HTTPException(status_code=404, detail="Enveloppe non trouvée, aucun ID spécifié")
    
    enveloppe_existante = get_enveloppe_by_id(enveloppe_id=data.id, session=session, current_user=current_user)

    if not enveloppe_existante:
        raise HTTPException(status_code=404, detail="Enveloppe non trouvée")
    
    if not enveloppe_existante.informations.modifiable:
        raise HTTPException(status_code=400, detail="Cette enveloppe n'est plus en mode EDITION")
    
    # Sauvegarder l'ancien expediteur_id avant la mise à jour
    ancien_expediteur_id = enveloppe_existante.expediteur_id
    
    # Mise à jour de l'enveloppe
    enveloppe_existante.updated_at = datetime.now()
    
    # Mise à jour de tous les champs
    update_data = data.dict(exclude={"id"})
    
    for field, value in update_data.items():
        if value is not None:
            setattr(enveloppe_existante, field, value)
        
    session.add(enveloppe_existante)
    session.commit()
    session.refresh(enveloppe_existante)
    
    # Si l'expéditeur a changé, réassigner l'enveloppe au bon lot
    if enveloppe_existante.statut != StatutEnveloppeEnum.EDITION and ancien_expediteur_id != enveloppe_existante.expediteur_id and enveloppe_existante.expediteur_id is not None:
        ServiceLotExpediteur(session).reassigner_enveloppe_apres_changement_expediteur(
            enveloppe_existante, ancien_expediteur_id
        )
        session.refresh(enveloppe_existante)
    
    return enveloppe_existante



@router.post("/{enveloppe_id}/add", response_model=EnveloppePublic)
def add_affranchissement_to_enveloppe(
    *,
    enveloppe: Enveloppe = Depends(get_modifiable_enveloppe),
    data: AjoutAffranchissement,
    session: SessionDep,
    current_user: CurrentUser
) -> EnveloppePublic:
    """
    Add an Affranchissement to an existing Enveloppe
    """
    frontend_action = FrontendActionEnum.RIEN

    # Récupère les données de l'affranchissement non vides
    data = {k: v for k, v in data.dict().items() if v is not None}
    
    # Crée l'affranchissement
    affranchissement = ServiceAffranchissement.create_affranchissement(data)
    affranchissement.enveloppe_id = enveloppe.id
    affranchissement.user_id = current_user.id
    
    # Update des champs manquants
    affranchissement.service.update(data)

    if affranchissement.informations.complet:
        # Effectue les verifications
        from services.validateurs.validateur_affranchissement import ValidateurAffranchissement
        ValidateurAffranchissement.executer_validations(affranchissement)
    
        enveloppe.updated_at = datetime.now()

        session.add(enveloppe)
        session.add(affranchissement)
        session.commit()
        session.refresh(enveloppe)

    return EnveloppePublic(
        enveloppe=enveloppe,
        affranchissement=affranchissement,
        frontend_action=frontend_action
    )

@router.put("/{enveloppe_id}/affranchissements/{affranchissement_id}", response_model=EnveloppePublic)
def update_affranchissement(
    *,
    enveloppe: Enveloppe = Depends(get_modifiable_enveloppe),
    affranchissement_id: int,
    data: ModificationAffranchissement,
    session: SessionDep,
    current_user: CurrentUser
) -> EnveloppePublic:
    """
    Met à jour un Affranchissement existant dans une Enveloppe.
    Principalement appelé après l'ajout d'un affranchissement numérique type CODE.
    """        
    affranchissement = next(
        (aff for aff in enveloppe.affranchissements if aff.id == affranchissement_id),
        None
    )
    
    if not affranchissement:
        raise HTTPException(status_code=404, detail="Affranchissement non trouvé")
             
    affranchissement.service.update(data.dict())

    # L'Affranchissement est-il complet ?
    if not affranchissement.informations.complet:
        raise ValueError(f"L'affranchissement est incomplet: {affranchissement.informations.champs_manquants}")

    # Met à jour l'enveloppe       
    session.add(affranchissement)
    session.add(enveloppe)
    session.commit()
    session.refresh(enveloppe)
    
    return EnveloppePublic(
        enveloppe=enveloppe,
        affranchissement=affranchissement,
        frontend_action=FrontendActionEnum.RIEN
    )

@router.delete("/{enveloppe_id}/affranchissements/{affranchissement_id}", response_model=EnveloppePublic)
def delete_affranchissement(
    *,
    enveloppe: Enveloppe = Depends(get_modifiable_enveloppe),
    affranchissement_id: int,
    session: SessionDep,
    current_user: CurrentUser
) -> EnveloppePublic:
    """
    Supprime un Affranchissement existant d'une Enveloppe.
    """
    affranchissement = next(
        (aff for aff in enveloppe.affranchissements if aff.id == affranchissement_id),
        None
    )
    
    if not affranchissement:
        raise HTTPException(status_code=404, detail="Affranchissement non trouvé")
    
    try:
        # Suppression de l'affranchissement
        session.delete(affranchissement)
        
        # Mise à jour de l'enveloppe
        enveloppe.updated_at = datetime.now()
        enveloppe.statut = StatutEnveloppeEnum.EDITION
        session.add(enveloppe)
        session.commit()
        session.refresh(enveloppe)
        
    except Exception as e:
        session.rollback()
        raise HTTPException(status_code=400, detail=str(e))
    
    return EnveloppePublic(
        enveloppe=enveloppe,
        affranchissement=None,
        frontend_action=FrontendActionEnum.RIEN
    )

@router.delete("/{enveloppe_id}/affranchissements", response_model=EnveloppePublic)
def delete_all_affranchissements(
    *,
    enveloppe: Enveloppe = Depends(get_modifiable_enveloppe),
    session: SessionDep,
    current_user: CurrentUser
) -> EnveloppePublic:
    """
    Supprime tous les affranchissements d'une enveloppe modifiable.
    """
    try:
        # Suppression de tous les affranchissements
        for affranchissement in enveloppe.affranchissements:
            if affranchissement.user_id != current_user.id:
                raise HTTPException(
                    status_code=403,
                    detail="Vous n'avez pas le droit de supprimer certains affranchissements"
                )
            session.delete(affranchissement)
        
        # Mise à jour de l'enveloppe
        enveloppe.updated_at = datetime.now()
        enveloppe.statut = StatutEnveloppeEnum.EDITION
        session.add(enveloppe)
        session.commit()
        session.refresh(enveloppe)
        
    except Exception as e:
        session.rollback()
        raise HTTPException(status_code=400, detail=str(e))
    
    return EnveloppePublic(
        enveloppe=enveloppe,
        affranchissement=None,
        frontend_action=FrontendActionEnum.RIEN
    )


@router.post("/{enveloppe_id}/terminer", response_model=EnveloppeItemPublic)
def terminer_enveloppe(
    *,
    enveloppe: Enveloppe = Depends(get_modifiable_enveloppe),
    session: SessionDep,
    current_user: CurrentUser
) -> Enveloppe:
    """
    Termine une enveloppe valorisée, devient immuable
    Si l'enveloppe est frauduleuse, vérifie si un lot doit être créé pour l'expéditeur
    """
    
    # if not enveloppe.informations.modifiable:
    #     raise HTTPException(status_code=400, detail="Enveloppe déjà terminée")
    
    if not enveloppe.informations.complet:
        raise HTTPException(status_code=400, detail="L'enveloppe n'est pas complète, certains affranchissements sont incomplets ou photos manquantes")
    
    # Si c'est la première fois que l'enveloppe passe de EDITION à un autre statut
    if enveloppe.statut == StatutEnveloppeEnum.EDITION:
        enveloppe.completed_at = datetime.now()

    enveloppe.statut = StatutEnveloppeEnum.TERMINEE
    
    session.add(enveloppe)
    session.commit()
    session.refresh(enveloppe)
    
    # Gestion du lot d'expéditeur
    ServiceLotExpediteur(session).lot_expediteur(enveloppe)

    return enveloppe


@router.post("/photos/token", response_model=EnveloppeItemPublic)
async def add_photo_to_enveloppe(
    *,
    file: UploadFile = File(...),
    session: SessionDep,
    current_user: CurrentUser
) -> Enveloppe:
    """
    Ajoute une photo à une enveloppe en utilisant le token privé de l'utilisateur
    puis récupère l'enveloppe en mode édition ou valorisée associée à cet utilisateur
    """
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="Le fichier doit être une image")

    # # Recherche de l'utilisateur par son token
    # user = session.query(User).filter(User.token_ajout_photo == token).first()
    
    # if not user:
    #     raise HTTPException(status_code=401, detail="Token invalide")
    
    # Recherche de toutes les enveloppes en mode EDITION ou VALORISE pour cet utilisateur
    enveloppe = ServiceEnveloppe.enveloppe_en_edition(current_user, session)
    
    # Chargement des relations
    session.refresh(enveloppe)
    
    try:
        ServiceEnveloppe(enveloppe).ajouter_photo(file)        
        session.add(enveloppe)
        session.commit()
        session.refresh(enveloppe)
        
    except Exception as e:
        session.rollback()
        raise HTTPException(status_code=400, detail=str(e))
    
    return enveloppe


@router.post("/{enveloppe_id}/photos", response_model=EnveloppeItemPublic)
async def add_photo(
    *,
    enveloppe: Enveloppe = Depends(get_modifiable_enveloppe),
    file: UploadFile = File(...),
    session: SessionDep,
    current_user: CurrentUser
) -> Enveloppe:
    """
    Ajoute une photo à une enveloppe.
    Nécessite d'être authentifié et d'être le propriétaire de l'enveloppe.
    """
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="Le fichier doit être une image")
    
    try:
        ServiceEnveloppe(enveloppe).ajouter_photo(file)  

        session.add(enveloppe)
        session.commit()
        session.refresh(enveloppe)
        
    except Exception as e:
        session.rollback()
        raise HTTPException(status_code=400, detail=str(e))
    
    return enveloppe


@router.delete("/{enveloppe_id}/photos/{photo_id}", response_model=EnveloppeItemPublic)
def delete_photo(
    *,
    enveloppe: Enveloppe = Depends(get_modifiable_enveloppe),
    photo_id: int,
    session: SessionDep,
    current_user: CurrentUser
) -> Enveloppe:
    """
    Supprime une photo d'une enveloppe.
    Nécessite d'être authentifié et d'être le propriétaire de l'enveloppe.
    """
    photo = next(
        (photo for photo in enveloppe.photos if photo.id == photo_id),
        None
    )
    
    if not photo:
        raise HTTPException(status_code=404, detail="Photo non trouvée")
    
    try:
        # Suppression du fichier dans Azure Blob Storage
        try:
            StorageService.delete_file(photo.url)
        except Exception as e:
            pass
        
        # Suppression de l'entrée PhotoEnveloppe
        session.delete(photo)
        
        # Mise à jour de l'enveloppe
        enveloppe.updated_at = datetime.now()
        session.add(enveloppe)
        session.commit()
        session.refresh(enveloppe)
        
    except Exception as e:
        session.rollback()
        raise HTTPException(status_code=400, detail=str(e))
    
    return enveloppe


@router.delete("/{enveloppe_id}", response_model=EnveloppeItemPublic)
def delete_enveloppe(
    *,
    enveloppe: Enveloppe = Depends(get_enveloppe_by_id),
    session: SessionDep,
    current_user: CurrentUser
) -> EnveloppeItemPublic:
    """
    Supprime une enveloppe existante.
    Nécessite d'être authentifié et d'être le propriétaire de l'enveloppe.
    """
    # Vérification que l'utilisateur est le propriétaire de l'enveloppe
    if current_user.role != UserRole.ADMIN and enveloppe.user_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="Vous n'avez pas le droit de supprimer cette enveloppe"
        )
    
    try:
        # Suppression des photos associées
        for photo in enveloppe.photos:
            try:
                StorageService.delete_file(photo.url)
            except Exception as e:
                pass
            session.delete(photo)
        
        # Suppression des affranchissements associés
        for affranchissement in enveloppe.affranchissements:
            session.delete(affranchissement)
        
        # Suppression de l'enveloppe
        session.delete(enveloppe)
        session.commit()
        
    except Exception as e:
        session.rollback()
        raise HTTPException(status_code=400, detail=str(e))
    
    return enveloppe


@router.get("/{enveloppe_id}/photos", response_model=List[PhotoEnveloppe])
async def get_photos(
    *,
    enveloppe: Enveloppe = Depends(get_enveloppe),
    session: SessionDep,
    current_user: CurrentUser
) -> List[PhotoEnveloppe]:
    """
    Récupère uniquement les photos d'une enveloppe.
    """
    return enveloppe.photos


# @router.post("/{enveloppe_id}/changer-casier", response_model=EnveloppeItemPublic)
# def changer_casier_enveloppe(
#     *,
#     enveloppe: Enveloppe = Depends(get_modifiable_enveloppe),
#     session: SessionDep,
#     current_user: CurrentUser,
#     casier_id: int | None = None
# ) -> Enveloppe:
#     """
#     Assigne un nouveau casier à l'enveloppe.
#     Utile lorsqu'un utilisateur souhaite déplacer une enveloppe vers un autre casier.
#     L'utilisateur peut choisir un casier spécifique en fournissant casier_id.
#     """
    
#     # Vérifier que l'enveloppe a un lot d'expéditeur
#     if not enveloppe.casier or not enveloppe.lot_expediteur:
#         raise HTTPException(status_code=400, detail="L'enveloppe n'est pas associée à un lot d'expéditeur avec casier")
    
#     lot_expediteur = enveloppe.lot_expediteur
    
#     # Libérer l'ancien casier si nécessaire
#     ancien_casier = enveloppe.casier

#     if not ancien_casier:
#         raise HTTPException(status_code=400, detail="L'enveloppe n'est pas associée à un casier")

#     # ancien_casier.statut = StatutCasierEnum.PLEIN
    
#     # Obtenir un nouveau casier
#     if casier_id:
#         nouveau_casier = session.query(Casier).filter(Casier.id == casier_id, Casier.site_id == enveloppe.site_id).first()
#         if not nouveau_casier:
#             raise HTTPException(status_code=400, detail="Le casier spécifié n'existe pas ou n'appartient pas au même site")
#         if nouveau_casier.statut != StatutCasierEnum.DISPONIBLE:
#             raise HTTPException(status_code=400, detail="Le casier spécifié n'est pas disponible")
#         if nouveau_casier in lot_expediteur.casiers:
#             raise HTTPException(status_code=400, detail="Le casier spécifié est déjà utilisé par ce lot")
#         if len(nouveau_casier.enveloppes) != 0:
#             raise HTTPException(status_code=400, detail="Le casier n'est pas vide")
#     else:
#         nouveau_casier = ServiceCasier.obtenir_casier_disponible(enveloppe.site)
    
#     if not nouveau_casier:
#         raise HTTPException(status_code=400, detail="Aucun casier disponible")

#     # Assigner le nouveau casier à l'enveloppe
#     enveloppe.casier = nouveau_casier
#     enveloppe.lot_expediteur.casier = nouveau_casier
    
#     session.commit()
#     session.refresh(enveloppe)
    
#     return enveloppe

