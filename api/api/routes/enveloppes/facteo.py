from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlmodel import Session, select, SQLModel
from datetime import datetime
from typing import List
from api.deps import SessionDep, CurrentUser
from models.business import Enveloppe, Expediteur, EnveloppePublic, Destination, PhotoEnveloppe 
from services.storage import StorageService
from services.enveloppe import ServiceEnveloppe
import json
from constants.enumerations import SourceEnveloppeEnum


router = APIRouter()


class EnveloppeFacteo(SQLModel):
    poids: float  # en grammes
    photos: list[str] # liste des photos en base64
    donnees: dict # toutes les données autres et optionnelles


@router.post("/facteo", response_model=EnveloppePublic)
async def create_enveloppe_facteo(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    poids: float = Form(...),
    donnees: str = Form(...),  # JSON stringifié
    photos: list[UploadFile] = File(...)
) -> Enveloppe:
    # Vérification des photos
    for photo in photos:
        if not photo.content_type.startswith("image/"):
            raise HTTPException(
                status_code=400, 
                detail=f"Le fichier {photo.filename} doit être une image"
            )
    
    
    
    # Création de l'enveloppe
    enveloppe = ServiceEnveloppe.enveloppe_en_edition(current_user, session)
    enveloppe.source = SourceEnveloppeEnum.FACTEO
    enveloppe.poids = poids

    # Utilisation des données supplémentaires
    try:
        donnees_dict = json.loads(donnees)

        if donnees_dict.get("expediteur"):
            enveloppe.expediteur = Expediteur.cherche_ou_cree(session, donnees_dict["expediteur"])

        # if donnees_dict.get("destination"):
        #     destination = 
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Format JSON invalide pour donnees")
    
    service = ServiceEnveloppe(enveloppe)

    # Photos
    for photo in photos:
        service.ajouter_photo(photo)
    
    session.add(enveloppe)
    session.commit()
    session.refresh(enveloppe)

    return enveloppe
    