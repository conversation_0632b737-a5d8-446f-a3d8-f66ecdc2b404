from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session, select, func
from typing import List
from api.deps import SessionDep, CurrentAdminUser
from models import RegleMetier
from models.public import PaginatedResponse
from math import ceil


router = APIRouter()

@router.post("/", response_model=RegleMetier)
def create_regle_metier(
    *,
    session: SessionDep,
    current_admin: CurrentAdminUser,
    regle_metier: RegleMetier
) -> RegleMetier:
    """
    Créer une nouvelle règle métier (admin uniquement)
    """
    session.add(regle_metier)
    session.commit()
    session.refresh(regle_metier)
    return regle_metier

@router.get("/", response_model=PaginatedResponse[RegleMetier])
def read_regles_metier(
    *,
    session: SessionDep,
    current_admin: CurrentAdminUser,
    skip: int = 0,
    limit: int = 10,
    search: str | None = None,
) -> PaginatedResponse[RegleMetier]:
    """
    Récupérer la liste paginée des règles métier (admin uniquement)
    """
    # ---- 1. Base query -------------------------------------------------------
    base_query = select(RegleMetier)
    if search:
        like = f"%{search}%"
        base_query = base_query.where(RegleMetier.cle.ilike(like))

    # ---- 2. Total items ------------------------------------------------------
    total_items: int = session.exec(
        select(func.count()).select_from(base_query.subquery())
    ).one()

    # ---- 3. Slice paginée ----------------------------------------------------
    items: list[RegleMetier] = session.exec(
           base_query.order_by(RegleMetier.cle.asc(), RegleMetier.id.asc())  
            .offset(skip)
            .limit(limit)
    ).all()

    # ---- 4. Response ---------------------------------------------------------
    return PaginatedResponse[RegleMetier](
        items=items,
        current_page=(skip // limit) + 1,
        page_size=limit,
        total_items=total_items,
        total_pages=ceil(total_items / limit) if limit else 1,
    )


@router.get("/{regle_metier_id}", response_model=RegleMetier)
def read_regle_metier(
    *,
    session: SessionDep,
    current_admin: CurrentAdminUser,
    regle_metier_id: int
) -> RegleMetier:
    """
    Récupérer une règle métier par ID (admin uniquement)
    """
    regle_metier = session.get(RegleMetier, regle_metier_id)
    if not regle_metier:
        raise HTTPException(status_code=404, detail="Règle métier non trouvée")
    return regle_metier

@router.put("/{regle_metier_id}", response_model=RegleMetier)
def update_regle_metier(
    *,
    session: SessionDep,
    current_admin: CurrentAdminUser,
    regle_metier_id: int,
    regle_metier: RegleMetier
) -> RegleMetier:
    """
    Mettre à jour une règle métier existante (admin uniquement)
    """
    db_regle_metier = session.get(RegleMetier, regle_metier_id)
    if not db_regle_metier:
        raise HTTPException(status_code=404, detail="Règle métier non trouvée")
    
    for key, value in regle_metier.dict(exclude_unset=True).items():
        setattr(db_regle_metier, key, value)
    
    session.add(db_regle_metier)
    session.commit()
    session.refresh(db_regle_metier)
    return db_regle_metier

@router.delete("/{regle_metier_id}", response_model=RegleMetier)
def delete_regle_metier(
    *,
    session: SessionDep,
    current_admin: CurrentAdminUser,
    regle_metier_id: int
) -> RegleMetier:
    """
    Supprimer une règle métier (admin uniquement)
    """
    regle_metier = session.get(RegleMetier, regle_metier_id)
    if not regle_metier:
        raise HTTPException(status_code=404, detail="Règle métier non trouvée")
    
    session.delete(regle_metier)
    session.commit()
    return regle_metier