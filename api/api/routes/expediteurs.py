from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import Session, select, and_
from typing import List, Optional, Dict, Any
from api.deps import SessionDep, CurrentUser
from models import Expediteur, Enveloppe
from models.public import RechercheExpediteur
import requests
from core.config import settings

router = APIRouter()

@router.post("/", response_model=Expediteur)
def create_expediteur(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    expediteur: Expediteur
) -> Expediteur:
    """
    Create a new Expediteur
    """
    session.add(expediteur)
    session.commit()
    session.refresh(expediteur)
    return expediteur

@router.get("/", response_model=List[Expediteur])
def read_expediteurs(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    skip: int = 0,
    limit: int = 10,
    search: str = None
) -> List[Expediteur]:
    """
    Retrieve a list of Expediteurs with optional search filter
    """
    query = select(Expediteur)
    
    if search:
        search = f"%{search}%"
        query = query.where(
            Expediteur.nom.ilike(search) | 
            Expediteur.prenom.ilike(search) |
            Expediteur.adresse.ilike(search) )

    expediteurs = session.exec(query.offset(skip).limit(limit)).all()
    return expediteurs

@router.get("/{expediteur_id}", response_model=Expediteur)
def read_expediteur(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    expediteur_id: int
) -> Expediteur:
    """
    Retrieve a single Expediteur by ID
    """
    expediteur = session.get(Expediteur, expediteur_id)
    if not expediteur:
        raise HTTPException(status_code=404, detail="Expediteur not found")
    return expediteur

@router.put("/{expediteur_id}", response_model=Expediteur)
def update_expediteur(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    expediteur_id: int,
    expediteur: Expediteur
) -> Expediteur:
    """
    Update an existing Expediteur
    """
    db_expediteur = session.get(Expediteur, expediteur_id)
    if not db_expediteur:
        raise HTTPException(status_code=404, detail="Expediteur not found")
    
    for key, value in expediteur.dict(exclude_unset=True).items():
        setattr(db_expediteur, key, value)
    
    session.add(db_expediteur)
    session.commit()
    session.refresh(db_expediteur)
    return db_expediteur

@router.delete("/{expediteur_id}", response_model=Expediteur)
def delete_expediteur(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    expediteur_id: int
) -> Expediteur:
    """
    Delete an Expediteur
    """
    expediteur = session.get(Expediteur, expediteur_id)
    if not expediteur:
        raise HTTPException(status_code=404, detail="Expediteur not found")
    
    session.delete(expediteur)
    session.commit()
    return expediteur

@router.post("/search", response_model=List[Expediteur])
def search_expediteur(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    search_params: RechercheExpediteur
) -> List[Expediteur]:
    """
    Recherche un expéditeur par son nom ou son adresse (ville, code postal)
    en utilisant un body JSON
    """
    query = select(Expediteur)
    
    # Construire les conditions de recherche
    conditions = []
    
    # Si on recherche par nom
    if search_params.nom:
        conditions.append(Expediteur.nom.ilike(f"%{search_params.nom}%"))


    # Si au moins un des champs (adresse, ville ou code postal est rempli)
    # alors on verifie qu'ils sont tous remplis et pas None
    adresse_fields = [search_params.adresse, search_params.ville, search_params.code_postal]

    # Si on recherche pour une adresse
    if any(field is not None for field in adresse_fields):       
        if any(field is None for field in adresse_fields):
            raise HTTPException("Il faut renseigner: adresse, code postal et ville")

        conditions.append(Expediteur.adresse.ilike(f"%{search_params.adresse}%"))
        conditions.append(Expediteur.ville.ilike(f"%{search_params.ville}%"))
        conditions.append(Expediteur.code_postal.ilike(f"%{search_params.code_postal}%"))

    # Appliquer les conditions si au moins une est spécifiée
    if conditions:
        query = query.where(and_(*conditions))
    else:
        return []
    
    print(query)
    
    # Exécuter la requête avec une limite
    expediteurs = session.exec(query.limit(10)).all()
    return expediteurs

@router.get("/pappers/search", response_model=Dict[str, Any])
def search_pappers(
    *,
    current_user: CurrentUser,
    q: str = Query(..., description="Terme de recherche (nom d'entreprise)"),
    per_page: int = Query(10, description="Nombre de résultats par page"),
    page: int = Query(1, description="Numéro de page")
) -> Dict[str, Any]:
    """
    Recherche une entreprise via l'API PAPPERS pour obtenir son SIRET
    """
    # URL de l'API PAPPERS
    api_url = "https://api.pappers.fr/v2/recherche"
    
    # Paramètres de la requête
    params = {
        "q": q,
        "per_page": per_page,
        "page": page,
        "api_token": settings.PAPPERS_API_KEY
    }
    
    # Appel à l'API PAPPERS
    try:
        response = requests.get(api_url, params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.HTTPError as e:
        raise HTTPException(status_code=e.response.status_code, detail=f"Erreur API PAPPERS: {e.response.text}")
    except requests.exceptions.RequestException as e:
        raise HTTPException(status_code=500, detail=f"Erreur de connexion à l'API PAPPERS: {str(e)}")
