from typing import Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlmodel import col, delete, func, select, or_
from fastapi.responses import StreamingResponse
import qrcode
import io
from models import Site
import uuid
import crud
from api.deps import (
    CurrentUser,
    SessionDep,
    CurrentAdminUser,
    CurrentAllRoleUser
)
from core.config import settings
from core.security import get_password_hash, verify_password
from models.users import (
    Message,
    UpdatePassword,
    User,
    UserCreate,
    UserPublic,
    UserRegister,
    UserUpdate,
    UserUpdateMe,
    UserRole,
    UsersPaginatedPublic
)
from utils import generate_new_account_email, send_email

router = APIRouter()


@router.get("/", response_model=UsersPaginatedPublic)
def read_users(
    session: SessionDep, 
    admin: CurrentAdminUser, 
    page: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100),
    search: str | None = Query(None),
    role: UserRole | None = Query(None),
    site_id: int | None = Query(None)
) -> Any:
    """Retrieve users with pagination and filtering."""
    
    # Calculate offset
    skip = (page - 1) * pageSize
    
    # Base query
    query = select(User)
    count_query = select(func.count()).select_from(User)
    
    # Apply filters
    filters = []
    
    if search:
        search_filter = or_(
            col(User.email).ilike(f"%{search}%"),
            col(User.full_name).ilike(f"%{search}%"),
            col(User.pseudo).ilike(f"%{search}%")
        )
        filters.append(search_filter)
    
    if role:
        filters.append(col(User.role) == role)
    
    if site_id:
        filters.append(col(User.site_id) == site_id)
    
    # Apply filters to both queries
    if filters:
        query = query.where(*filters)
        count_query = count_query.where(*filters)
    
    # Execute count query
    total_items = session.exec(count_query).one()
    
    # Execute main query with pagination
    query = query.offset(skip).limit(pageSize)
    users = session.exec(query).all()
    
    # Calculate pagination info
    total_pages = (total_items + pageSize - 1) // pageSize
    
    return UsersPaginatedPublic(
        items=users,
        total_items=total_items,
        total_pages=total_pages,
        current_page=page,
        page_size=pageSize
    )



@router.post(
    "/", response_model=UserPublic
)
def create_user(*, session: SessionDep, user_in: UserCreate, admin: CurrentAdminUser) -> Any:
    """
    Create new user.
    """
    user = crud.get_user_by_email(session=session, email=user_in.email)
    if user:
        raise HTTPException(
            status_code=400,
            detail="The user with this email already exists in the system.",
        )
    
    if user_in.site_id is None:
        user_in.site_id = session.exec(select(Site)).first().id

    user = crud.create_user(session=session, user_create=user_in)
    if settings.emails_enabled and user_in.email:
        email_data = generate_new_account_email(
            email_to=user_in.email, username=user_in.email, password=user_in.password
        )
        send_email(
            email_to=user_in.email,
            subject=email_data.subject,
            html_content=email_data.html_content,
        )
    return user


@router.patch("/me", response_model=UserPublic)
def update_user_me(
    *, session: SessionDep, user_in: UserUpdateMe, current_user: CurrentAllRoleUser
) -> Any:
    """
    Update own user.
    """

    if user_in.email:
        existing_user = crud.get_user_by_email(session=session, email=user_in.email)
        if existing_user and existing_user.id != current_user.id:
            raise HTTPException(
                status_code=409, detail="User with this email already exists"
            )
    user_data = user_in.model_dump(exclude_unset=True)
    current_user.sqlmodel_update(user_data)
    session.add(current_user)
    session.commit()
    session.refresh(current_user)
    return current_user


@router.patch("/me/password", response_model=Message)
def update_password_me(
    *, session: SessionDep, body: UpdatePassword, current_user: CurrentUser
) -> Any:
    """
    Update own password.
    """
    if not verify_password(body.current_password, current_user.hashed_password):
        raise HTTPException(status_code=400, detail="Incorrect password")
    if body.current_password == body.new_password:
        raise HTTPException(
            status_code=400, detail="New password cannot be the same as the current one"
        )
    hashed_password = get_password_hash(body.new_password)
    current_user.hashed_password = hashed_password
    session.add(current_user)
    session.commit()
    return Message(message="Password updated successfully")


@router.get("/me", response_model=UserPublic)
def read_user_me(current_user: CurrentUser) -> Any:
    """
    Get current user.
    """

    return current_user


@router.delete("/me", response_model=Message)
def delete_user_me(session: SessionDep, current_user: CurrentUser) -> Any:
    """
    Delete own user.
    """
    if current_user.role == UserRole.ADMIN:
        raise HTTPException(
            status_code=403, detail="Super users are not allowed to delete themselves"
        )
    session.delete(current_user)
    session.commit()
    return Message(message="User deleted successfully")


@router.post("/signup", response_model=UserPublic)
def register_user(session: SessionDep, user_in: UserRegister) -> Any:
    """
    Create new user without the need to be logged in.
    """
    if not settings.USERS_OPEN_REGISTRATION:
        raise HTTPException(
            status_code=403,
            detail="Open user registration is forbidden on this server",
        )
    user = crud.get_user_by_email(session=session, email=user_in.email)
    if user:
        raise HTTPException(
            status_code=400,
            detail="The user with this email already exists in the system",
        )
    user_create = UserCreate.model_validate(user_in)
    user = crud.create_user(session=session, user_create=user_create)
    return user


@router.get("/{user_id}", response_model=UserPublic)
def read_user_by_id(
    user_id: int, session: SessionDep, current_user: CurrentUser
) -> Any:
    """
    Get a specific user by id.
    """
    user = session.get(User, user_id)
    if user == current_user:
        return user
    if not current_user.role == UserRole.ADMIN:
        raise HTTPException(
            status_code=403,
            detail="The user doesn't have enough privileges",
        )
    return user


@router.patch(
    "/{user_id}",
    response_model=UserPublic,
)
def update_user(
    *,
    session: SessionDep,
    user_id: int,
    user_in: UserUpdate,
    admin: CurrentAdminUser
) -> UserPublic:
    """
    Update a user.
    """

    db_user = session.get(User, user_id)
    if not db_user:
        raise HTTPException(
            status_code=404,
            detail="The user with this id does not exist in the system",
        )
    if user_in.email:
        existing_user = crud.get_user_by_email(session=session, email=user_in.email)
        if existing_user and existing_user.id != user_id:
            raise HTTPException(
                status_code=409, detail="User with this email already exists"
            )

    db_user = crud.update_user(session=session, db_user=db_user, user_in=user_in)
    
    return db_user


@router.delete("/{user_id}")
def delete_user(
    session: SessionDep, current_user: CurrentUser, user_id: int, admin: CurrentAdminUser
) -> Message:
    """
    Delete a user.
    """
    user = session.get(User, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if user == current_user:
        raise HTTPException(
            status_code=403, detail="Super users are not allowed to delete themselves"
        )
    session.delete(user)
    session.commit()
    return Message(message="User deleted successfully")


@router.get("/me/qrcode")
def generate_qrcode(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    new_token: bool = False
) -> StreamingResponse:
    """
    Génère un QR code contenant le token photo de l'utilisateur
    
    Args:
        new_token: Si True, génère un nouveau token même si l'utilisateur en a déjà un
    """

    from tests.utils.user import get_token_for_user
    from datetime import timedelta
    token = get_token_for_user(current_user, mode="photo", expires_delta=timedelta(days=5))

    # Création du QR code
    qr = qrcode.QRCode(version=1, box_size=10, border=5)
    qr.add_data(token)
    qr.make(fit=True)

    # Génération de l'image
    img = qr.make_image(fill_color="black", back_color="white")
    
    # Conversion en bytes pour le streaming
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format='PNG')
    img_byte_arr.seek(0)

    return StreamingResponse(img_byte_arr, media_type="image/png")