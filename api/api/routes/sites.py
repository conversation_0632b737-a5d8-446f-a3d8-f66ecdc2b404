from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session, select
from typing import List
from api.deps import SessionDep, CurrentUser
from models import Site

router = APIRouter()

@router.post("/", response_model=Site)
def create_site(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    site: Site
) -> Site:
    """
    Create a new Site
    """
    session.add(site)
    session.commit()
    session.refresh(site)
    
    print(session)
    
    return site

@router.get("/", response_model=List[Site])
def read_sites(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    skip: int = 0,
    limit: int = 10,
    search: str = None
) -> List[Site]:
    """
    Retrieve a list of Sites with optional search filter
    """
    query = select(Site)
    
    if search:
        search = f"%{search}%"
        query = query.where(Site.nom.ilike(search))

    sites = session.exec(query.offset(skip).limit(limit)).all()
    return sites

@router.get("/{site_id}", response_model=Site)
def read_site(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    site_id: int
) -> Site:
    """
    Retrieve a single Site by ID
    """
    site = session.get(Site, site_id)
    if not site:
        raise HTTPException(status_code=404, detail="Site not found")
    return site

@router.put("/{site_id}", response_model=Site)
def update_site(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    site_id: int,
    site: Site
) -> Site:
    """
    Update an existing Site
    """
    db_site = session.get(Site, site_id)
    if not db_site:
        raise HTTPException(status_code=404, detail="Site not found")
    
    for key, value in site.dict(exclude_unset=True).items():
        setattr(db_site, key, value)
    
    session.add(db_site)
    session.commit()
    session.refresh(db_site)
    return db_site

@router.delete("/{site_id}", response_model=Site)
def delete_site(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    site_id: int
) -> Site:
    """
    Delete a Site
    """

    raise HTTPException(status_code=400, detail="Interdit de supprimer un site")

    site = session.get(Site, site_id)
    if not site:
        raise HTTPException(status_code=404, detail="Site not found")
    
    session.delete(site)
    session.commit()
    return site 