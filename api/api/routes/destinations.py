from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session, select
from typing import List
from api.deps import SessionDep, CurrentUser
from models import Destination

router = APIRouter()

@router.post("/", response_model=Destination)
def create_destination(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    destination: Destination
) -> Destination:
    """
    Create a new Destination
    """
    session.add(destination)
    session.commit()
    session.refresh(destination)
    return destination

@router.get("/", response_model=List[Destination])
def read_destinations(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    skip: int = 0,
    limit: int = 10,
    search: str = None
) -> List[Destination]:
    """
    Retrieve a list of Destinations with optional search filter
    """
    query = select(Destination)
    
    if search:
        search = f"%{search}%"
        query = query.where(
            Destination.nom_fr.ilike(search) | 
            Destination.nom_en.ilike(search) |
            Destination.alpha2.ilike(search) |
            Destination.alpha3.ilike(search)
        )

    destinations = session.exec(query.offset(skip).limit(limit)).all()
    return destinations

@router.get("/{destination_id}", response_model=Destination)
def read_destination(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    destination_id: int
) -> Destination:
    """
    Retrieve a single Destination by ID
    """
    destination = session.get(Destination, destination_id)
    if not destination:
        raise HTTPException(status_code=404, detail="Destination not found")
    return destination

@router.put("/{destination_id}", response_model=Destination)
def update_destination(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    destination_id: int,
    destination: Destination
) -> Destination:
    """
    Update an existing Destination
    """
    db_destination = session.get(Destination, destination_id)
    if not db_destination:
        raise HTTPException(status_code=404, detail="Destination not found")
    
    for key, value in destination.dict(exclude_unset=True).items():
        setattr(db_destination, key, value)
    
    session.add(db_destination)
    session.commit()
    session.refresh(db_destination)
    return db_destination

@router.delete("/{destination_id}", response_model=Destination)
def delete_destination(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    destination_id: int
) -> Destination:
    """
    Delete a Destination
    """
    destination = session.get(Destination, destination_id)
    if not destination:
        raise HTTPException(status_code=404, detail="Destination not found")
    
    session.delete(destination)
    session.commit()
    return destination 