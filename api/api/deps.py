from collections.abc import Generator
from typing import Annotated
import jwt
import json
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2<PERSON><PERSON>wordBearer
from jwt.exceptions import InvalidTokenError
from pydantic import ValidationError
from sqlmodel import Session
from core import security
from core.config import settings
from models.users import User, UserRole, TokenPayload


reusable_oauth2 = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/login/access-token"
)


def get_db() -> Generator[Session, None, None]:
    from core.db import get_session
    with get_session() as session:
        yield session


SessionDep = Annotated[Session, Depends(get_db)]
TokenDep = Annotated[str, Depends(reusable_oauth2)]


def get_current_user_jwt(session: SessionDep, token: TokenDep) -> User:
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[security.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
    except (InvalidTokenError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )
    user = session.get(User, token_data.sub)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    if not user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return user



from core.keycloak import get_current_user_from_keycloak, valid_access_token, oauth2_scheme

async def get_current_user(session: SessionDep, token: TokenDep) -> User:
    # 1. Essayer d'abord avec JWT
    try:
        return get_current_user_jwt(session, token)
    except Exception:
        pass

    # 2. Si JWT échoue, essayer avec Keycloak
    try:
        token_data = await valid_access_token(token)
        user = await get_current_user_from_keycloak(token_data, session)
        if user:
            return user
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Impossible de valider les identifiants"
        )




CurrentAllRoleUser = Annotated[User, Depends(get_current_user)]


# Fonction générique pour vérifier le rôle de l'utilisateur
def get_current_user_with_role(current_user: CurrentAllRoleUser, required_role: UserRole) -> User:
    role_hierarchy = {
        UserRole.USER: [UserRole.USER, UserRole.MANAGER, UserRole.ADMIN],
        UserRole.MANAGER: [UserRole.MANAGER, UserRole.ADMIN],
        UserRole.ADMIN: [UserRole.ADMIN]
    }
    if current_user.role not in role_hierarchy.get(required_role, []):
        raise HTTPException(
            status_code=403, detail="L'utilisateur n'a pas les privilèges suffisants : " + required_role
        )
    return current_user


def get_current_active_user(current_user: CurrentAllRoleUser) -> User:
    if current_user.role == UserRole.GUEST:
        raise HTTPException(
            status_code=403, detail="L'utilisateur n'a pas les privilèges suffisants : USER" 
        )
    return current_user
    
def get_current_active_admin(current_user: CurrentAllRoleUser) -> User:
    return get_current_user_with_role(current_user, UserRole.ADMIN)

def get_current_active_manager(current_user: CurrentAllRoleUser) -> User:
    return get_current_user_with_role(current_user, UserRole.MANAGER)

def get_current_active_guest(current_user: CurrentAllRoleUser) -> User:
    return get_current_user_with_role(current_user, UserRole.GUEST)


# Annotations pour chaque rôle
CurrentUser = Annotated[User, Depends(get_current_active_user)]
CurrentAdminUser = Annotated[User, Depends(get_current_active_admin)]
CurrentManagerUser = Annotated[User, Depends(get_current_active_manager)]
CurrentGuestUser = Annotated[User, Depends(get_current_active_guest)]
