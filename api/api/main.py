from fastapi import APIRouter

from api.routes.users import users, login
from api.routes import (
    expediteurs,
    destinations,
    sites,
    affranchissements,
    enumerations,
    dashboard,
    casiers
)
from api.routes.enveloppes import gestion as gestion_enveloppes, edition as edition_enveloppes, facteo as facteo_enveloppes
from api.routes.admin import regles_metier
from api.routes.lots_expediteur import lots_expediteur, encaissements

api_router = APIRouter()

api_router.include_router(login.router, tags=["login"])
api_router.include_router(users.router, prefix="/users", tags=["users"])

# Enveloppes
api_router.include_router(edition_enveloppes.router, prefix="/enveloppes", tags=["enveloppes"])
api_router.include_router(gestion_enveloppes.router, prefix="/enveloppes", tags=["enveloppes"])
api_router.include_router(facteo_enveloppes.router, prefix="/enveloppes", tags=["enveloppes"])

api_router.include_router(affranchissements.router, prefix="/affranchissements", tags=["affranchissements"])

api_router.include_router(expediteurs.router, prefix="/expediteurs", tags=["expediteurs"])
api_router.include_router(destinations.router, prefix="/destinations", tags=["destinations"])
api_router.include_router(sites.router, prefix="/sites", tags=["sites"])

# api_router.include_router(natures.router, prefix="/natures", tags=["natures"])
api_router.include_router(enumerations.router, prefix="/enumerations", tags=["enumerations"])

api_router.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"],)

api_router.include_router(regles_metier.router, prefix="/admin/regles-metier", tags=["regles-metier"])

# Casier
api_router.include_router(casiers.router, prefix="/casiers", tags=["casiers"])

# Lot Expediteur
api_router.include_router(lots_expediteur.router, prefix="/lots-expediteurs", tags=["lots_expediteur"])
api_router.include_router(encaissements.router, prefix="/lots-expediteurs", tags=["lots_expediteur"])
