[tool.poetry]
name = "app"
version = "0.1.0"
description = ""
authors = ["Admin <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.10"
uvicorn = {extras = ["standard"], version = "^0.24.0.post1"}
fastapi = "^0.109.1"
python-multipart = "^0.0.7"
email-validator = "^2.1.0.post1"
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
tenacity = "^8.2.3"
pydantic = ">2.0"
emails = "^0.6"
gunicorn = "^22.0.0"
jinja2 = "^3.1.4"
alembic = "^1.12.1"
httpx = "^0.25.1"
psycopg = {extras = ["binary"], version = "^3.1.13"}
sqlmodel = "^0.0.19"
# Pin bcrypt until passlib supports the latest
bcrypt = "4.0.1"
pydantic-settings = "^2.2.1"
sentry-sdk = {extras = ["fastapi"], version = "^1.40.6"}
pyjwt = "^2.8.0"
pypandoc_binary = "^1.13"
sqlalchemy-utils = "^0.41.2"
selenium = "^4.25.0"
celery = "^5.4.0"
redis = "^5.1.1"
flower = "^2.0.1"
apscheduler = "^3.10.4"
azure-communication-email = "^1.0.0"
azure-storage-blob = "^12.24.1"
elasticsearch = "^8.17.1"
qrcode = "^8.0"
pillow = "^11.1.0"
alembic-postgresql-enum = "^1.7.0"
python-jose = "^3.4.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
mypy = "^1.8.0"
ruff = "^0.2.2"
pre-commit = "^3.6.2"
types-passlib = "^1.7.7.20240106"
coverage = "^7.4.3"
pytest-xdist = "^3.6.1"

[build-system]
requires = ["poetry>=0.12"]
build-backend = "poetry.masonry.api"

[tool.mypy]
strict = true
exclude = ["venv", ".venv", "alembic"]

[tool.ruff]
target-version = "py310"
exclude = ["alembic"]

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4",  # flake8-comprehensions
    "UP",  # pyupgrade
    "ARG001", # unused arguments in functions
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "W191",  # indentation contains tabs
    "B904",  # Allow raising exceptions without from e, for HTTPException
]

[tool.ruff.lint.pyupgrade]
# Preserve types, even if a file imports `from __future__ import annotations`.
keep-runtime-typing = true
