import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any

import emails  # type: ignore
import jwt
from jinja2 import Template
from jwt.exceptions import InvalidTokenError
from azure.communication.email import EmailClient

from core.config import settings


@dataclass
class EmailData:
    html_content: str
    subject: str


def render_email_template(*, template_name: str, context: dict[str, Any]) -> str:
    template_str = (
        Path(__file__).parent / "email-templates" / "build" / template_name
    ).read_text()
    html_content = Template(template_str).render(context)
    return html_content


def send_email(
    *,
    email_to: str,
    subject: str = "",
    html_content: str = "",
) -> None:
    assert settings.emails_enabled, "No provided configuration for email variables"
    try:
        connection_string = settings.AZURE_EMAIL_API_KEY
        client = EmailClient.from_connection_string(connection_string)

        message = {
            "senderAddress": settings.EMAILS_FROM_EMAIL,
            "recipients": {
                "to": [{"address": email_to}]
            },
            "content": {
                "subject": subject,
                "html": html_content,
            },
        }

        poller = client.begin_send(message)
        result = poller.result()
        logging.info(f"Email sent successfully, Message ID: {result.message_id}")

    except Exception as ex:
        logging.error(f"Error sending email: {ex}")


def generate_test_email(email_to: str) -> EmailData:
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - Test email"
    html_content = render_email_template(
        template_name="test_email.html",
        context={"project_name": settings.PROJECT_NAME, "email": email_to},
    )
    return EmailData(html_content=html_content, subject=subject)


def generate_reset_password_email(email_to: str, email: str, token: str) -> EmailData:
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - Password recovery for user {email}"
    link = f"{settings.server_host}/reset-password?token={token}"
    html_content = render_email_template(
        template_name="reset_password.html",
        context={
            "project_name": settings.PROJECT_NAME,
            "username": email,
            "email": email_to,
            "valid_hours": settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS,
            "link": link,
        },
    )
    return EmailData(html_content=html_content, subject=subject)


def generate_new_account_email(
    email_to: str, username: str, password: str
) -> EmailData:
    project_name = settings.PROJECT_NAME
    subject = f"{project_name} - New account for user {username}"
    html_content = render_email_template(
        template_name="new_account.html",
        context={
            "project_name": settings.PROJECT_NAME,
            "username": username,
            "password": password,
            "email": email_to,
            "link": settings.server_host,
        },
    )
    return EmailData(html_content=html_content, subject=subject)


def generate_password_reset_token(email: str) -> str:
    delta = timedelta(hours=settings.EMAIL_RESET_TOKEN_EXPIRE_HOURS)
    now = datetime.utcnow()
    expires = now + delta
    exp = expires.timestamp()
    encoded_jwt = jwt.encode(
        {"exp": exp, "nbf": now, "sub": email},
        settings.SECRET_KEY,
        algorithm="HS256",
    )
    return encoded_jwt


def verify_password_reset_token(token: str) -> str | None:
    try:
        decoded_token = jwt.decode(token, settings.SECRET_KEY, algorithms=["HS256"])
        return str(decoded_token["sub"])
    except InvalidTokenError:
        return None


from sqlalchemy.orm import RelationshipProperty

def to_dict(obj, seen_objects=None):
    """
    Convert an SQLAlchemy model instance into a dictionary.
    Handles nested relationships and avoids circular references.
    """
    if seen_objects is None:
        seen_objects = set()

    if isinstance(obj, list):
        return [to_dict(item, seen_objects) for item in obj]

    # Avoid re-serializing already seen objects to prevent recursion issues
    obj_id = id(obj)
    if obj_id in seen_objects:
        return None
    seen_objects.add(obj_id)

    # Convert columns (non-relationship fields) to dict
    result = {c.key: getattr(obj, c.key) for c in obj.__table__.columns}

    # Recursively convert relationships, but skip circular references
    for relationship in obj.__mapper__.relationships:
        related_value = getattr(obj, relationship.key)

        # If the relationship is lazy-loaded or not yet loaded, skip it
        if related_value is None or isinstance(related_value, RelationshipProperty):
            continue

        # Only include the related object if it hasn't been processed before
        if relationship.back_populates:
            # Skip back-references to avoid recursion
            continue

        result[relationship.key] = to_dict(related_value, seen_objects)

    return result
