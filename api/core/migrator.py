from elasticsearch import Elasticsearch

import json
from sqlmodel import select  # Ajoutez cette ligne pour importer 'select'
from azure.storage.blob import BlobServiceClient, generate_blob_sas, BlobSasPermissions
from sqlalchemy import create_engine, text
from alembic import command
from alembic.config import Config
from core.db import get_session
from sqlalchemy_utils import create_database, database_exists, drop_database
from core.config import settings
import os
from datetime import datetime
from core.utils.prix import convertir_prix_devise_en_euros
from models.business import Expediteur, Destination, CodeAffranchissementFrauduleux, RegleMetier  # Assurez-vous que ces modèles sont importés correctement
from services.affranchissement import ServiceAffranchissement
from services.validateurs.validateur_affranchissement import ValidateurAffranchissement

user = "elastic"
password = os.getenv("ELASTIC_PASSWORD")
es = Elasticsearch(f"https://elastic.fraude-server-sta.aks.sandbox.innovation-laposte.io:443"
                   , basic_auth=(user, password))


def reset_database():
    return
    # Get engine from session
    engine = get_session().get_bind()
    
    db_uri = f"{settings.SQLALCHEMY_DATABASE_URI}"
    engine = create_engine(db_uri)

    # Ensure the test database is fresh
    if database_exists(engine.url):
        drop_database(engine.url)
    create_database(engine.url)

    from alembic.config import Config
    from alembic import command

    # Générer une révision Alembic automatique
    # alembic_cfg = Config("/app/alembic.ini")
    # alembic_cfg.set_main_option("sqlalchemy.url", db_uri)
    # command.revision(alembic_cfg, autogenerate=True, message="Migration automatique")

    # Apply migrations using Alembic
    alembic_cfg = Config("/app/alembic.ini")  # Path to your Alembic configuration file
    alembic_cfg.set_main_option("sqlalchemy.url", db_uri)

    if input("Voulez-vous réinitialiser les migrations ? (y/n)") == "y":
        # Supprimer tous les fichiers de versions Alembic
        import os
        import glob

        versions_dir = "/app/alembic/versions"
        for f in glob.glob(os.path.join(versions_dir, "*.py")):
            os.remove(f)
        command.revision(alembic_cfg, autogenerate=True, message="Migration automatique")
        command.upgrade(alembic_cfg, "head")  # Apply all migrations to the latest state

    print("Base de données réinitialisée et migrations appliquées avec succès")


def get_chain(obj, path, default=None):
    path=path.split(".")
    for part in path:
        try:
            obj = obj[part]
        except (KeyError, IndexError):
            return default
    return obj


def migrate_main(session, source):
    import copy
    source_original = copy.deepcopy(source)  # Utiliser deepcopy pour une copie profonde
    original_valo = source_original.get("_source", {}).get('valo', {})
    
    from services.migration import ServiceMigration
    
    # On migre la data Elastic de kuzzle vers un modele SQLMOdel
    env = ServiceMigration.create_enveloppe(source, session=session)

    return
    
    if env.valorisation:
        # Store current valorisation values
        current_valorisation = copy.deepcopy(env.valorisation)

        
        # Recalculate valorisation pour comparer
        # from services.enveloppe import ServiceEnveloppe
        # ServiceEnveloppe(env).valoriser()        
        
        def compare_nested_dicts(d1, d2, tolerance=0.05):
            differences = []
            def compare_dicts(d1, d2, path=""):
                if isinstance(d1, dict) and isinstance(d2, dict):
                    pass
                    # for k in d1:
                    #     if k not in d2:
                    #         differences.append(f"{path}{k}: manquant dans dict2")
                    #     else:
                    #         compare_dicts(d1[k], d2[k], f"{path}{k}.")
                    # for k in d2:
                    #     if k not in d1:
                    #         differences.append(f"{path}{k}: manquant dans dict1")
                else:
                    if abs(float(d1) - float(d2)) > tolerance:
                        differences.append(f"{path}: {d1} vs {d2} : {abs(d1 - d2)}")
            compare_dicts(d1, d2)
            return differences

        differences = compare_nested_dicts(env.valorisation, current_valorisation)
        if differences:
            print("Différences trouvées dans la valorisation:")
            for diff in differences:
                print(f"- {diff}")
            raise Exception("Différence significative dans la valorisation")
        
    # Si c'est un numero, on test de réappliquer les validations
    elements = source_original.get("_source", {}).get("main", {}).get("elements", [])
    elements_with_code = [element for element in elements if element.get("code") is not None]

    # On réapplique les validations
    for element in elements_with_code:
        if "LL" in element.get("code").get("raw"):
            print(element)
            ka
        continue
        if element.get("fake") == False:
            # aff = ServiceAffranchissement.create_affranchissement({
            #     "code": element.get("code").get("raw"),
            # })
            # ValidateurAffranchissement.executer_validations(aff)
            # print(aff)
            print(element)
            print("--------------------------------")
            kaak
        

    return env

def display_data(session, source):
    print(source)

def migrate_expediteur(session, source):
    id = source.get('_id')
    data = source.get("_source", {}).get('sender', {})
   
    expediteur = Expediteur(
        nom=data.get('name').strip(),
        siret=data.get('siret'),
        adresse=data.get('address').strip(),
        coclico=data.get('coclico'),
        id_migration=id
    )

    session.add(expediteur)

def migrate_destination(session, source):
    id = source.get('_id')
    data = source.get("_source", {}).get('destination', {})

    destination = Destination(
        id_migration=id,
        nom_fr=data.get('nameFra').strip(),
        nom_en=data.get('nameEng').strip(),
        alpha2=data.get('alpha2'),
        alpha3=data.get('alpha3'),
        num=data.get('num'),
        zone=data.get('zone'),
        supplement=data.get('supplement'),
        created_at= datetime.fromtimestamp(source.get("_source", {}).get('_kuzzle_info').get('createdAt')/1000)
    )
    session.add(destination)


from core.db import get_session
session = get_session()


def apply_all_migrations():
    with get_session() as session:
        # if input("Voulez-vous réinitialiser la base de données ? (y/n)") == "y":
        #     reset_database()

        mapping_collection_importer = {
            # "&myapp.xtra_coll_sender": (migrate_expediteur, 100),
            # "&myapp.xtra_coll_destination": (migrate_destination, 100),

            "&myapp.xtra_coll_control_main3": (migrate_main, 200),
            # "&myapp.xtra_coll_wissous_code": None,
            # "&myapp.xtra_coll_control_code3": None,
            # "&myapp.xtra_coll_control_picture3": None,
            
            # "&myapp.base_coll_tech_sonde": None,
            # "&myapp.xtra_coll_control_element3": None,
        }

    
        # Parcourir chaque index dans le mapping
        for index, migrate_func in mapping_collection_importer.items():
            migrate_func, batch_size = migrate_func
            # Récupérer le nombre total de documents pour cet index
            count_response = es.count(index=index)
            total_docs = count_response['count']
            
            for start in range(0, total_docs, batch_size):
                print(f"Search [{start}-{min(start+batch_size, total_docs)}/{total_docs}] {index}", end=""); 
                search_response = es.search(index=index, 
                                            size=batch_size, 
                                            from_=start, 
                                            sort=['@timestamp'], 
                                            _source={'excludes': ['main.pictures']}); 
                print(" Done")
                
                # Traiter chaque document du lot
                for hit in search_response['hits']['hits']:
                    try:
                        migrate_func(session, hit)
                    except Exception as e:
                        raise
                        print(f"Erreur lors de la migration du document {hit['_id']}: {str(e)}")
                        continue
                
                try:
                    pass
                    # session.commit()
                except Exception as e:
                    if "already exists" in str(e):
                        session.rollback()
                    else:
                        raise
                
                # Valider les changements après chaque lot
                # session.commit()
                
                print(f"Traitement du lot {start}-{min(start+batch_size, total_docs)} terminé pour {index}")


    #     # each value of each main.elements.idCategory
    #     # simple getter
    #     for element in get_chain(source, "main.elements"):
    #         existing_catogry.add(element.get('idCategory'))
    #         existing_type.add(element.get('idMention'))

    # print(existing_catogry)
    # print(existing_type)

# {'&myapp.xtra_coll_control_code3': {'count': 9297, 'first_doc': dict_keys(['@timestamp', 'datetimeParis', 'code', 'main', '_kuzzle_info'])}, '&myapp.xtra_coll_control_picture3': {'count': 7710, 'first_doc': dict_keys(['_kuzzle_info', '@timestamp', 'datetimeParis', 'sender', 'destination', 'picture', 'main'])}, '&myapp.xtra_coll_control_element3': {'count': 24910, 'first_doc': dict_keys(['_kuzzle_info', '@timestamp', 'datetimeParis', 'sender', 'destination', 'element', 'main'])}, '&myapp.xtra_coll_sender': {'count': 282, 'first_doc': dict_keys(['@timestamp', 'datetimeParis', 'sender', '_kuzzle_info'])}, '&myapp.base_coll_tech_sonde': {'count': 8768, 'first_doc': dict_keys(['date', 'info', '_kuzzle_info'])}, '&myapp.xtra_coll_control_main3': {'count': 6765, 'first_doc': dict_keys(['@timestamp', 'datetimeParis', 'sender', 'destination', 'main', 'valo', '_kuzzle_info'])}, '&myapp.xtra_coll_destination': {'count': 249, 'first_doc': dict_keys(['@timestamp', 'datetimeParis', 'destination', '_kuzzle_info'])}, '&myapp.xtra_coll_wissous_code': {'count': 255620, 'first_doc': dict_keys(['_kuzzle_info', '@timestamp', 'datetimeParis', 'id', 'format'])}}

indexes = [
    "&myapp.xtra_coll_control_code3",
    "&myapp.xtra_coll_control_picture3",
    "&myapp.xtra_coll_control_element3",
    "&myapp.xtra_coll_sender",
    "&myapp.base_coll_tech_sonde",
    "&myapp.xtra_coll_control_main3",
    "&myapp.xtra_coll_destination",
    "&myapp.xtra_coll_wissous_code"
]
result = {

}
# # Fetch data from Elasticsearch
# for index in indexes:
#     # Get count
#     count_response = es.count(index=index)
#     # Get first document
#     search_response = es.search(index=index, size=1)
#     first_doc = None
#     if search_response['hits']['hits']:
#         first_doc = search_response['hits']['hits'][0]['_source']
    

#     result[index] = {
#         "count": count_response['count'],
#         "first_doc": first_doc.keys()
#     }
    
    
# print(result)
    


# if __name__ == "__main__":
#     with open('/data/data.json', 'r') as f:
#         data = json.load(f)
        
#     migrate(data[""])
session.close()

# Lister tous les index existants
# try:
#     # Récupérer tous les index
#     indices = es.indices.get_alias().keys()
#     print("Liste des index Elasticsearch:")
#     myapp_indices = [index for index in indices if "myapp" in index]
#     print(json.dumps(myapp_indices, indent=2))
# except Exception as e:
#     print(f"Erreur lors de la récupération des index: {str(e)}")

apply_all_migrations()

# # Import S10 sequence
# from database.import_s10_sequence import import_s10_sequence
# import_s10_sequence()

# # Import Wissou code
# from database.import_wissou_code import import_wissou_code
# import_wissou_code()

# # Create Sites


# items = [CodeAffranchissementFrauduleux, RegleMetier, Destination, Expediteur]

# for item in items:
#     count = session.query(item).count()
#     print(f"Nombre de {item.__name__}: {count}")