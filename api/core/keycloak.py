from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON>uthori<PERSON><PERSON><PERSON><PERSON>earer
from jose import jwt
from typing import Annotated, Optional
from pydantic import BaseModel
from core.config import settings
from models import User, UserRole
from api.deps import SessionDep
from jwt import PyJWKClient

# URL des clés publiques de Keycloak
jwks_url = f"{settings.KEYCLOAK_URL}/realms/{settings.KEYCLOAK_REALM}/protocol/openid-connect/certs"
jwks_client = PyJWKClient(jwks_url)


oauth2_scheme = OAuth2AuthorizationCodeBearer(
    authorizationUrl=f"{settings.KEYCLOAK_URL}/realms/{settings.KEYCLOAK_REALM}/protocol/openid-connect/auth",
    tokenUrl=f"{settings.KEYCLOAK_URL}/realms/{settings.KEYCLOAK_REALM}/protocol/openid-connect/token",
)

class TokenData(BaseModel):
    username: Optional[str] = None
    sub: Optional[str] = None
    realm_access: Optional[dict] = None

async def valid_access_token(
    access_token: Annotated[str, Depends(oauth2_scheme)]
):
    
    try:
        # Récupérer la clé de signature à partir du token
        signing_key = jwks_client.get_signing_key_from_jwt(access_token)
        
        # Décoder et vérifier le token
        payload = jwt.decode(
            access_token,
            signing_key.key,
            algorithms=["RS256"],
            audience=settings.KEYCLOAK_CLIENT_ID,
            options={"verify_exp": True, "verify_aud": False},  # Désactiver temporairement la vérification d'audience pour déboguer
        )
        return payload
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Token invalide ou expiré: {str(e)}",
            headers={"WWW-Authenticate": "Bearer"},
        )

# Création d'une annotation pour la dépendance du token validé
KeycloakTokenDep = Annotated[dict, Depends(valid_access_token)]

ROLES = {
    "admin": UserRole.ADMIN,
    "manager": UserRole.MANAGER,
    "user": UserRole.USER
}

def map_role_to_user_role(roles: list) -> str:
    """
    Détermine le meilleur rôle utilisateur en fonction des rôles Keycloak.
    Si le rôle exact n'est pas trouvé, retourne le rôle le moins privilégié disponible.
    
    Args:
        roles: Liste des rôles Keycloak de l'utilisateur
        
    Returns:
        Le rôle correspondant dans la base de données
    """
    if not roles or not isinstance(roles, list):
        return list(ROLES.values())[-1]  # Retourne le dernier rôle (moins privilégié)
    
    # Vérifier si un des rôles Keycloak correspond directement
    for keycloak_role, db_role in ROLES.items():
        if keycloak_role in roles:
            return db_role
    
    # Si aucun rôle correspondant n'est trouvé, retourner le rôle le moins privilégié
    return list(ROLES.values())[-1]

async def get_current_user_from_keycloak(
    token_data: KeycloakTokenDep,
    session: SessionDep
) -> User:
    
    # Extraire les informations du token
    email = token_data.get("email")
    
    if not email:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Informations d'utilisateur invalides",
        )
    
    roles = token_data.get("resource_access", {}).get(settings.KEYCLOAK_CLIENT_ID, {}).get("roles", [])
    
    # Vérifier si l'utilisateur existe dans notre base de données
    user = session.query(User).filter(User.email == email).first()
    
    # Si l'utilisateur n'existe pas, on peut le créer
    if not user:

        user = User(
            email=email,
            is_active=True,
            hashed_password="",
            role=UserRole.GUEST,
            full_name=f"{token_data.get('given_name', '')} {token_data.get('family_name', '')}".strip()
        )
        session.add(user)
        session.commit()
        session.refresh(user)
    else:
        session.commit()
        session.refresh(user)
        
    return user