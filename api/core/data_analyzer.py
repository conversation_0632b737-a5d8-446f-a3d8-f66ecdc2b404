from elasticsearch import Elasticsearch
import json
import os

user= "admin"
password= os.getenv("ELASTIC_PASSWORD")

es = Elasticsearch(f"https://elastic.fraude-server-sta.aks.sandbox.innovation-laposte.io:443"
                   , basic_auth=(user, password))


def rechercher_valorisation(index_name):
    # Initialisation de la recherche avec scroll
    result = es.search(
        index=index_name,
        scroll='2m',  # Garde le scroll actif pendant 2 minutes
        body={
            "size": 1000,  # Nombre de documents par page
            "query": {
                "match_all": {}  # Récupère tous les documents
            },
            "_source": {
                "excludes": ["main.pictures"]  # Exclure le champ main.pictures
            }
        }
    )

    # Récupère l'identifiant de scroll
    scroll_id = result['_scroll_id']
    scroll_size = len(result['hits']['hits'])

    # Tant qu'il y a des résultats
    while scroll_size > 0:
        for hit in result['hits']['hits']:
            
            # Vérifie si le document contient des éléments avec un code commençant par %
            elements = hit.get('_source', {}).get('main', {}).get('elements', [])
            for element in elements:
                code = element.get('code', {}) or {}
                raw = code.get('raw', '')
                
                text_raw = json.dumps(raw).lower()

                if raw.startswith("LL"):
                    return element

                if raw == "LL014699171FR":
                    return element
                
                continue
                
                if "SD" in raw:
                    return(code)

                continue

                if raw == "%000000087000836448041376710A10^ad1257f":
                    return code
                    
                if raw and raw.strip().startswith('sd'):
                    return code
            
            # Vérifie si 'valorisation' est présent dans le document
            # if 'valo' in hit['_source']:
            #     return hit  # Retourne le premier document trouvé avec 'valorisation'

        # Récupère la page suivante
        result = es.scroll(scroll_id=scroll_id, scroll='2m')
        scroll_size = len(result['hits']['hits'])

    # Si rien n'est trouvé
    return None

def rechercher_code(index_name, code_to_search):
    # Initialisation de la recherche avec scroll
    result = es.search(
        index=index_name,
        scroll='2m',  # Garde le scroll actif pendant 2 minutes
        body={
            "size": 100,  # Nombre de documents par page
            "query": {
                "nested": {
                    "path": "main.elements",
                    "query": {
                        "term": {
                            "main.elements.code.raw": code_to_search
                        }
                    }
                }
            },
            "_source": {
                "excludes": ["main.pictures"]  # Exclure le champ main.pictures
            }
        }
    )

    # Récupère l'identifiant de scroll
    scroll_id = result['_scroll_id']
    scroll_size = len(result['hits']['hits'])

    # Tant qu'il y a des résultats
    while scroll_size > 0:
        for hit in result['hits']['hits']:
            for element in hit["_source"]["main"]["elements"]:
                code = element.get("code") or {}
                if code.get("raw", "").startswith(code_to_search):
                    if code.get("check", {}).get("grammar", {}).get("result", {}).get("details", []):
                        for detail in code["check"]["grammar"]["result"]["details"]:
                            if "key" in detail.lower() and "KO" in detail:
                                return element
                    
                    # return element
        
        # Récupère la page suivante
        result = es.scroll(scroll_id=scroll_id, scroll='2m')
        scroll_size = len(result['hits']['hits'])

    # Si rien n'est trouvé
    return None


if __name__ == "__main__":
    print(rechercher_valorisation("&myapp.xtra_coll_control_main3"))
    # print(rechercher_code("&myapp.xtra_coll_control_main3", "LL"))