from enum import Enum
def force_dict_enum_str(dict_value: dict):
    for key, value in dict_value.items():
        if isinstance(value, Enum):
            dict_value[key] = str(value).split(".")[1]
            
    return dict_value

class DotDict(dict):
    """Dictionnaire permettant l'accès par attribut et sérialisable en JSON"""
    __getattr__ = dict.get
    __setattr__ = dict.__setitem__
    __delattr__ = dict.__delitem__