from openpyxl import load_workbook
from pathlib import Path
from typing import List, Callable
from models.business import RegleMetier
from constants.enumerations import TypeRegleMetierEnum, TypeAffranchissementEnum
from datetime import datetime
from tqdm import tqdm
import pandas as pd


def process_xslx(file_path: str, process_batch: Callable[[List[dict]], None], batch_size: int = 100, sheet : int = 0) -> None:
    """
    Lit un fichier Excel et appelle une méthode pour traiter chaque lot de lignes.
    
    :param file_path: Chemin du fichier Excel
    :param process_batch: Méthode à appeler pour chaque lot de lignes
    :param batch_size: Nombre de lignes par lot
    """
    try:
        # Vérifier si le fichier existe
        if not Path(file_path).exists():
            raise FileNotFoundError(f"Le fichier {file_path} n'existe pas")
            
        # Charger le workbook et choisir la sheet
        wb = load_workbook(filename=file_path, read_only=True)
        ws = wb.worksheets[sheet]

        headers = [cell.value for cell in next(ws.rows)]
        print("Headers: ", headers)

        # Obtenez le nombre total de lignes pour la barre de progression
        total_rows = ws.max_row - 1 
        
        batch = []
        for i, row in enumerate(tqdm(ws.iter_rows(min_row=2), total=total_rows, desc="Traitement des lignes"), start=1):
            row_data = {headers[j]: str(cell.value) for j, cell in enumerate(row) if headers[j] is not None}
            batch.append(row_data)

            if i % batch_size == 0:
                # Appeler la méthode pour traiter le lot
                process_batch(batch)
                batch = []

        # Traitez le dernier lot s'il reste des lignes
        if batch:
            process_batch(batch)

        wb.close()
        
    except FileNotFoundError as fnf_error:
        print(fnf_error)
    except Exception as e:
        print(f"Erreur lors de l'importation du fichier Excel: {str(e)}")



import polars as pl
def extract_column_values(chemin_fichier, nom_colonne, index_sheet=0):
    """
    Utilise polars qui est optimisé pour les grandes données.
    """
    # Lire avec polars (nécessite d'installer polars: pip install polars)
    df = pl.read_excel(
        chemin_fichier,
        sheet_id=index_sheet+1,
        columns=[nom_colonne]
        # read_csv_options={"columns": [nom_colonne]}
    )
    return df[nom_colonne].to_list()
