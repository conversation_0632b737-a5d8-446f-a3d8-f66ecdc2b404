import sentry_sdk
from fastapi import FastAPI
from fastapi.routing import APIRoute
from starlette.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
from core.config import settings

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from core.db import get_session
from api.main import api_router
from core.keycloak import oauth2_scheme

def custom_generate_unique_id(route: APIRoute) -> str:
    return f"{route.tags[0]}-{route.name}"


if settings.SENTRY_DSN and settings.ENVIRONMENT != "local":
    sentry_sdk.init(dsn=str(settings.SENTRY_DSN), enable_tracing=True)

app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    generate_unique_id_function=custom_generate_unique_id,
    swagger_ui_init_oauth={
        "clientId": settings.KEYCLOAK_CLIENT_ID
    }
)

def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title="Votre API",
        version="1.0.0",
        description="Description de votre API",
        routes=app.routes,
    )
    
    # Définir plusieurs schémas de sécurité
    openapi_schema["components"]["securitySchemes"] = {
        "OAuth2": {
            "type": "oauth2",
            "flows": {
                "authorizationCode": {
                    "authorizationUrl": f"{settings.KEYCLOAK_URL}/realms/{settings.KEYCLOAK_REALM}/protocol/openid-connect/auth",
                    "tokenUrl": f"{settings.KEYCLOAK_URL}/realms/{settings.KEYCLOAK_REALM}/protocol/openid-connect/token",
                    "scopes": {
                        "openid": "OpenID Connect",
                        "profile": "Profil utilisateur",
                        "email": "Email utilisateur"
                    }
                }
            }
        }
    }

    # Ajouter security automatiquement sur chaque route
    for route in app.routes:
        if hasattr(route, "operation_id"):
            methods = route.methods or []
            for method in methods:
                method = method.lower()
                if method in openapi_schema["paths"][route.path]:
                    openapi_schema["paths"][route.path][method]["security"] = [{"OAuth2": ["openid", "profile", "email"]}]

    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi

# Set all CORS enabled origins
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[
            str(origin).strip("/") for origin in settings.BACKEND_CORS_ORIGINS
        ],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

app.include_router(api_router, prefix=settings.API_V1_STR)


@app.exception_handler(ValueError)
async def global_exception_handler(request, exc):
    from fastapi.responses import JSONResponse
    return JSONResponse(
        status_code=400,
        content={"detail": str(exc)}
    )

# CRON tasks
# Create the scheduler
# scheduler = BackgroundScheduler()
# scheduler.start()
#
# def hourly_task():
#     with get_session() as session:
#
# # Add the task to run every hour
# scheduler.add_job(hourly_task, trigger=IntervalTrigger(hours=1))

# @app.on_event("startup")
# async def startup_event():
#     if not scheduler.running:
#         scheduler.start()
#
# @app.on_event("shutdown")
# async def shutdown_event():
#     scheduler.shutdown()
