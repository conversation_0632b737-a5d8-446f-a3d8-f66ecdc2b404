# Use an official Python runtime as a parent image
FROM python:3.10-slim

# Set environment variables for Poetry and virtual environment location
ENV PYTHONUNBUFFERED=1 \
    POETRY_VERSION=1.8.1 \
    POETRY_HOME="/root/.local" \
    POETRY_VIRTUALENVS_PATH="/opt/venv"

# Add Poetry's bin directory and the virtual environment bin directory to PATH
ENV PATH="$POETRY_HOME/bin:/opt/venv/bin:$PATH"

# Set the working directory in the container
WORKDIR /app

# Install system dependencies (for building some Python packages) and PostgreSQL client
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    postgresql-client \
    curl \
    ca-certificates \
    gnupg \
    lsb-release 

# # PG 17
# RUN apt install -y postgresql-common
# RUN yes | /usr/share/postgresql-common/pgdg/apt.postgresql.org.sh
# RUN apt install -y postgresql-client-17

# Install Poetry
RUN pip install "poetry==$POETRY_VERSION"

# Copy the project files into the container
COPY ./pyproject.toml ./poetry.lock* /app/

# Install dependencies
RUN poetry config virtualenvs.create false && poetry install --no-root --no-interaction --no-ansi

# Expose the port FastAPI will run on
EXPOSE 8000

ENV PYTHONPATH=/app

# Copy the remaining project files
COPY ./ /app/

# New lib
RUN poetry config virtualenvs.create false && poetry install --no-root --no-interaction --no-ansi

# Remove initial_fixture_data.py (only for local dev env)
RUN rm -rf /app/initial_fixture_data.py

# Command to run the FastAPI app with Uvicorn
CMD ["/app/scripts/docker-entrypoint.sh"]
