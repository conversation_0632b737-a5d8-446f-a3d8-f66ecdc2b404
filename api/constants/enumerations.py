from enum import Enum
import re

class CategorieAffranchissementEnum(Enum):
    """
    Categorie d'affranchissement
    """
    CODE = "CODE"
    MARI = "MARI"
    BEAU = "BEAU"
    MAFF = "MAFF"
    T = "T"
    VIGN = "VIGN"
    PAP = "PAP"

class TypeAffranchissementEnum(str, Enum):
    """
    Type d'affranchissement
    """
    SD = "SD"
    S10 = "S10"
    TNUM = "TNUM"
    LV20G = "LV20G"
    LP20G = "LP20G"
    INTMON20G = "INTMON20G"
    LSP = "LSP"
    ECO20G = "ECO20G"
    EUR20G = "EUR20G"
    VAL = "VAL"
    DEWD1 = "DEWD1"
    INTER20G = "INTER20G"
    GUIC = "GUIC"
    AUTO = "AUTO"
    T = "T"
    NON_DETERMINE = "NON_DETERMINE"
    PAP_S_500 = "PAP_S_500"
    PAP_XS_250 = "PAP_XS_250"
    
class StatutEnveloppeEnum(str, Enum):
    EDITION = "EDITION"
    VALORISE = "VALORISE"
    PAUSE   = "PAUSE"
    FRAUDULEUSE = "FRAUDULEUSE"
    SOUS_AFFRANCHI = "SOUS_AFFRANCHI"
    TERMINEE = "TERMINEE"

class StatutVerificationEnum(str, Enum):
    VALIDE = "VALIDE"
    INVALIDE = "INVALIDE"
    NON_DETERMINE = "NON_DETERMINE"

class ValiditeAffranchissementEnum(str, Enum):
    VALIDE = "VALIDE"
    INVALIDE = "INVALIDE"

class TypeVerificationAffranchissementEnum(str, Enum):
    COUNT = "COUNT"
    WISSOU = "WISSOU"
    CODE_FRAUDULEUX = "CODE_FRAUDULEUX"
    TIMBRE = "TIMBRE"
    TARIF = "TARIF"
    SSU_TRACKING = "SSU_TRACKING"
    SSU_PURCHASE = "SSU_PURCHASE"
    GRAMMAIRE = "GRAMMAIRE"
    GRAMMAIRE_ORIGIN = "GRAMMAIRE_ORIGIN"
    GRAMMAIRE_CLE = "GRAMMAIRE_CLE"
    GRAMMAIRE_SERVICE = "GRAMMAIRE_SERVICE"
    GRAMMAIRE_COUNTRY = "GRAMMAIRE_COUNTRY"
    GRAMMAIRE_SIGNATURE = "GRAMMAIRE_SIGNATURE"
    SEQUENCE = "SEQUENCE" # Le code existe t-il dans une RegleMetier de type SEQUENCE ?
    AUTRE = "AUTRE" 

class ProduitEnum(Enum):
    LV = "LV"
    LINT = "LINT"
    PPI = "PPI"
    LSP = "LSP"
    NON_DETERMINE = "NON_DETERMINE"

class DeviseEnum(Enum):
    EURO = "EURO"
    FRANCS = "FRANCS"
    ANCIEN_FRANCS = "ANCIEN_FRANCS"
    

class NatureAffranchissementEnum(Enum):
    LV_20 = "LV_20"
    LV_100 = "LV_100"
    LV_250 = "LV_250"
    LV_500 = "LV_500"
    LV_1000 = "LV_1000"
    LV_2000 = "LV_2000"
    LSP_20 = "LSP_20"
    LSP_100 = "LSP_100"
    LSP_250 = "LSP_250"
    LSP_500 = "LSP_500"
    LSP_1000 = "LSP_1000"
    LSP_2000 = "LSP_2000"
    LINT_20 = "LINT_20"
    LINT_50 = "LINT_50"
    LINT_100 = "LINT_100"
    LINT_250 = "LINT_250"
    LINT_500 = "LINT_500"
    LINT_2000 = "LINT_2000"
    PPI_50 = "PPI_50"
    PPI_100 = "PPI_100"
    PPI_250 = "PPI_250"
    PPI_500 = "PPI_500"
    PPI_1000 = "PPI_1000"
    PPI_2000 = "PPI_2000"
    LV_20_S = "LV_20_S"
    LV_100_S = "LV_100_S"
    LV_250_S = "LV_250_S"
    LV_500_S = "LV_500_S"
    LV_1000_S = "LV_1000_S"
    LV_2000_S = "LV_2000_S"
    LSP_20_S = "LSP_20_S"
    LSP_100_S = "LSP_100_S"
    LSP_250_S = "LSP_250_S"
    LSP_500_S = "LSP_500_S"
    LSP_1000_S = "LSP_1000_S"
    LSP_2000_S = "LSP_2000_S"
    LINT_20_S = "LINT_20_S"
    LINT_50_S = "LINT_50_S"
    LINT_100_S = "LINT_100_S"
    LINT_250_S = "LINT_250_S"
    LINT_500_S = "LINT_500_S"
    LINT_2000_S = "LINT_2000_S"
    PPI_50_S = "PPI_50_S"
    PPI_100_S = "PPI_100_S"
    PPI_250_S = "PPI_250_S"
    PPI_500_S = "PPI_500_S"
    PPI_1000_S = "PPI_1000_S"
    PPI_2000_S = "PPI_2000_S"
    NON_DETERMINE = "NON_DETERMINE"
    
class SousTypeAffranchissementEnum(Enum):
    SDN = "SDN"
    SDX = "SDX"
    NON_DETERMINE = "NON_DETERMINE"

class TypeRegleMetierEnum(Enum):
    SEQUENCE = "SEQUENCE"
    VALEUR = "VALEUR"

class FrontendActionEnum(Enum):
    COMPLETER = "COMPLETER"
    RIEN = "RIEN"


class StatutLotExpediteurEnum(str, Enum):
    OUVERT = "OUVERT"
    FERME = "FERME"
    NOTIFIE = "NOTIFIE"
    PAIEMENT_PARTIEL = "PAIEMENT_PARTIEL"
    PAIEMENT_TOTAL_RECU = "PAIEMENT_TOTAL_RECU"
    TRAITE_LIBERABLE = "TRAITE_LIBERABLE"
    TRAITE_LIBOURNE = "TRAITE_LIBOURNE"
    TRAITE_PPDC = "TRAITE_PPDC"
    REMB_PARTIEL = "REMB_PARTIEL"
    TRAITE_OPERATIONNEL = "TRAITE_OPERATIONNEL"
    ARRIVEE_LIBOURNE = "ARRIVEE_LIBOURNE"
    VALORISER_LIBOURNE = "VALORISER_LIBOURNE"
    CONTENTIEUX = "CONTENTIEUX"

class OptionTraitementLotExpediteurEnum(str, Enum):
    LIVRAISON_DESTINATAIRES = "LIVRAISON_DESTINATAIRES"  # Cas A
    RECUPERATION_SUR_SITE = "RECUPERATION_SUR_SITE"      # Cas B
    RENVOI_EXPEDITEUR = "RENVOI_EXPEDITEUR"              # Cas C

class SourceEnveloppeEnum(str, Enum):
    MANUEL = "MANUEL"
    TRAITEMENT = "TRAITEMENT"
    FACTEO = "FACTEO"
    IMPORT = "IMPORT"

class StatutCasierEnum(str, Enum):
    DISPONIBLE = "DISPONIBLE"
    OCCUPE = "OCCUPE"

class DestinationEnveloppeEnum(str, Enum):
    METROPOLE = "METROPOLE"
    DOM_COM = "DOM_COM"
    INTERNATIONAL = "INTERNATIONAL"

class StatutPaiementEnum(str, Enum):
    NON_INITIALISE = "NON_INITIALISE"
    EN_ATTENTE = "EN_ATTENTE"
    AUTORISE = "AUTORISE"
    PAYE = "PAYE"
    ECHEC = "ECHEC"
    ANNULE = "ANNULE"
    REMBOURSE = "REMBOURSE"
