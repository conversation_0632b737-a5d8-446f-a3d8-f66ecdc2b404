
from enum import Enum
from typing import Dict, TypedDict, Any
from constants.enumerations import NatureAffranchissementEnum, ProduitEnum
from sqlmodel import SQLModel
from pydantic import computed_field, Field
from constants.variables import CONSTANTES_PRIX_AFFRANCHISSEMENT
from services.produits import ServiceProduits

class NatureAffranchissementType(SQLModel):
    id: NatureAffranchissementEnum
    lib: str
    nom_produit: ProduitEnum = ProduitEnum.NON_DETERMINE
    poids: int = 0
    suivi: bool = False
    produit: Any = Field(None, exclude=True)

    class Config:
        from_attributes = True  # Pour permettre la sérialisation des attributs
        populate_by_name = True
    
    def __init__(self, id: NatureAffranchissementEnum, lib: str):        
        from constants.enumerations import ProduitEnum
        
        super().__init__(id=id, lib=lib)
        
        from services.produits import ServiceProduits

        try:
            split = (str(self.id).split(".")[-1].split("_")) #LV_20_S -> LV, 20, S
            self.nom_produit = ProduitEnum(split[0])
            self.poids = int(split[1])

            if len(split) == 3:
                self.suivi = split[2].upper().strip() == "S"
            else:
                self.suivi = False
            
            self.produit = ServiceProduits.recherche_produit_proche(produit=self.nom_produit, poids=self.poids)
        except:
            pass
    
    @computed_field
    @property
    def prix_unite(self) -> float | None:
        """
        Retourne le prix de l'affranchissement en fonction du produit, du poids et de sa nature.
        Le prix est calculé en combinant le prix de base MTEL avec les options de suivi si applicables.
        
        Returns:
            float | None: Le prix unitaire calculé ou None si le prix ne peut pas être déterminé
        """
        
        if self.produit is None:
            return None
        
        # Prix de base MTEL
        price_mtel = self.produit.courrier_mtel
        
        # Si option de suivi ("S")
        if self.suivi:
            if self.produit.produit in ["LINT", "PPI"]:
                price_mtel += CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10
            else:
                price_mtel += CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_sd88
                
        return price_mtel

    

# Configuration des natures
NATURES_AFFRANCHISSEMENT: Dict[NatureAffranchissementEnum, NatureAffranchissementType] = {
    NatureAffranchissementEnum.LV_20: NatureAffranchissementType(NatureAffranchissementEnum.LV_20, "L.Verte Max 20g"),
    NatureAffranchissementEnum.LV_100: NatureAffranchissementType(NatureAffranchissementEnum.LV_100, "L.Verte Max 100g"),
    NatureAffranchissementEnum.LV_250: NatureAffranchissementType(NatureAffranchissementEnum.LV_250, "L.Verte Max 250g"),
    NatureAffranchissementEnum.LV_500: NatureAffranchissementType(NatureAffranchissementEnum.LV_500, "L.Verte Max 500g"),
    NatureAffranchissementEnum.LV_1000: NatureAffranchissementType(NatureAffranchissementEnum.LV_1000, "L.Verte Max 1000g"),
    NatureAffranchissementEnum.LV_2000: NatureAffranchissementType(NatureAffranchissementEnum.LV_2000, "L.Verte Max 2000g"),
    NatureAffranchissementEnum.LSP_20: NatureAffranchissementType(NatureAffranchissementEnum.LSP_20, "L.S.Plus Max 20g"),
    NatureAffranchissementEnum.LSP_100: NatureAffranchissementType(NatureAffranchissementEnum.LSP_100, "L.S.Plus Max 100g"),
    NatureAffranchissementEnum.LSP_250: NatureAffranchissementType(NatureAffranchissementEnum.LSP_250, "L.S.Plus Max 250g"),
    NatureAffranchissementEnum.LSP_500: NatureAffranchissementType(NatureAffranchissementEnum.LSP_500, "L.S.Plus Max 500g"),
    NatureAffranchissementEnum.LSP_1000: NatureAffranchissementType(NatureAffranchissementEnum.LSP_1000, "L.S.Plus Max 1000g"),
    NatureAffranchissementEnum.LSP_2000: NatureAffranchissementType(NatureAffranchissementEnum.LSP_2000, "L.S.Plus Max 2000g"),
    NatureAffranchissementEnum.LINT_20: NatureAffranchissementType(NatureAffranchissementEnum.LINT_20, "L.Inter Max 20g"),
    NatureAffranchissementEnum.LINT_50: NatureAffranchissementType(NatureAffranchissementEnum.LINT_50, "L.Inter Max 50g"),
    NatureAffranchissementEnum.LINT_100: NatureAffranchissementType(NatureAffranchissementEnum.LINT_100, "L.Inter Max 100g"),
    NatureAffranchissementEnum.LINT_250: NatureAffranchissementType(NatureAffranchissementEnum.LINT_250, "L.Inter Max 250g"),
    NatureAffranchissementEnum.LINT_500: NatureAffranchissementType(NatureAffranchissementEnum.LINT_500, "L.Inter Max 500g"),
    NatureAffranchissementEnum.LINT_2000: NatureAffranchissementType(NatureAffranchissementEnum.LINT_2000, "L.Inter Max 2000g"),
    NatureAffranchissementEnum.PPI_50: NatureAffranchissementType(NatureAffranchissementEnum.PPI_50, "P.P.Inter Max 50g"),
    NatureAffranchissementEnum.PPI_100: NatureAffranchissementType(NatureAffranchissementEnum.PPI_100, "P.P.Inter Max 100g"),
    NatureAffranchissementEnum.PPI_250: NatureAffranchissementType(NatureAffranchissementEnum.PPI_250, "P.P.Inter Max 250g"),
    NatureAffranchissementEnum.PPI_500: NatureAffranchissementType(NatureAffranchissementEnum.PPI_500, "P.P.Inter Max 500g"),
    NatureAffranchissementEnum.PPI_1000: NatureAffranchissementType(NatureAffranchissementEnum.PPI_1000, "P.P.Inter Max 1000g"),
    NatureAffranchissementEnum.PPI_2000: NatureAffranchissementType(NatureAffranchissementEnum.PPI_2000, "P.P.Inter Max 2000g"),
    NatureAffranchissementEnum.LV_20_S: NatureAffranchissementType(NatureAffranchissementEnum.LV_20_S, "L.Verte Max 20g + Suivi"),
    NatureAffranchissementEnum.LV_100_S: NatureAffranchissementType(NatureAffranchissementEnum.LV_100_S, "L.Verte Max 100g + Suivi"),
    NatureAffranchissementEnum.LV_250_S: NatureAffranchissementType(NatureAffranchissementEnum.LV_250_S, "L.Verte Max 250g + Suivi"),
    NatureAffranchissementEnum.LV_500_S: NatureAffranchissementType(NatureAffranchissementEnum.LV_500_S, "L.Verte Max 500g + Suivi"),
    NatureAffranchissementEnum.LV_1000_S: NatureAffranchissementType(NatureAffranchissementEnum.LV_1000_S, "L.Verte Max 1000g + Suivi"),
    NatureAffranchissementEnum.LV_2000_S: NatureAffranchissementType(NatureAffranchissementEnum.LV_2000_S, "L.Verte Max 2000g + Suivi"),
    NatureAffranchissementEnum.LSP_20_S: NatureAffranchissementType(NatureAffranchissementEnum.LSP_20_S, "L.S.Plus Max 20g + Suivi"),
    NatureAffranchissementEnum.LSP_100_S: NatureAffranchissementType(NatureAffranchissementEnum.LSP_100_S, "L.S.Plus Max 100g + Suivi"),
    NatureAffranchissementEnum.LSP_250_S: NatureAffranchissementType(NatureAffranchissementEnum.LSP_250_S, "L.S.Plus Max 250g + Suivi"),
    NatureAffranchissementEnum.LSP_500_S: NatureAffranchissementType(NatureAffranchissementEnum.LSP_500_S, "L.S.Plus Max 500g + Suivi"),
    NatureAffranchissementEnum.LSP_1000_S: NatureAffranchissementType(NatureAffranchissementEnum.LSP_1000_S, "L.S.Plus Max 1000g + Suivi"),
    NatureAffranchissementEnum.LSP_2000_S: NatureAffranchissementType(NatureAffranchissementEnum.LSP_2000_S, "L.S.Plus Max 2000g + Suivi"),
    NatureAffranchissementEnum.LINT_20_S: NatureAffranchissementType(NatureAffranchissementEnum.LINT_20_S, "L.Inter Max 20g + Suivi"),
    NatureAffranchissementEnum.LINT_50_S: NatureAffranchissementType(NatureAffranchissementEnum.LINT_50_S, "L.Inter Max 50g + Suivi"),
    NatureAffranchissementEnum.LINT_100_S: NatureAffranchissementType(NatureAffranchissementEnum.LINT_100_S, "L.Inter Max 100g + Suivi"),
    NatureAffranchissementEnum.LINT_250_S: NatureAffranchissementType(NatureAffranchissementEnum.LINT_250_S, "L.Inter Max 250g + Suivi"),
    NatureAffranchissementEnum.LINT_500_S: NatureAffranchissementType(NatureAffranchissementEnum.LINT_500_S, "L.Inter Max 500g + Suivi"),
    NatureAffranchissementEnum.LINT_2000_S: NatureAffranchissementType(NatureAffranchissementEnum.LINT_2000_S, "L.Inter Max 2000g + Suivi"),
    NatureAffranchissementEnum.PPI_50_S: NatureAffranchissementType(NatureAffranchissementEnum.PPI_50_S, "P.P.Inter Max 50g + Suivi"),
    NatureAffranchissementEnum.PPI_100_S: NatureAffranchissementType(NatureAffranchissementEnum.PPI_100_S, "P.P.Inter Max 100g + Suivi"),
    NatureAffranchissementEnum.PPI_250_S: NatureAffranchissementType(NatureAffranchissementEnum.PPI_250_S, "P.P.Inter Max 250g + Suivi"),
    NatureAffranchissementEnum.PPI_500_S: NatureAffranchissementType(NatureAffranchissementEnum.PPI_500_S, "P.P.Inter Max 500g + Suivi"),
    NatureAffranchissementEnum.PPI_1000_S: NatureAffranchissementType(NatureAffranchissementEnum.PPI_1000_S, "P.P.Inter Max 1000g + Suivi"),
    NatureAffranchissementEnum.PPI_2000_S: NatureAffranchissementType(NatureAffranchissementEnum.PPI_2000_S, "P.P.Inter Max 2000g + Suivi"),
    NatureAffranchissementEnum.NON_DETERMINE: NatureAffranchissementType(NatureAffranchissementEnum.NON_DETERMINE, "Indéterminé / Autre"),
}
