from enum import Enum
from typing import List, Optional, Dict
from core.utils.prix import convertir_prix_devise_en_euros
from constants.enumerations import CategorieAffranchissementEnum, TypeAffranchissementEnum, DeviseEnum
from constants.variables import CONSTANTES_PRIX_AFFRANCHISSEMENT
from sqlmodel import SQLModel
from models.public import ModeleAffranchissement, ValeurModeleAffranchissement, CategorieAffranchissement


# Configuration des catégories et de l'ensemble des Affranchissements possibles
CATEGORIES_AFFRANCHISSEMENTS = {
    CategorieAffranchissementEnum.CODE: CategorieAffranchissement(
        id=CategorieAffranchissementEnum.CODE,
        display1="CODE",
        display2="...",
        description="Code",
        color="xtra_envelop_element_code",
        variant="outlined",
        types_affranchissements=[
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.CODE,
                type=TypeAffranchissementEnum.SD,
                origine="86",
                color="xtra_envelop_element_code",
                variant="outlined",
                devise=DeviseEnum.EURO,
                prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_sd86,
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.CODE,
                type=TypeAffranchissementEnum.SD,
                origine="87",
                color="xtra_envelop_element_code",
                variant="outlined",
                devise=DeviseEnum.EURO,
                prix_unite_devise=None,
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.CODE,
                type=TypeAffranchissementEnum.SD,
                origine="88",
                color="xtra_envelop_element_code",
                variant="outlined",
                devise=DeviseEnum.EURO,
                prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_sd88,
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.CODE,
                type=TypeAffranchissementEnum.SD,
                color="xtra_envelop_element_code",
                variant="outlined",
                devise=DeviseEnum.EURO,
                prix_unite_devise=None,
                origine="MTEL"
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.CODE,
                type=TypeAffranchissementEnum.SD,
                color="xtra_envelop_element_code",
                variant="outlined",
                devise=DeviseEnum.EURO,
                prix_unite_devise=None,
                origine="IND"
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.CODE,
                type=TypeAffranchissementEnum.S10,
                color="xtra_envelop_element_code",
                variant="outlined",
                devise=DeviseEnum.EURO,
                valorisation_complexe=True,
                afficher=False
                # prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_s10,
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.CODE,
                type=TypeAffranchissementEnum.TNUM,
                color="xtra_envelop_element_code",
                variant="outlined",
                devise=DeviseEnum.EURO,
                prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_code_tnum,
            ),
        ]
    ),
    CategorieAffranchissementEnum.MARI: CategorieAffranchissement(
        id=CategorieAffranchissementEnum.MARI,
        display1="MARI",
        display2="...",
        description="Timbre Marianne",
        color="xtra_envelop_element_mari",
        variant="outlined",
        types_affranchissements=[
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.MARI,
                type=TypeAffranchissementEnum.LV20G,
                color="xtra_envelop_element_verte",
                variant="contained",
                devise=DeviseEnum.EURO,
                prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_lv20g_tp,
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.MARI,
                type=TypeAffranchissementEnum.LP20G,
                color="xtra_envelop_element_rouge",
                variant="contained",
                devise=DeviseEnum.EURO,
                prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_lp20g_tp,
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.MARI,
                type=TypeAffranchissementEnum.INTMON20G,
                color="xtra_envelop_element_inter",
                variant="contained",
                devise=DeviseEnum.EURO,
                prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_lint20g_tp,
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.MARI,
                type=TypeAffranchissementEnum.LSP,
                color="xtra_envelop_element_plus",
                variant="contained",
                devise=DeviseEnum.EURO,
                prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_lsp20g_tp,
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.MARI,
                type=TypeAffranchissementEnum.ECO20G,
                color="xtra_envelop_element_ecopli",
                variant="contained",
                devise=DeviseEnum.EURO,
                prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_eco20g_tp,
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.MARI,
                type=TypeAffranchissementEnum.EUR20G,
                color="xtra_envelop_element_europe",
                variant="contained",
                devise=DeviseEnum.EURO,
                prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_lint20g_tp,
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.MARI,
                type=TypeAffranchissementEnum.VAL,
                color="xtra_envelop_element_mari",
                variant="contained",
                valeurs=[
                    ValeurModeleAffranchissement(devise=DeviseEnum.EURO),
                    ValeurModeleAffranchissement(devise=DeviseEnum.EURO, prix_unite_devise=0.05, color="xtra_envelop_element_5centimes"),
                    ValeurModeleAffranchissement(devise=DeviseEnum.EURO, prix_unite_devise=1, color="xtra_envelop_element_1euro"),
                    ValeurModeleAffranchissement(devise=DeviseEnum.FRANCS, variant="outlined"),
                    ValeurModeleAffranchissement(devise=DeviseEnum.ANCIEN_FRANCS, variant="outlined")
                ]
            )
        ]
    ),
    CategorieAffranchissementEnum.BEAU: CategorieAffranchissement(
        id=CategorieAffranchissementEnum.BEAU,
        display1="BEAU",
        display2="...",
        description="Beau Timbre",
        color="xtra_envelop_element_beau",
        variant="outlined",
        types_affranchissements=[
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.BEAU,
                type=TypeAffranchissementEnum.DEWD1,
                color="xtra_envelop_element_beau",
                variant="outlined",
                devise=DeviseEnum.EURO,
                prix_unite_devise=5.26,
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.BEAU,
                type=TypeAffranchissementEnum.LV20G,
                color="xtra_envelop_element_beau",
                variant="outlined",
                devise=DeviseEnum.EURO,
                prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_lv20g_tp,
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.BEAU,
                type=TypeAffranchissementEnum.LP20G,
                color="xtra_envelop_element_beau",
                variant="outlined",
                devise=DeviseEnum.EURO,
                prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_lp20g_tp,
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.BEAU,
                type=TypeAffranchissementEnum.INTER20G,
                color="xtra_envelop_element_beau",
                variant="outlined",
                devise=DeviseEnum.EURO,
                prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_lint20g_tp,
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.BEAU,
                type=TypeAffranchissementEnum.VAL,
                color="xtra_envelop_element_beau",
                variant="outlined",
                valeurs=[
                    ValeurModeleAffranchissement(devise=DeviseEnum.EURO),
                    ValeurModeleAffranchissement(devise=DeviseEnum.EURO, prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_lv20g_tp),
                    ValeurModeleAffranchissement(devise=DeviseEnum.EURO, prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_lint20g_tp),
                    ValeurModeleAffranchissement(devise=DeviseEnum.EURO, prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_lv100g_tp),
                    ValeurModeleAffranchissement(devise=DeviseEnum.FRANCS),
                    ValeurModeleAffranchissement(devise=DeviseEnum.ANCIEN_FRANCS),
                ]
            )
        ]
    ),
    CategorieAffranchissementEnum.MAFF: CategorieAffranchissement(
        id=CategorieAffranchissementEnum.MAFF,
        display1="MAFF",
        display2="...",
        description="Machine à Affranchir",
        color="xtra_envelop_element_maff",
        variant="outlined",
        types_affranchissements=[
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.MAFF,
                type=TypeAffranchissementEnum.VAL,
                color="xtra_envelop_element_maff",
                variant="outlined",
                valeurs=[
                    ValeurModeleAffranchissement(devise=DeviseEnum.EURO),
                    ValeurModeleAffranchissement(devise=DeviseEnum.EURO, prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_lv20g_tp),
                    ValeurModeleAffranchissement(devise=DeviseEnum.EURO, prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_lint20g_tp),
                    ValeurModeleAffranchissement(devise=DeviseEnum.EURO, prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_lv100g_tp),
                    ValeurModeleAffranchissement(devise=DeviseEnum.FRANCS),
                    ValeurModeleAffranchissement(devise=DeviseEnum.ANCIEN_FRANCS),
                ]
            )
        ]
    ),
    CategorieAffranchissementEnum.T: CategorieAffranchissement(
        id=CategorieAffranchissementEnum.T,
        display1="T",
        display2="...",
        description="Enveloppe T",
        color="xtra_envelop_element_t",
        variant="outlined",
        types_affranchissements=[
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.T,
                type=TypeAffranchissementEnum.T,
                color="xtra_envelop_element_t",
                variant="outlined",
                devise=DeviseEnum.EURO,
                prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_lv20g_tp,
            ),
        ]
    ),
    CategorieAffranchissementEnum.VIGN: CategorieAffranchissement(
        id=CategorieAffranchissementEnum.VIGN,
        display1="VIGN",
        display2="...",
        description="Vignette",
        color="xtra_envelop_element_vign",
        variant="outlined",
        types_affranchissements=[
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.VIGN,
                type=TypeAffranchissementEnum.GUIC,
                color="xtra_envelop_element_vign",
                variant="outlined",
                devise=DeviseEnum.EURO,
                prix_unite_devise=None,
                valeurs=[
                    ValeurModeleAffranchissement(devise=DeviseEnum.EURO)
                ]
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.VIGN,
                type=TypeAffranchissementEnum.AUTO,
                color="xtra_envelop_element_vign",
                variant="outlined",
                devise=DeviseEnum.EURO,
                prix_unite_devise=None,
                valeurs=[
                    ValeurModeleAffranchissement(devise=DeviseEnum.EURO)
                ]
            ),
        ]
    ),
    CategorieAffranchissementEnum.PAP: CategorieAffranchissement(
        id=CategorieAffranchissementEnum.PAP,
        display1="VIGN",
        display2="...",
        description="Produit Pret a Poster",
        color="xtra_envelop_element_vign",
        variant="outlined",
        types_affranchissements=[
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.PAP,
                type=TypeAffranchissementEnum.PAP_S_500,
                color="xtra_envelop_element_vign",
                variant="outlined",
                devise=DeviseEnum.EURO,
                prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_pap_s_500,
            ),
            ModeleAffranchissement(
                categorie=CategorieAffranchissementEnum.PAP,
                type=TypeAffranchissementEnum.PAP_XS_250,
                color="xtra_envelop_element_vign",
                variant="outlined",
                devise=DeviseEnum.EURO,
                prix_unite_devise=CONSTANTES_PRIX_AFFRANCHISSEMENT.price_pap_xs_250
            ),
        ]
    ),
}

