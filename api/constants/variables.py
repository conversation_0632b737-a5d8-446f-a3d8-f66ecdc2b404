from typing import TypedDict
from dataclasses import dataclass


@dataclass
class ConstantesValorisationType:
    coeff_taxe_livraison_si_fraude: float
    taxe_livraison_fixe_si_fraude: float 
    frais_collecte_ht: float
    coeff_collecte_tva: float

@dataclass
class ConstantesPrixAffranchissementType:
    price_lp20g_tp: float
    price_eco20g_tp: float
    price_lv20g_tp: float
    price_lint20g_tp: float
    price_lv100g_tp: float
    price_lsp20g_tp: float
    price_code_sd86: float  # Tarif LV20g MTEL
    price_code_tnum: float  # Tarif TNUM20g 
    price_code_sd88: float  # Prix fixe pour le moment ?
    price_code_s10: float   # Prix fixe pour le moment ?
    price_pap_s_500: float
    price_pap_xs_250: float
    

CONSTANTES_VALORISATION = ConstantesValorisationType(
    coeff_taxe_livraison_si_fraude=0.1,
    taxe_livraison_fixe_si_fraude=3.0,
    frais_collecte_ht=4.68,
    coeff_collecte_tva=0.2,
)

CONSTANTES_PRIX_AFFRANCHISSEMENT = ConstantesPrixAffranchissementType(
    # Prix en principe figé car ce tarif n'existe plus
    price_lp20g_tp=1.43,
    price_eco20g_tp=1.14,
    # Prix TP présents dans les catégories+shortcuts (revalorisés chaque année)
    price_lv20g_tp=1.29,
    price_lint20g_tp=1.96,
    price_lv100g_tp=2.58,
    price_lsp20g_tp=2.99,
    # Prix des codes si pas trouvés dans le SI (revalorisés chaque année)
    price_code_sd86=1.26,  # Tarif LV20g MTEL
    price_code_tnum=1.29,  # Tarif TNUM20g
    price_code_sd88=0.5,   # Prix fixe pour le moment ?
    price_code_s10=2.8,    # Prix fixe pour le moment ?

    # PAP
    price_pap_s_500=20.10,
    price_pap_xs_250=15.55
)

CONSTANTES_CODES_PRODUITS = {
    "LV_20G": "1123820"
}