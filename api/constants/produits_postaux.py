from enum import Enum

# https://www.laposte.fr/tarifs-postaux-courrier-lettres-timbres#ancre04

class ProduitsPostauxEnum(Enum):
    LETTRE_VERTE = "LETTRE_VERTE"
    LETTRE_VERTE_SUIVIE = "LETTRE_VERTE_SUIVIE"
    LETTRE_RECOMMANDEE = "LETTRE_RECOMMANDEE"
    LETTRE_SERVICES_PLUS = "LETTRE_SERVICES_PLUS"
    E_LETTRE_ROUGE = "E_LETTRE_ROUGE"

class OptionProduitPostalDestinationEnum(Enum):
    FRANCE = "FRANCE"
    INTERNATIONAL = "INTERNATIONAL"

class NiveauRecommandationEnum(Enum):
    R1 = "R1"
    R2 = "R2"
    R3 = "R3"

class GammeProduitPostal:
    def __init__(self, gamme, produits):
        self.gamme = gamme
        self.produits = produits

class OptionProduitPostal:
    def __init__(self, prix : float, 
                 destination : OptionProduitPostalDestinationEnum = OptionProduitPostalDestinationEnum.FRANCE,
                 niveau_recommandation : NiveauRecommandationEnum = None,
                 donnees = {}):
        self._prix = prix
        self.destination = destination
        self.niveau_recommandation = niveau_recommandation
        self.donnees = donnees

    def prix(self):
        prix = self. _prix

        if self.niveau_recommandation is not None:
            if self.destination == OptionProduitPostalDestinationEnum.FRANCE:
                prix += 1.40
            else:
                prix += 1.50

        return prix

class ProduitPostal:
    def __init__(self, poids_max, options : list[OptionProduitPostal]):
        self.poids_max = poids_max
        self.options = options


class GammesProduitsPostaux:
    GAMMES = [
        GammeProduitPostal(
            gamme=ProduitsPostauxEnum.LETTRE_VERTE_SUIVIE,
            produits=[
                ProduitPostal(20, [
                    OptionProduitPostal(1.89, OptionProduitPostalDestinationEnum.FRANCE),
                    OptionProduitPostal(4.90, OptionProduitPostalDestinationEnum.INTERNATIONAL)
                ]),
                ProduitPostal(100, [
                    OptionProduitPostal(3.28, OptionProduitPostalDestinationEnum.FRANCE),
                    OptionProduitPostal(7.30, OptionProduitPostalDestinationEnum.INTERNATIONAL)
                ]),
                ProduitPostal(250, [
                    OptionProduitPostal(5.22, OptionProduitPostalDestinationEnum.FRANCE),
                    OptionProduitPostal(13.60, OptionProduitPostalDestinationEnum.INTERNATIONAL)
                ]),
                ProduitPostal(500, [
                    OptionProduitPostal(7.20, OptionProduitPostalDestinationEnum.FRANCE),
                    OptionProduitPostal(18.30, OptionProduitPostalDestinationEnum.INTERNATIONAL)
                ]),
                ProduitPostal(1000, [
                    OptionProduitPostal(8.90, OptionProduitPostalDestinationEnum.FRANCE),
                    OptionProduitPostal(32.30, OptionProduitPostalDestinationEnum.INTERNATIONAL)
                ]),
                ProduitPostal(2000, [
                    OptionProduitPostal(10.75, OptionProduitPostalDestinationEnum.FRANCE),
                    OptionProduitPostal(32.30, OptionProduitPostalDestinationEnum.INTERNATIONAL)
                ])
            ]
        ),
        GammeProduitPostal(
            gamme=ProduitsPostauxEnum.LETTRE_RECOMMANDEE,
            produits=[
                ProduitPostal(20, [
                    OptionProduitPostal(5.74, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R1),
                    OptionProduitPostal(6.85, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R2),
                    OptionProduitPostal(8.43, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R3),
                    OptionProduitPostal(7.05, OptionProduitPostalDestinationEnum.INTERNATIONAL, NiveauRecommandationEnum.R1),
                    OptionProduitPostal(8.05, OptionProduitPostalDestinationEnum.INTERNATIONAL, NiveauRecommandationEnum.R2),
                ]),
                ProduitPostal(50, [
                    OptionProduitPostal(6.56, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R1),
                    OptionProduitPostal(7.58, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R2),
                    OptionProduitPostal(9.08, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R3),
                    OptionProduitPostal(9.45, OptionProduitPostalDestinationEnum.INTERNATIONAL, NiveauRecommandationEnum.R1),
                    OptionProduitPostal(10.45, OptionProduitPostalDestinationEnum.INTERNATIONAL, NiveauRecommandationEnum.R2),
                ]),
                ProduitPostal(100, [
                    OptionProduitPostal(7.40, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R1),
                    OptionProduitPostal(8.43, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R2),
                    OptionProduitPostal(9.90, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R3),
                    OptionProduitPostal(9.45, OptionProduitPostalDestinationEnum.INTERNATIONAL, NiveauRecommandationEnum.R1),
                    OptionProduitPostal(10.45, OptionProduitPostalDestinationEnum.INTERNATIONAL, NiveauRecommandationEnum.R2),
                ]),
                ProduitPostal(250, [
                    OptionProduitPostal(9.05, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R1),
                    OptionProduitPostal(10.08, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R2),
                    OptionProduitPostal(11.67, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R3),
                    OptionProduitPostal(15.75, OptionProduitPostalDestinationEnum.INTERNATIONAL, NiveauRecommandationEnum.R1),
                    OptionProduitPostal(16.75, OptionProduitPostalDestinationEnum.INTERNATIONAL, NiveauRecommandationEnum.R2),
                ]),
                ProduitPostal(500, [
                    OptionProduitPostal(10.63, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R1),
                    OptionProduitPostal(11.60, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R2),
                    OptionProduitPostal(13.06, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R3),
                    OptionProduitPostal(20.45, OptionProduitPostalDestinationEnum.INTERNATIONAL, NiveauRecommandationEnum.R1),
                    OptionProduitPostal(21.45, OptionProduitPostalDestinationEnum.INTERNATIONAL, NiveauRecommandationEnum.R2),
                ]),
                ProduitPostal(1000, [
                    OptionProduitPostal(12.23, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R1),
                    OptionProduitPostal(13.25, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R2),
                    OptionProduitPostal(14.70, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R3),
                    OptionProduitPostal(34.45, OptionProduitPostalDestinationEnum.INTERNATIONAL, NiveauRecommandationEnum.R1),
                    OptionProduitPostal(35.45, OptionProduitPostalDestinationEnum.INTERNATIONAL, NiveauRecommandationEnum.R2),
                ]),
                ProduitPostal(2000, [
                    OptionProduitPostal(14.48, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R1),
                    OptionProduitPostal(15.48, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R2),
                    OptionProduitPostal(17.08, OptionProduitPostalDestinationEnum.FRANCE, NiveauRecommandationEnum.R3),
                    OptionProduitPostal(34.45, OptionProduitPostalDestinationEnum.INTERNATIONAL, NiveauRecommandationEnum.R1),
                    OptionProduitPostal(35.45, OptionProduitPostalDestinationEnum.INTERNATIONAL, NiveauRecommandationEnum.R2),
                ]),
            ]
        ),
        GammeProduitPostal(
            gamme=ProduitsPostauxEnum.LETTRE_VERTE,
            produits=[
                ProduitPostal(20,   [OptionProduitPostal(1.39, OptionProduitPostalDestinationEnum.FRANCE)]),
                ProduitPostal(100,  [OptionProduitPostal(2.78, OptionProduitPostalDestinationEnum.FRANCE)]),
                ProduitPostal(250,  [OptionProduitPostal(4.72, OptionProduitPostalDestinationEnum.FRANCE)]),
                ProduitPostal(500,  [OptionProduitPostal(6.70, OptionProduitPostalDestinationEnum.FRANCE)]),
                ProduitPostal(1000, [OptionProduitPostal(8.40, OptionProduitPostalDestinationEnum.FRANCE)]),
                ProduitPostal(2000, [OptionProduitPostal(10.25, OptionProduitPostalDestinationEnum.FRANCE)]),
            ]
        ),
        GammeProduitPostal(
            gamme=ProduitsPostauxEnum.LETTRE_SERVICES_PLUS,
            produits=[
                ProduitPostal(20,   [OptionProduitPostal(3.15, OptionProduitPostalDestinationEnum.FRANCE)]),
                ProduitPostal(100,  [OptionProduitPostal(4.15, OptionProduitPostalDestinationEnum.FRANCE)]),
                ProduitPostal(250,  [OptionProduitPostal(5.25, OptionProduitPostalDestinationEnum.FRANCE)]),
                ProduitPostal(500,  [OptionProduitPostal(7.35, OptionProduitPostalDestinationEnum.FRANCE)]),
                ProduitPostal(1000, [OptionProduitPostal(9.40, OptionProduitPostalDestinationEnum.FRANCE)]),
                ProduitPostal(2000, [OptionProduitPostal(10.90, OptionProduitPostalDestinationEnum.FRANCE)]),
            ]
        )
    ]