from models.business import RegleMetier
from constants.enumerations import TypeRegleMetierEnum

def ajouter_regle_metier_si_absente(key: str, type: TypeRegleMetierEnum, valeur: dict) -> RegleMetier:
    """
    Ajoute une règle métier si elle n'existe pas déjà (basé sur la clé et le type)
    
    Args:
        key (str): Clé de la règle métier
        type (TypeRegleMetierEnum): Type de la règle métier
        valeur (dict): Valeur de la règle métier
    
    Returns:
        RegleMetier: L'instance de la règle métier (nouvelle ou existante)
    """
    regle_existante = RegleMetier.query.filter_by(key=key, type=type).first()
    if not regle_existante:
        regle_existante = RegleMetier(key=key, type=type, valeur=valeur)
        regle_existante.save()
    return regle_existante


ajouter_regle_metier_si_absente(
    key="S10_LA",
    type=TypeRegleMetierEnum.SEQUENCE_S10,
    valeur={"debut": "01469918", "fin": "01469919", "service": "LL"}
)

