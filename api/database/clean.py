# Clean db
from sqlalchemy import create_engine
from sqlalchemy_utils import create_database, database_exists, drop_database
from core.config import settings
from alembic.config import Config
from alembic import command
import glob
import os
from models import User, UserCreate, Site, Expediteur, Enveloppe
from constants.enumerations import StatutEnveloppeEnum
from datetime import datetime
from core.db import get_session
import crud

# Get database URI
db_uri = str(settings.SQLALCHEMY_DATABASE_URI)
engine = create_engine(db_uri)


confirmation = input(f"Êtes-vous sûr de vouloir supprimer la base de données '{engine.url}'? (y/n): ")
if confirmation.lower() != 'y':
    exit()

# Drop and recreate database
if database_exists(engine.url):
    drop_database(engine.url)
create_database(engine.url)

alembic_cfg = Config("/app/alembic.ini")
alembic_cfg.set_main_option("sqlalchemy.url", db_uri)
command.upgrade(alembic_cfg, "head")