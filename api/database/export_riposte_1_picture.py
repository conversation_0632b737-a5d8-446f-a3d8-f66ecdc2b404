from elasticsearch import Elasticsearch
import json
import base64
import os
from pathlib import Path
from datetime import datetime

# Configuration Elasticsearch
user = "elastic"
password = os.getenv("ELASTIC_PASSWORD")
es = Elasticsearch(f"https://elastic.fraude-server-sta.aks.sandbox.innovation-laposte.io:443",
                   basic_auth=(user, password))

index = "&myapp.xtra_coll_control_picture3"

# Créer le répertoire de destination s'il n'existe pas
output_dir = Path("/data/ripostev1/images")
output_dir.mkdir(parents=True, exist_ok=True)

def extract_and_save_images():
    try:
        # Recherche des 15 premiers documents avec les images
        result = es.search(
            index=index,
            body={
                "query": {"match_all": {}},
                "size": 100
            }
        )
        
        # Afficher le nombre total de documents trouvés
        total_docs = result['hits']['total']['value']
        print(f"Nombre total de documents dans l'index: {total_docs}")
        
        # Parcourir les documents et extraire les images
        for i, hit in enumerate(result['hits']['hits']):
            doc_id = hit['_id']
            print(f"\nTraitement du document {i+1} (ID: {doc_id}):")

            img_data = hit['_source']['picture']['dataUrl']
            
            # Vérifier si le document contient des images
            if img_data:
                
                if ',' in img_data:  # Format data:image/jpeg;base64,/9j/...
                    img_data = img_data.split(',', 1)[1]
                
                # Décoder et sauvegarder l'image
                img_path = f"{output_dir}/{doc_id}.jpg"
                try:
                    with open(img_path, 'wb') as img_file:
                        img_file.write(base64.b64decode(img_data))
                    print(f"  Image sauvegardée: {img_path}")
                except Exception as e:
                    print(f"  Erreur lors de la sauvegarde de l'image: {str(e)}")
            else:
                print("  Aucune image trouvée dans ce document")
        
        return result['hits']['hits']
    
    except Exception as e:
        print(f"Erreur lors de la récupération des documents: {str(e)}")
        return None

# Exécuter la fonction
if __name__ == "__main__":
    extract_and_save_images()
