import os
import json
import tempfile
from datetime import datetime
from elasticsearch import Elasticsearch
from elasticsearch.helpers import scan
from azure.storage.blob import BlobClient, ContentSettings

def export_elasticsearch_to_azure_blob():
    # Paramètres Elasticsearch
    es_host = "https://elastic.fraude-server-sta.aks.sandbox.innovation-laposte.io:443"
    es_user = "elastic"
    es_password = os.getenv("ELASTIC_PASSWORD")
    
    # Paramètres Azure Blob Storage
    container_name = "elasticsearch-exports"

    # Taille des lots
    batch_size = 1000
    
    # Connexion à Elasticsearch
    es = Elasticsearch(es_host, basic_auth=(es_user, es_password), verify_certs=False)
    print(f"Connecté à Elasticsearch sur {es_host}")

    # Récupérer les index existants
    existing_indices = es.indices.get_alias(index="&myapp*")
    print(f"Indices existants: {existing_indices}")
    indices = [v for v in existing_indices.keys()]

    
    # Timestamp pour les noms de fichiers
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Exporter chaque index
    for index_name in indices:
        try:
            # Vérifier si l'index existe
            if not es.indices.exists(index=index_name):
                print(f"L'index '{index_name}' n'existe pas. Passage au suivant.")
                continue
            
            # Nom du blob
            blob_name = f"{index_name.replace('&', '').replace('.', '_')}_{timestamp}.json"
            blob_path = f"{container_name}/{blob_name}"
            
            print(f"Début de l'exportation de l'index '{index_name}'...")
            
            # Utiliser un fichier temporaire
            with tempfile.NamedTemporaryFile(mode='w+', delete=False, encoding='utf-8') as temp_file:
                temp_file_path = temp_file.name
                
                # Compteurs
                total_docs = 0
                current_batch = 0
                
                # Écrire les données
                temp_file.write("[\n")
                
                # Scan des documents
                results = scan(
                    client=es,
                    index=index_name,
                    query={"query": {"match_all": {}}},
                    size=batch_size
                )
                
                first_doc = True
                for doc in results:
                    if not first_doc:
                        temp_file.write(",\n")
                    else:
                        first_doc = False
                    
                    json.dump(doc, temp_file, ensure_ascii=False)
                    total_docs += 1
                    
                    if total_docs % batch_size == 0:
                        current_batch += 1
                        print(f"  Index '{index_name}' - Lot {current_batch} : {total_docs} documents")
                
                temp_file.write("\n]")
                temp_file.flush()
            
            # Télécharger vers Azure en utilisant BlobClient
            print(f"Téléchargement vers Azure Blob Storage...")
            blob_client = BlobClient.from_blob_url(
                os.getenv("AZURE_STORAGE_CONNECTION_URL").replace("{blob_path}", blob_path)
            )
            
            with open(temp_file_path, 'rb') as data:
                blob_client.upload_blob(
                    data, 
                    blob_type="BlockBlob",
                    content_type='application/json',
                    overwrite=True
                )
            
            # Supprimer le fichier temporaire
            os.unlink(temp_file_path)
            
            print(f"Exportation de '{index_name}' terminée : {total_docs} documents exportés vers '{blob_name}'")
            
        except Exception as e:
            print(f"Erreur lors de l'exportation de '{index_name}': {e}")
    
    print("Exportation terminée pour tous les index.")

if __name__ == "__main__":
    export_elasticsearch_to_azure_blob()