from typing import List, Optional
from sqlmodel import SQLModel
from datetime import datetime
from models.users import User
from services.api_laposte.produits_laposte import recuperer_produit_par_code_produit
from pydantic import Field


class MoyenPaiementSelectionne(SQLModel):
    nom: str
    url: str
    jeton: str

class MoyenPaiement(SQLModel):
    nom: str
    url: str
    label: str
    authorization_token: str

class FractionnementPaiement(SQLModel):
    """
    Modèle pour définir un fractionnement de paiement avec pourcentage.
    """
    numero_paiement: int = Field(description="Numéro du paiement (1, 2, 3)")
    pourcentage: float = Field(description="Pourcentage du montant total (ex: 33.33)", gt=0, le=100)
