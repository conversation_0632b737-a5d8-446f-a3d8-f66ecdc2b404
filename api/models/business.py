from sqlmodel import Field, SQLModel, Relationship
from sqlalchemy.dialects.postgresql import ARRAY
from datetime import datetime
from typing import Optional, List
from enum import Enum
from sqlalchemy import Column, JSON, String, Integer, Float
from constants.enumerations import SourceEnveloppeEnum, ValiditeAffranchissementEnum, SousTypeAffranchissementEnum, DeviseEnum, StatutPaiementEnum, StatutEnveloppeEnum, StatutVerificationEnum, TypeVerificationAffranchissementEnum, CategorieAffranchissementEnum, TypeAffranchissementEnum, NatureAffranchissementEnum, TypeRegleMetierEnum, StatutLotExpediteurEnum, OptionTraitementLotExpediteurEnum, DestinationEnveloppeEnum
from constants.affranchissements.nature import NATURES_AFFRANCHISSEMENT
from pydantic import computed_field, model_validator, BaseModel
from sqlalchemy import UniqueConstraint
from models.users import User
from uuid import uuid4
from sqlalchemy import event
from sqlalchemy.ext.hybrid import hybrid_property
from core.utils.dict import DotDict


class EnveloppeBase(SQLModel):
    statut: StatutEnveloppeEnum = StatutEnveloppeEnum.EDITION
    # statut_affranchissement: StatutAffranchissementEnum = StatutAffranchissementEnum.EDITION
    # statut_verification: StatutVerificationEnum = StatutVerificationEnum.VALIDE

    poids: float = Field(nullable=False, default=0)
    surpoids: bool = Field(nullable=False, default=False)
    surdimensionne: bool = Field(nullable=False, default=False)

    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    completed_at: datetime | None = Field(default=None)

class EnveloppePublic(EnveloppeBase):
    id: int
    affranchissements: List["Affranchissement"]
    photos: List["PhotoEnveloppe"]
    valorisation: dict | None = None

from typing import ClassVar

def _get_informations(self) -> dict:
    from services.enveloppe import ServiceEnveloppe
    service = ServiceEnveloppe(self)    
    return DotDict(**service.informations())

def _get_informations_exp(cls):
    return cls.informations_data

class Enveloppe(EnveloppeBase, table=True):
    "Enveloppe ou PLI"
    id: int | None = Field(default=None, primary_key=True)
    id_migration: str | None = Field(default=None, max_length=255, unique=True)
    uuid: str | None = Field(default_factory=lambda: str(uuid4()), max_length=255, unique=True)
    destination_enveloppe: DestinationEnveloppeEnum = DestinationEnveloppeEnum.METROPOLE
    source: SourceEnveloppeEnum = SourceEnveloppeEnum.MANUEL
    
    # Valorisation
    valorisation: dict = Field(
        default=None,
        sa_column=Column(JSON, nullable=True)
    )

    # Relations    
    # traitement_lot_enveloppes_id: int | None = Field(default=None, foreign_key="traitementlotenveloppes.id")
    # traitement_lot_enveloppes: Optional["TraitementLotEnveloppes"]  = Relationship(back_populates="enveloppes")

    expediteur_id: int | None = Field(default=None, foreign_key="expediteur.id")
    expediteur: Optional["Expediteur"] = Relationship(back_populates="enveloppes")

    destination_id: int | None = Field(default=None, foreign_key="destination.id")
    destination: Optional["Destination"] = Relationship(back_populates="enveloppes")

    site_id: int = Field(foreign_key="site.id")
    site: "Site" = Relationship(back_populates="enveloppes")

    affranchissements: list["Affranchissement"] = Relationship(back_populates="enveloppe", sa_relationship_kwargs={"lazy": "joined"})
    photos: list["PhotoEnveloppe"] = Relationship(back_populates="enveloppe")

    user_id: int = Field(foreign_key="user.id")
    user: User = Relationship(back_populates="enveloppes")

    # Ajouter la relation directe avec LotExpediteur
    lot_expediteur_id: int | None = Field(default=None, foreign_key="lotexpediteur.id")
    lot_expediteur: Optional["LotExpediteur"] = Relationship(back_populates="enveloppes")
    
    # Conserver la relation avec le casier pour le stockage physique
    casier_id: int | None = Field(default=None, foreign_key="casier.id")
    casier: Optional["Casier"] = Relationship(back_populates="enveloppes")

    # Informations
    informations_data: dict = Field(
        default={},
        sa_column=Column(JSON, nullable=True)
    )

    informations: ClassVar[dict] = hybrid_property(_get_informations,
                                                   expr=_get_informations_exp)
    
    @property
    def service(self):
        from services.enveloppe import ServiceEnveloppe
        return ServiceEnveloppe(self)
    

@event.listens_for(Enveloppe, "before_update")
def before_update_enveloppe(mapper, connection, instance):
    """Appelé avant la mise à jour d'une enveloppe"""
    from services.enveloppe import ServiceEnveloppe
    ServiceEnveloppe(instance).on_save()

@event.listens_for(Enveloppe, "before_insert")
def before_insert_enveloppe(mapper, connection, instance):
    """Appelé avant l'insertion d'une nouvelle enveloppe"""
    from services.enveloppe import ServiceEnveloppe
    ServiceEnveloppe(instance).on_save()


class Affranchissement(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)
    id_migration: str | None = Field(default=None, max_length=255, unique=True)
    
    # Enumerations qui catégorise l'affranchissement
    categorie: CategorieAffranchissementEnum = CategorieAffranchissementEnum.BEAU
    type: TypeAffranchissementEnum = TypeAffranchissementEnum.S10
    sous_type: SousTypeAffranchissementEnum = SousTypeAffranchissementEnum.NON_DETERMINE
    nature: NatureAffranchissementEnum | None = Field(default=None)
    origine: str | None = Field(default=None)

    @property
    def nature_instance(self):
        return NATURES_AFFRANCHISSEMENT.get(self.nature, None) or NATURES_AFFRANCHISSEMENT[NatureAffranchissementEnum.NON_DETERMINE]

    # Prix
    prix_unite_devise: float = Field(nullable=False, default=0)
    prix_unite_euros: float = Field(nullable=False, default=0)
    devise: DeviseEnum = DeviseEnum.EURO

    quantite: int = Field(nullable=False, default=1)
    poids_max: float = Field(nullable=False, default=0)
    code: str = Field(default="", max_length=200)
    
    # Provient soit des verifications, soit de l'utilisateur lui même
    statut: ValiditeAffranchissementEnum = ValiditeAffranchissementEnum.VALIDE

    # donnees: dict = Field(
    #     default={},
    #     sa_column=Column(JSON, nullable=False)
    # )

    enveloppe_id: int | None = Field(default=None, foreign_key="enveloppe.id")
    enveloppe: Enveloppe = Relationship(back_populates="affranchissements")

    # statut_verification: StatutVerificationEnum = StatutVerificationEnum.NON_DETERMINE
    verifications: list["VerificationAffranchissement"] = Relationship(back_populates="affranchissement")
    
    user_id: int = Field(foreign_key="user.id")
    user: User = Relationship()

    @computed_field
    @property
    def donnees(self) -> dict:
        from services.affranchissement import ServiceAffranchissement
        return ServiceAffranchissement(self).__class__.donnees(self.code)
    
    @property
    def service(self):
        from services.affranchissement import ServiceAffranchissement
        return ServiceAffranchissement(self)
    
    @property
    def modele_affranchissement(self):
        from services.affranchissement import ServiceAffranchissement
        return ServiceAffranchissement.get_modele_affranchissement({
            "categorie": str(self.categorie.value),
            "type": str(self.type.value),
            "devise": str(self.devise.value) if self.devise else None,
            "prix_unite_devise": self.prix_unite_devise,
            "quantite": self.quantite,
            "origine": self.origine
        })
    
    # @computed_field
    # @property
    # def prix_unite_euros(self) -> float:
    #     return self.service.prix_unite_euros()

    @computed_field
    @property
    def informations(self) -> dict:
        return DotDict(**self.service.informations())
    
@event.listens_for(Affranchissement, "before_update")
def before_update_affranchissement(mapper, connection, instance):
    """Appelé avant la mise à jour d'un affranchissement"""
    from services.affranchissement import ServiceAffranchissement
    instance.prix_unite_euros = ServiceAffranchissement(instance).prix_unite_euros()

@event.listens_for(Affranchissement, "before_insert")
def before_insert_affranchissement(mapper, connection, instance):
    """Appelé avant l'insertion d'un nouvel affranchissement"""
    from services.affranchissement import ServiceAffranchissement
    instance.prix_unite_euros = ServiceAffranchissement(instance).prix_unite_euros()


class VerificationAffranchissement(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)

    type: TypeVerificationAffranchissementEnum = TypeVerificationAffranchissementEnum.AUTRE
    statut: StatutVerificationEnum = StatutVerificationEnum.INVALIDE
    message: str | None = Field(default=None, max_length=255)
    donnees: dict = Field(
        default={},
        sa_column=Column(JSON, nullable=False)
    )

    affranchissement_id: int | None = Field(default=None, foreign_key="affranchissement.id")
    affranchissement: Affranchissement = Relationship(back_populates="verifications")
    

class PhotoEnveloppe(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)

    format: str | None = Field(default=None, max_length=255)
    qualite: str | None = Field(default=None, max_length=255)
    largeur: float | None = Field(default=None)
    hauteur: float | None = Field(default=None)
    orientation: str | None = Field(default=None, max_length=255)
    url: str | None = Field(default=None, max_length=2048, exclude=True)

    commentaire: str | None = Field(default=None, max_length=255)

    enveloppe_id: int | None = Field(default=None, foreign_key="enveloppe.id")
    enveloppe: Enveloppe = Relationship(back_populates="photos")

    @computed_field
    @property
    def public_url(self) -> str | None:
        """
        Retourne l'URL publique de la photo en remplaçant le jeton SAS par le jeton public
        """
        if not self.url:
            return None
        
        base_url = self.url.split('?')[0]
        from core.config import settings
        return f"{base_url}?{settings.AZURE_STORAGE_PUBLIC_SAS_TOKEN}"

class Expediteur(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)
    id_migration: str | None = Field(default=None, max_length=255, unique=True)

    prenom: str | None = Field(default=None, max_length=255)
    nom: str | None = Field(default=None, max_length=255)
    adresse: str | None = Field(default=None, max_length=255)
    ville: str | None = Field(default=None, max_length=255)
    code_postal: str | None = Field(default=None, max_length=255)
    pays: str | None = Field(default=None, max_length=255)


    siret: str | None = Field(default=None, max_length=255)
    coclico: str | None = Field(default=None, max_length=255)


    enveloppes: list["Enveloppe"] = Relationship(back_populates="expediteur")
    messages: list["MessageExpediteur"] = Relationship(back_populates="expediteur")

    lots: list["LotExpediteur"] = Relationship(back_populates="expediteur")

    @classmethod
    def cherche_ou_cree(cls, session, data: dict) -> "Expediteur":
        existe = session.query(Expediteur).filter(
            Expediteur.nom == data.get("nom"),
            Expediteur.adresse == data.get("adresse"),
            Expediteur.ville == data.get("ville"),
            Expediteur.code_postal == data.get("code_postal")
        ).first()

        if not existe:
            existe = Expediteur(**data)
            session.add(existe)
            session.commit()
            session.refresh(existe)

        return existe


class CodeAffranchissementFrauduleux(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)
    code: str = Field(max_length=255, nullable=False, unique=True, index=True)
    # type: TypeAffranchissementEnum = TypeAffranchissementEnum.AUTO
    created_at: datetime = Field(default_factory=datetime.now)


class Destination(SQLModel, table=True):
    """
    Destination est une destination de livraison
    """
    id: int | None = Field(default=None, primary_key=True)
    id_migration: str | None = Field(default=None, max_length=255, unique=True)

    nom_fr: str | None = Field(default=None, max_length=255)
    nom_en: str | None = Field(default=None, max_length=255)
    alpha2: str | None = Field(default=None, max_length=2)
    alpha3: str | None = Field(default=None, max_length=3)
    num: int | None = Field(default=None)
    zone: str | None = Field(default=None, max_length=3)
    supplement: int | None = Field(default=None)
    created_at: datetime = Field(default_factory=datetime.now)

    enveloppes: list["Enveloppe"] = Relationship(back_populates="destination")

class Site(SQLModel, table=True):
    """
    Site de traitement des envellopes LaPoste
    """
    id: int | None = Field(default=None, primary_key=True)
    id_migration: str | None = Field(default=None, max_length=255, unique=True)

    nom: str | None = Field(default=None, max_length=255)
    created_at: datetime = Field(default_factory=datetime.now)

    enveloppes: list["Enveloppe"] = Relationship(back_populates="site")
    lots_expediteur: list["LotExpediteur"] = Relationship(back_populates="site")
    
class RegleMetier(SQLModel, table=True):
    """
    RegleMetier est une regle de gestion qui peut etre appliquée a un affranchissement ou autre.
    Valeur est un dictionnaire contenant les données de la regle.
    """
    id: int | None = Field(default=None, primary_key=True)
    cle: str | None = Field(default=None, max_length=255)
    type_affranchissement: TypeAffranchissementEnum | None = Field(default=None)
    type_regle: TypeRegleMetierEnum = TypeRegleMetierEnum.VALEUR
    valeur: dict = Field(
        default={},
        sa_column=Column(JSON, nullable=False)
    )
    active: bool = Field(default=True)

    __table_args__ = (
        UniqueConstraint('cle', 'type_regle', 'type_affranchissement', name='uq_cle_type_regle_type_affranchissement'),
    )

    @classmethod
    def valeur_pour(cls, session, type_regle = None, cle = None, valeur_defaut = None):
        seuil = session.query(RegleMetier).filter(
            RegleMetier.type_regle == type_regle,
            RegleMetier.cle == cle
        ).first()

        return (seuil.valeur.get("valeur", None) if seuil else None) or valeur_defaut



class LotExpediteur(SQLModel, table=True):
    """
    LotExpediteur est un regroupement d'enveloppes pour un même expediteur.
    Permet de gérer l'avancement du recouvrement des frais d'un fraudeur.
    """
    id: int | None = Field(default=None, primary_key=True)
    id_public: str | None = Field(default=None, max_length=255, unique=True)

    nom: str | None = Field(default=None, max_length=255)
    expediteur_id: int | None = Field(default=None, foreign_key="expediteur.id")
    expediteur: Expediteur | None = Relationship()

    enveloppes: list["Enveloppe"] = Relationship(back_populates="lot_expediteur")
    
    statut: StatutLotExpediteurEnum = Field(default=StatutLotExpediteurEnum.OUVERT)
    
    # Choix Expediteur traitement
    option_recouvrement: OptionTraitementLotExpediteurEnum | None = None
    option_recouvrement_params: dict = Field(
        default={},
        sa_column=Column(JSON, nullable=False)
    )

    adresse_renvoi: str | None = Field(default=None, max_length=255)
    
    date_creation: datetime = Field(default_factory=datetime.now)
    date_paiement: datetime | None = Field(default=None)
    date_recuperation: datetime | None = Field(default=None)

    # Site
    site_id: int | None = Field(default=None, foreign_key="site.id")
    site: Optional["Site"] | None = Relationship(back_populates="lots_expediteur")

    # Casier actuellement utilisé pour les nouvelles enveloppes
    casier_id: int | None = Field(default=None, foreign_key="casier.id")
    casier: Optional["Casier"] | None = Relationship(
        sa_relationship_kwargs={"foreign_keys": "[LotExpediteur.casier_id]"}
    )
    
    # Tous les casiers utilisés par ce lot (relation inverse)
    casiers: list["Casier"] = Relationship(
        back_populates="lot_expediteur",
        sa_relationship_kwargs={"foreign_keys": "[Casier.lot_expediteur_id]"}
    )
    
    messages: list["MessageExpediteur"] = Relationship(back_populates="lot_expediteur")

    # Paiement
    montant_ttc: float | None = Field(default=None)
    paiements: list["PaiementLotExpediteur"] = Relationship(back_populates="lot_expediteur")

    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    user_modification_id: int | None = Field(default=None, foreign_key="user.id")
    user_modification: Optional["User"] | None = Relationship()
    
    def est_visible_par_expediteur(self) -> bool:
        """
        Vérifie si le lot est visible par l'expediteur
        """
        return self.statut in [StatutLotExpediteurEnum.NOTIFIE,
                               StatutLotExpediteurEnum.PAIEMENT_PARTIEL,
                               StatutLotExpediteurEnum.PAIEMENT_TOTAL_RECU,
                               StatutLotExpediteurEnum.TRAITE_OPERATIONNEL]

    def generer_id_public(self):
        """Génère un identifiant public unique au format LT + numéro + FR"""
        import random
        # Sur 7 chiffres
        numero_aleatoire = random.randint(1, 9999999)
        numero_aleatoire = str(numero_aleatoire).zfill(7)        
        self.id_public = f"LT{numero_aleatoire}FR"

@event.listens_for(LotExpediteur, "before_insert")
def before_insert_lot_expediteur(mapper, connection, instance):
    """Appelé avant l'insertion d'un nouveau lot expéditeur pour générer l'id_public"""
    if not instance.id_public:
        instance.generer_id_public()

@event.listens_for(LotExpediteur, "before_update")
def before_update_lot_expediteur(mapper, connection, instance):
    """Appelé avant la mise à jour d'un lot expéditeur pour générer l'id_public"""
    if not instance.id_public:
        instance.generer_id_public()


class Casier(SQLModel, table=True):
    """
    Casier est un emplacement physique où sont stockées les enveloppes d'un lot.
    Un casier ne peut être associé qu'à un seul LotExpediteur à la fois.
    """
    id: int | None = Field(default=None, primary_key=True)
    numero: str = Field(max_length=50, nullable=False)
    emplacement: str | None = Field(default=None, max_length=255)
    # capacite_max: int = Field(default=50)
    # statut: StatutCasierEnum = StatutCasierEnum.DISPONIBLE
    
    site_id: int = Field(foreign_key="site.id")
    site: Site = Relationship()

    # Relation avec les enveloppes stockées dans ce casier
    enveloppes: list["Enveloppe"] = Relationship(back_populates="casier")
    
    # Relation directe avec LotExpediteur (indique quel lot utilise actuellement ce casier)
    lot_expediteur_id: int | None = Field(default=None, foreign_key="lotexpediteur.id")
    lot_expediteur: Optional["LotExpediteur"] = Relationship(
        back_populates="casiers",
        sa_relationship_kwargs={"foreign_keys": "[Casier.lot_expediteur_id]"}
    )
    
    date_attribution: datetime | None = Field(default=None)
    date_liberation: datetime | None = Field(default=None)
    
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now) # Lot expediteur associé à travers les enveloppes

class MessageExpediteur(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)

    expediteur_id: int | None = Field(default=None, foreign_key="expediteur.id")
    expediteur: Optional["Expediteur"] = Relationship(back_populates="messages")

    lot_expediteur_id: int | None = Field(default=None, foreign_key="lotexpediteur.id")
    lot_expediteur: Optional["LotExpediteur"] = Relationship(back_populates="messages")

class PaiementLotExpediteur(SQLModel, table=True):
    """
    Paiement fractionné pour un LotExpediteur.
    Permet de gérer les paiements en plusieurs fois avec pourcentages.
    """
    id: int | None = Field(default=None, primary_key=True)

    # Identifiant unique du paiement
    id_paiement: str | None = Field(default=None, max_length=255, unique=True)

    # Relation avec le lot expéditeur
    lot_expediteur_id: int = Field(foreign_key="lotexpediteur.id")
    lot_expediteur: LotExpediteur = Relationship(back_populates="paiements")

    # Fractions
    numero_paiement: int = Field(description="Numéro du paiement (1, 2, 3)")
    pourcentage: float = Field(description="Pourcentage du montant total (ex: 33.33)")
    montant_ttc: float = Field(description="Montant calculé en centimes")

    # Statut et suivi
    statut: StatutPaiementEnum = Field(default=StatutPaiementEnum.NON_INITIALISE)

    transactions: list["TransactionPaiementLotExpediteur"] = Relationship(back_populates="paiement_lot_expediteur")

    # Dates
    date_creation: datetime = Field(default_factory=datetime.now)
    date_paiement: datetime | None = Field(default=None)
    date_echeance: datetime | None = Field(default=None)

    def verifier_statut_paiement(self, session=None) -> StatutPaiementEnum:
        """Met à jour le statut du paiement à partir des statuts de toutes ses transactions"""
        from services.api_laposte.encaissement import ServiceEncaissement

        if not self.transactions:
            self.statut = StatutPaiementEnum.NON_INITIALISE
            return self.statut

        # Met à jour tous les statuts individuels
        for transaction in self.transactions:
            transaction.verifier_statut_paiement(session=session)

        # Priorité des statuts (du plus "positif" au plus "bloquant")
        priorite = [
            StatutPaiementEnum.PAYE,
            StatutPaiementEnum.AUTORISE,
            StatutPaiementEnum.EN_ATTENTE,
            StatutPaiementEnum.REMBOURSE,
            StatutPaiementEnum.ANNULE,
            StatutPaiementEnum.ECHEC,
            StatutPaiementEnum.NON_INITIALISE,
        ]

        # Classement des statuts par priorité
        transactions_par_priorite = sorted(
            self.transactions,
            key=lambda t: priorite.index(t.statut)
        )

        # Prend le meilleur statut
        self.statut = transactions_par_priorite[0].statut

        if session:
            session.add(self)
            session.commit()

        return self.statut

class TransactionPaiementLotExpediteur(SQLModel, table=True):
    id: int | None = Field(default=None, primary_key=True)

    paiement_lot_expediteur_id: int = Field(foreign_key="paiementlotexpediteur.id")
    paiement_lot_expediteur: PaiementLotExpediteur = Relationship(back_populates="transactions")

    # Informations scelius
    methode : str | None = Field(default=None, max_length=255)
    statut: StatutPaiementEnum = Field(default=StatutPaiementEnum.NON_INITIALISE)
    id_transaction: str = Field(max_length=255, unique=True)
    donnees: dict = Field(
        default={},
        sa_column=Column(JSON, nullable=False)
    )

    date_creation: datetime = Field(default_factory=datetime.now)
    date_maj: datetime = Field(default_factory=datetime.now)
    date_expiration: datetime | None = Field(default=None)
    

    def verifier_statut_paiement(self, session=None) -> StatutPaiementEnum:
        """Vérifie le statut du paiement via l'API d'encaissement"""
        from services.api_laposte.encaissement import ServiceEncaissement

        if not self.id_transaction:
            return self.statut
        
        # Mapping des statuts de l'API d'encaissement vers les statuts internes
        mapping_statuts = {
            "PENDING": StatutPaiementEnum.EN_ATTENTE,
            "AUTHORIZED": StatutPaiementEnum.AUTORISE,
            "CAPTURED": StatutPaiementEnum.PAYE,
            "FAILED": StatutPaiementEnum.ECHEC,
            "CANCELLED": StatutPaiementEnum.ANNULE,
            "REFUNDED": StatutPaiementEnum.REMBOURSE
        }

        # Si le statut est un statut final, pas besoin de refaire la requete
        if self.statut not in [StatutPaiementEnum.AUTORISE, StatutPaiementEnum.EN_ATTENTE]:
            return self.statut
        
        donnees = ServiceEncaissement().suivre_transaction(self.id_transaction)

        statut = mapping_statuts.get(donnees.get("status"), self.statut)
        self.statut = statut

        if session:
            session.add(self)
            session.commit()

        return statut
