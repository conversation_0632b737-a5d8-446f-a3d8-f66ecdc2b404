from sqlmodel import Field, Relationship, SQLModel
from pydantic import EmailStr
from datetime import datetime
from sqlalchemy import Column, JSO<PERSON>, String, Integer, Float
from enum import Enum
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy import UniqueConstraint
import uuid

class UserRole(str, Enum):
    GUEST = "guest"
    USER = "user"
    MANAGER = "manager"
    ADMIN = "admin"

# Shared properties
class UserBase(SQLModel):
    email: EmailStr = Field(unique=True, index=True, max_length=255)
    is_active: bool = True
    role: UserRole = Field(default=UserRole.USER)
    full_name: str | None = Field(default=None, max_length=255)
    pseudo: str | None = Field(default=None, max_length=255)
    profile_picture: str | None = Field(default=None, max_length=255)
    site_id: int | None = Field(default=None)

# Properties to receive via API on creation
class UserCreate(UserBase):
    password: str = Field(min_length=8, max_length=40)

class UserRegister(SQLModel):
    email: EmailStr = Field(max_length=255)
    password: str = Field(min_length=8, max_length=40)

# Properties to receive via API on update, all are optional
class UserUpdate(UserBase):
    email: EmailStr | None = Field(default=None, max_length=255)  # type: ignore
    password: str | None = Field(default=None, min_length=8, max_length=40)
    site_id: int | None = Field(default=None)

class UserUpdateMe(SQLModel):
    full_name: str | None = Field(default=None, max_length=255)
    email: EmailStr | None = Field(default=None, max_length=255)
    pseudo: str | None = Field(default=None, max_length=255)
    profile_picture: str | None = Field(default=None, max_length=255)

class UpdatePassword(SQLModel):
    current_password: str = Field(min_length=8, max_length=40)
    new_password: str = Field(min_length=8, max_length=40)

# Database model, database table inferred from class name
class User(UserBase, table=True):
    id: int | None = Field(default=None, primary_key=True)
    hashed_password: str

    site_id: int = Field(
        default=1,
        foreign_key="site.id"
    )
    site: "Site" = Relationship()

    # uploads: list["Upload"] = Relationship(back_populates="user")
    enveloppes: list["Enveloppe"] = Relationship(back_populates="user")

    # Token unique et privé pour l'ajout de photos
    token_ajout_photo: str = Field(
        default_factory=lambda: str(uuid.uuid4()),
        max_length=255
    )

class SiteResponse(SQLModel):
    id: int
    nom: str 

# Properties to return via API, id is always required
class UserPublic(UserBase):
    id: int
    site: SiteResponse

class UsersPaginatedPublic(SQLModel):
    items: list[UserPublic]
    total_items: int
    total_pages: int
    current_page: int
    page_size: int

class Message(SQLModel):
    message: str


class Token(SQLModel):
    access_token: str
    token_type: str = "bearer"


# Contents of JWT token
class TokenPayload(SQLModel):
    sub: int | None = None


class NewPassword(SQLModel):
    token: str
    new_password: str = Field(min_length=8, max_length=40)


###


