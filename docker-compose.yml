services:
  # DATABASE
  db:
    image: postgres:12
    restart: always
    volumes:
      - ./data/pg:/var/lib/postgresql/data/pgdata
    env_file:
      - .env
    environment:
      - PGDATA=/var/lib/postgresql/data/pgdata
    # Script to CREATE multiple DB

  # REDIS
  redis:
    image: redis:7-alpine
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - ./data/redis:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  #API
  api:
    restart: always
    ports:
      - "8082:80"
      - "5555:5555"
    depends_on:
      - db
      - redis
    env_file:
      - .env
    volumes:
      - ./api/:/app/
      - ./data/:/data/
    build:
      context: ./api
      args:
        INSTALL_DEV: "true"
    platform: linux/amd64

  frontend:
    restart: always
    ports:
      - "8088:80"
    build:
      context: ./frontend
      dockerfile: dev.Dockerfile
      args:
        - VITE_API_URL=http://localhost:8082
        - NODE_ENV=production
    env_file:
      - frontend.env
    volumes:
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
      - ./CHANGELOG.md:/app/public/CHANGELOG.md
      - ./frontend/package.json:/app/package.json
      - ./data/:/data/
      - ./frontend/entrypoint.sh:/entrypoint.sh
