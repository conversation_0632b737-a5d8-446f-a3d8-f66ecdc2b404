# Documentation API - Gestion des Enveloppes et Affranchissements

## Introduction

Cette documentation décrit le workflow complet pour la gestion des enveloppes et des affranchissements dans l'application. L'objectif est de permettre aux utilisateurs de créer des enveloppes, d'y ajouter des affranchissements, et de les valoriser pour détecter les sous-affranchissements.

## Workflow Principal d'une Enveloppe

![Workflow Principal](./flow.png)

### 1. Création d'une Enveloppe

Une enveloppe est créée avec un statut initial "EDITION". 
Les informations requises sont :

- `site_id` : ID du site d'origine
- `poids` : Poids en grammes
- `surpoids` : Boolean indiquant si l'enveloppe est en surpoids
- `surdimensionne` : Boolean indiquant si l'enveloppe est surdimensionnée
- `produit` : Type de produit (LV, LINT, etc.)
- `destination_id` : ID de la destination (optionnel)
- `expediteur_id` : ID de l'expéditeur (optionnel)

Note: Un utilisateur ne peut avoir qu'une seule enveloppe en mode "EDITION" à la fois.
Cela permet notamment d'uploader des photos pour l'enveloppe en cours d'édition.

### 2. Ajout d'Affranchissements

Plusieurs types d'affranchissements peuvent être ajoutés à une enveloppe :

#### a. Affranchissements par Code
- Codes S10 (ex: LL014699171FR)
- Codes SD87 (ex: 87000922411888l)
- Codes SD86 (ex: SD86400667047777E)
```json
POST /enveloppes/{id}/add
{
    "code": "LL014699171FR"
}
```

#### b. Affranchissements Valorisables
- Type "VAL" avec montant et devise
- Nécessite une nature d'affranchissement (ex: LV_20)
```json
POST /enveloppes/{id}/add
{
    "categorie": "MARI",
    "type": "VAL",
    "devise": "EURO",
    "prix_unite_devise": 1.26
}
```


Après chaque ajout d'affranchissement, un objet est retourné avec les informations de l'affranchissement et de l'enveloppe. Voir Swagger pour plus d'informations.


### 3. Cycle de Vie d'une Enveloppe

Une enveloppe passe par différents statuts :

1. **EDITION** : État initial, modifications possibles
2. **VALORISE** : Après calcul des montants
3. **TERMINEE** : État final, plus aucune modification possible

## Points d'API Importants

### Enveloppes

1. `POST /enveloppes/` : Création d'une enveloppe
2. `POST /enveloppes/{id}/add` : Ajout d'un affranchissement
3. `PUT /enveloppes/{id}/affranchissements/{aff_id}` : Mise à jour d'un affranchissement
4. `POST /enveloppes/{id}/valoriser` : Valorisation de l'enveloppe
5. `POST /enveloppes/{id}/terminer` : Finalisation de l'enveloppe

### Énumérations et Données de Référence

1. `GET /enumerations/produits` : Liste des types de produits disponibles (LV, LINT, etc.)
2. `GET /enumerations/affranchissements` : Liste des modèles d'affranchissements avec leurs caractéristiques
3. `GET /enumerations/natures` : Liste des natures d'affranchissement avec leurs prix unitaires

### Destinations

1. `GET /destinations/` : Liste des destinations avec pagination et recherche
   - Paramètres optionnels: `skip`, `limit`, `search`
2. `GET /destinations/{id}` : Détails d'une destination
3. `POST /destinations/` : Création d'une destination
4. `PUT /destinations/{id}` : Mise à jour d'une destination

### Expéditeurs

1. `GET /expediteurs/` : Liste des expéditeurs avec pagination et recherche
   - Paramètres optionnels: `skip`, `limit`, `search`
2. `GET /expediteurs/{id}` : Détails d'un expéditeur
3. `POST /expediteurs/` : Création d'un expéditeur
4. `PUT /expediteurs/{id}` : Mise à jour d'un expéditeur

### Sites

1. `GET /sites/` : Liste des sites avec pagination et recherche
   - Paramètres optionnels: `skip`, `limit`, `search`
2. `GET /sites/{id}` : Détails d'un site
3. `POST /sites/` : Création d'un site
4. `PUT /sites/{id}` : Mise à jour d'un site

### Photos

1. `GET /users/me/qrcode` : Génération du QR code pour l'ajout de photos
2. `POST /enveloppes/photos/{token}` : Ajout de photo à l'enveloppe en cours (EDITION)
   - Accepte uniquement les fichiers image
   - Le token doit être valide et correspondre à un utilisateur
   - L'utilisateur doit avoir une enveloppe en édition

### Authentification

1. `POST /login/access-token` : Obtention d'un token d'accès
   - Corps de la requête : `username` (email) et `password`
2. `POST /login/test-token` : Test de validité du token
3. `POST /users/signup` : Création d'un compte utilisateur (si autorisé)

## Paramètres de Pagination

Pour les endpoints supportant la pagination :
- `skip` : Nombre d'éléments à sauter (défaut: 0)
- `limit` : Nombre maximum d'éléments à retourner (défaut: 10)
- `search` : Terme de recherche (optionnel)

## Headers Requis

Pour toutes les requêtes authentifiées :
```
Authorization: Bearer <token>
```

Pour l'upload de fichiers :
```
Content-Type: multipart/form-data
```

## Gestion des Photos

1. L'utilisateur génère un QR code via l'endpoint `/users/me/qrcode`
2. Le QR code contient un token unique
3. Les photos peuvent être ajoutées via l'endpoint `/enveloppes/photos/{token}`
4. Les photos sont automatiquement associées à l'enveloppe en cours d'édition

## Règles Métier Importantes

1. Une seule enveloppe en édition par utilisateur
2. Les affranchissements doivent être complets avant valorisation
3. Une enveloppe valorisée peut retourner en édition si modifiée
4. Une enveloppe terminée ne peut plus être modifiée
5. Les photos sont uniquement acceptées pour l'enveloppe en cours d'édition

## Frontend Actions

L'API retourne une propriété après chaque ajout/update d'affranchissement : `frontend_action` qui indique l'action à effectuer côté frontend :

- `RIEN` : Aucune action spécifique requise
- `COMPLETER` : L'affranchissement nécessite des informations complémentaires (apopup avec les verificatons effectuées + champs figées + champ a compléter)

## Recommandations pour l'Implémentation Frontend

1. Implémenter un système de gestion d'état pour suivre le statut de l'enveloppe
2. Prévoir des formulaires dynamiques pour les différents types d'affranchissements
3. Gérer les validations côté client avant l'envoi à l'API
4. Implémenter un système de feedback utilisateur pour les erreurs et succès
5. Prévoir un workflow de confirmation avant la finalisation d'une enveloppe

## Gestion des Erreurs

L'API retourne des codes d'erreur standards :
- 400 : Erreur de validation ou données incorrectes
- 403 : Action non autorisée
- 404 : Ressource non trouvée
- 409 : Conflit (ex: enveloppe déjà existante)
