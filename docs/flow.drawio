<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:135.0) Gecko/20100101 Firefox/135.0" version="26.1.0">
  <diagram name="Workflow Enveloppe" id="xcKTvfbIW6Iw430nsu_O">
    <mxGraphModel dx="1912" dy="980" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-1" value="Début" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="364" y="70" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-2" value="Création d&#39;une enveloppe&lt;br&gt;(Statut: EDITION)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="344" y="140" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-3" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-1" target="cJeBhY1JI6YaOUq9nzVQ-2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-4" value="Type d&#39;affranchissement?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="334" y="240" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-5" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-2" target="cJeBhY1JI6YaOUq9nzVQ-4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-6" value="Ajout affranchissement par code&lt;br&gt;(S10, SD87, SD86)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="159" y="320" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-7" value="Ajout affranchissement en utilisant les modeles existants" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="509" y="320" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-8" value="Numerique" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-4" target="cJeBhY1JI6YaOUq9nzVQ-6">
          <mxGeometry x="-0.2987" y="20" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-9" value="Papier" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-4" target="cJeBhY1JI6YaOUq9nzVQ-7">
          <mxGeometry x="-0.4103" y="-20" width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-10" value="frontend_action = COMPLETER?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="314" y="360" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-11" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-6" target="cJeBhY1JI6YaOUq9nzVQ-10">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-12" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-7" target="cJeBhY1JI6YaOUq9nzVQ-10">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-13" value="Compléter les informations&lt;br&gt;d&#39;affranchissement" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="110" y="480" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-14" value="Oui" style="endArrow=classic;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-10" target="cJeBhY1JI6YaOUq9nzVQ-13">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="330" as="sourcePoint" />
            <mxPoint x="440" y="280" as="targetPoint" />
            <Array as="points">
              <mxPoint x="190" y="430" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-15" value="Ajouter plus&lt;br&gt;d&#39;affranchissements?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="334" y="470" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-16" value="Non" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-10" target="cJeBhY1JI6YaOUq9nzVQ-15">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="330" as="sourcePoint" />
            <mxPoint x="440" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-17" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-13" target="cJeBhY1JI6YaOUq9nzVQ-15">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="330" as="sourcePoint" />
            <mxPoint x="440" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-18" value="Oui" style="endArrow=classic;html=1;edgeStyle=orthogonalEdgeStyle;exitX=0;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-15" target="cJeBhY1JI6YaOUq9nzVQ-4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="374" y="460" />
              <mxPoint x="100" y="460" />
              <mxPoint x="100" y="260" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-19" value="Valorisation de l&#39;enveloppe&lt;br&gt;(Statut: VALORISE)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="334" y="580" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-20" value="Non" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-15" target="cJeBhY1JI6YaOUq9nzVQ-19">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="330" as="sourcePoint" />
            <mxPoint x="440" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-21" value="Modifications&lt;br&gt;nécessaires?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="334" y="660" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-22" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-19" target="cJeBhY1JI6YaOUq9nzVQ-21">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="330" as="sourcePoint" />
            <mxPoint x="440" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-23" value="Oui" style="endArrow=classic;html=1;edgeStyle=orthogonalEdgeStyle;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-21" target="cJeBhY1JI6YaOUq9nzVQ-2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="80" y="170" as="targetPoint" />
            <Array as="points">
              <mxPoint x="60" y="700" />
              <mxPoint x="60" y="170" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-24" value="Finalisation de l&#39;enveloppe&lt;br&gt;(Statut: TERMINEE)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="334" y="780" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-25" value="Non" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-21" target="cJeBhY1JI6YaOUq9nzVQ-24">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="330" as="sourcePoint" />
            <mxPoint x="440" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-26" value="Fin" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="560" y="780" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-27" value="" style="endArrow=classic;html=1;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-24" target="cJeBhY1JI6YaOUq9nzVQ-26">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="330" as="sourcePoint" />
            <mxPoint x="440" y="280" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-28" value="Génération QR code&lt;br&gt;pour photos" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="600" y="140" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-29" value="Upload photos via&lt;br&gt;token QR code" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="600" y="220" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-30" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-28" target="cJeBhY1JI6YaOUq9nzVQ-29">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-31" value="Processus parallèle&lt;br&gt;(à tout moment pendant EDITION)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="600" y="100" width="140" height="30" as="geometry" />
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-32" value="" style="endArrow=classic;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;dashed=1;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-2" target="cJeBhY1JI6YaOUq9nzVQ-28">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="cJeBhY1JI6YaOUq9nzVQ-33" value="" style="endArrow=classic;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;dashed=1;" edge="1" parent="1" source="cJeBhY1JI6YaOUq9nzVQ-29" target="cJeBhY1JI6YaOUq9nzVQ-2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390" y="410" as="sourcePoint" />
            <mxPoint x="440" y="360" as="targetPoint" />
            <Array as="points">
              <mxPoint x="540" y="250" />
              <mxPoint x="540" y="190" />
            </Array>
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
