# Workflow pour la gestion des enveloppes & lots d'expéditeurs
Lorsqu'une Enveloppe est terminé sur l'interface, on va vérifier si l'Expéditeur a X Enveloppe ou le statut vaut FRAUDULEUSE ou SOUS_AFFRANCHI ou l'Utilisateur peut demander un LotExpediteur.
Si c'est le cas, alors on créer ou récupere un LotExpediteur en Statut OUVERT.
L'ensemble des enveloppes pour cet Expéditeur iront dans ce LotExpéditeur tant que le LotExpediteur est OUVERT, si celui-ci change de statut, les nouvelles Enveloppes iront dans un nouveau LotExpediteur.
Chaque LotExpediteur est associé à un ou plusieurs Casier qui contiendra donc les Enveloppes du LotExpediteur.

## Gestion des casiers
Chaque Casier a une capacité maximale d'enveloppes qu'il peut contenir. Lorsqu'une Enveloppe doit être mise dans un casier, l'utilisateur peut décider d'obtenir un nouveau Casier s'il considère que le casier actuel est plein, même si le nombre maximum n'est pas atteint.
Le système proposera automatiquement un nouveau casier si le casier actuel a atteint sa capacité maximale (nombre de plis).

A tout moment, l'Utilisateur peut refuser le Casier pour en demander un nouveau 

Ensuite, un Administrateur décidera de traiter le LotExpediteur, l'Expediteur sera donc NOTIFIE par un moyen de communication (à définir), le LotExpediteur passera en statut TRAITEMENT, l'Expediteur sera notifié et 3 choix lui seront proposées (A, B ou C).
Une fois que l'Expéditeur aura payé, les Enveloppes sortiront du Casier pour être traitées selon le choix fait par l'Expéditeur.
Le LotExpediteur passera donc en TRAITE et le Casier sera vide et a nouveau disponible pour un autre LotExpediteur.

Si pas de paiement reçu après X jours, on considère option --> Libourne.


# FlowChart
flowchart TD
    A[Enveloppe terminée] --> B{Expéditeur a X enveloppes\n dites FRAUDULEUSE\nou SOUS_AFFRANCHI?}
    B -->|Non| C[Continuer a saisir des Enveloppes]
    B -->|Oui| D{LotExpediteur\nen statut OUVERT\nexiste?}
    D -->|Oui| E[Ajouter l'enveloppe\nau LotExpediteur]
    D -->|Non| F[Créer nouveau LotExpediteur\nstatut = OUVERT]
    F --> G[Attribuer un Casier\nstatut = OCCUPE]
    G --> E
    E --> H[Stocker l'enveloppe\ndans le Casier associé]
    H --> C

    
    S[Administrateur décide\nde traiter le LotExpediteur] --> I[Notification\nde l'Expéditeur]
    I --> J[LotExpediteur\nstatut = NOTIFIE]
    J --> K{Choix de\nl'Expéditeur}
    K -->|Option A| L[Livraison\naux destinataires]
    K -->|Option B| M[Récupération\nsur site]
    K -->|Option C| N[Renvoi à\nl'expéditeur]
    L --> O[Paiement\npar l'Expéditeur\nLotExpediteur statut = PAIEMENT_RECU]
    M --> O
    N --> O
    O --> P[Traitement des enveloppes\nselon le choix]
    P --> Q[LotExpediteur\nstatut = TRAITE]
    Q --> R[Casier\nstatut = DISPONIBLE]

# FlowChart
flowchart TD
    %% Styles pour améliorer la lisibilité
    classDef statut fill:#f9f,stroke:#333,stroke-width:2px,color:black,font-weight:bold
    classDef action fill:#bbf,stroke:#333,stroke-width:1px
    classDef decision fill:#ff9,stroke:#333,stroke-width:2px
    classDef start fill:#9f9,stroke:#333,stroke-width:2px
    
    %% Première partie: Création du lot
    A[/"🔄 DÉBUT: Enveloppe terminée"/] --> B{"❓ Expéditeur a X enveloppes\n FRAUDULEUSE ou\n SOUS_AFFRANCHI?"}
    B -->|"❌ Non"| C["📝 Continuer à saisir\ndes Enveloppes"]
    B -->|"✅ Oui"| D{"❓ Existe-t-il un\nLotExpediteur avec\nstatut = OUVERT?"}
    D -->|"✅ Oui"| E["➕ Ajouter l'enveloppe\nau LotExpediteur existant"]
    D -->|"❌ Non"| F["🆕 Créer nouveau LotExpediteur\nstatut = OUVERT"]
    F --> G["🔑 Attribuer un Casier\nstatut = OCCUPE"]
    G --> E
    E --> H["📥 Stocker l'enveloppe\ndans le Casier associé"]
    H --> I{"❓ Casier plein ou\nutilisateur demande\nnouveau casier?"}
    I -->|"✅ Oui"| J["🔄 Attribuer un\nnouveau Casier"]
    I -->|"❌ Non"| C
    J --> C
    
    %% Deuxième partie: Traitement du lot
    S["👨‍💼 Administrateur décide\nde traiter le LotExpediteur"] --> T["📧 Notification\nde l'Expéditeur"]
    T --> U["📢 LotExpediteur\nstatut = NOTIFIE"]
    U --> V{"🔄 Choix de\nl'Expéditeur"}
    V -->|"🚚 Option A:\nLivraison"| W["📦 Livraison\naux destinataires"]
    V -->|"🏢 Option B:\nRécupération"| X["🔍 Récupération\nsur site"]
    V -->|"↩️ Option C:\nRenvoi"| Y["📮 Renvoi à\nl'expéditeur"]
    W --> Z["💰 Paiement par l'Expéditeur\nLotExpediteur statut = PAIEMENT_RECU"]
    X --> Z
    Y --> Z
    Z --> AA["⚙️ Traitement des enveloppes\nselon le choix"]
    AA --> BB["✅ LotExpediteur\nstatut = TRAITE"]
    BB --> CC["🔓 Casier\nstatut = DISPONIBLE"]
    
    %% Application des styles
    class A,S start
    class B,D,I,V decision
    class C,E,F,G,H,J,T,U,W,X,Y,Z,AA,BB,CC action
    class F,G,J,U,Z,BB,CC statut
