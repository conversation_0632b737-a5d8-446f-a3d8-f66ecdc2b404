# Workflow
1. Quand le LotExpediteur est NOTIFIE, on créer X PaiementLotExpediteur que le fraudeur nous doit.
2. Le Fraudeur est notifié et possède un lien pour accéder au site de paiement via son l'identifiant_public (id_public) de son Lot.
3. Le Fraudeur voit son LotExpediteur, ses photos etc et le ou les paiements qui lui sont demandés. (via la route GET /{lot_id_public}/visualiser)
4. Il clique sur un paiement, notre API appelle l'API d'encaissement, pour récupérer les moyens de paiements disponibles (/{lot_id_public}/initier_paiement/{paiement_id})
5. Le fraudeur sélectionne un moyen de paiement, ce moyen de paiement est envoyé à l'API (/{lot_id_public}/initier_paiement/{paiement_id}/payer).
On appelle l'API Encaissement pour faire un POST pour ce moyen de paiement,
on enregistre une nouvelle Transaction (TransactionPaiementLotExpediteur) transaction_id et toutes informations et on retourne les instructions au fraudeur.
6. Le paiement est effectué ou non, on a la transaction ID pour savoir.

# Service
L'API d'encaissement est : encaissement_service.py
Le fichier contenant l'ensemble des routes pour ce workflow: api/route/lots_expediteur/encaissements.py