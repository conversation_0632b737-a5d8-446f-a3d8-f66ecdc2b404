# Workflow API pour l'application mobile

## Flux principal des enveloppes

### 1. Récupération/Création d'une enveloppe en édition

```
GET /api/v1/enveloppes/edition
```

Permet de récupérer l'enveloppe en édition ou de la créer si elle n'existe pas.

### 2. Modification des informations de l'enveloppe

```
POST /api/v1/enveloppes/
```

Permet de mettre à jour les informations de base de l'enveloppe:
- Poids
- Dimensions (surdimensionné ou non)
- Destination
- Expéditeur


### 3. Ajout d'un affranchissement

```
POST /api/v1/enveloppes/{enveloppe_id}/add
```

Ajoute un nouvel affranchissement à l'enveloppe:
- Requiert un objet `AjoutAffranchissement` avec les détails de l'affranchissement
- Effectue les validations nécessaires selon le type d'affranchissement
- Retourne un objet `EnveloppePublic` avec l'affranchissement ajouté

#### Gestion des affranchissements incomplets

Certains affranchissements nécessitent des informations complémentaires pour être valides:
- Si l'affranchissement est incomplet, il est temporairement est retourné par l'API mais est indiqué comme `informations.complet: "FALSE"`
- Le frontend doit afficher un formulaire permettant à l'utilisateur de compléter les informations manquantes
- Les champs requis sont indiqués dans `affranchissement.informations.champs_requis`
- Une fois complété, l'affranchissement doit être réenvoyer via la même route `POST /api/v1/enveloppes/{enveloppe_id}/add`

#### Exemples d'ajout d'affranchissement

##### Exemple 1: Ajout via code (S10, SD87, etc.)

```json
POST /api/v1/enveloppes/{enveloppe_id}/add
{
    "code": "LL014699171FR"
}
```

Pour un code SD87 qui nécessite une nature:

```json
POST /api/v1/enveloppes/{enveloppe_id}/add
{
    "code": "SD87000999922741N",
    "nature": "LV_20"
}
```

##### Exemple 2: Ajout via modèle d'affranchissement

```json
POST /api/v1/enveloppes/{enveloppe_id}/add
{
    "categorie": "MARI",
    "type": "VAL",
    "devise": "EURO",
    "prix_unite_devise": 1.26
}
```

Pour un affranchissement de type SD avec origine spécifique:

```json
POST /api/v1/enveloppes/{enveloppe_id}/add
{
    "categorie": "CODE",
    "type": "SD",
    "prix_unite_devise": 0.5,
    "devise": "EURO",
    "origine": "88"
}
```

### 4. Mise à jour d'un affranchissement

```
PUT /api/v1/enveloppes/{enveloppe_id}/affranchissements/{affranchissement_id}
```

Permet de compléter ou modifier un affranchissement existant:

### 5. Suppression d'un affranchissement

```
DELETE /api/v1/enveloppes/{enveloppe_id}/affranchissements/{affranchissement_id}
```

Supprime un affranchissement spécifique de l'enveloppe.

### 6. Suppression de tous les affranchissements

```
DELETE /api/v1/enveloppes/{enveloppe_id}/affranchissements
```

Supprime tous les affranchissements de l'enveloppe.

### 7. Ajout de photos


```
POST /api/v1/enveloppes/{enveloppe_id}/photos
```

Pour ajouter une photo à une enveloppe spécifique.

### 8. Finalisation de l'enveloppe

```
POST /api/v1/enveloppes/{enveloppe_id}/terminer
```

Marque l'enveloppe comme terminée:
- Vérifie que l'enveloppe est complète (affranchissements et photos)
- Change le statut de l'enveloppe à `TERMINEE`
- Gère la création d'un lot d'expéditeur si nécessaire

## Routes auxiliaires utiles

### Données de référence

```
GET /api/v1/enumerations/produits
GET /api/v1/enumerations/affranchissements
GET /api/v1/enumerations/natures
```

Ces routes fournissent les données de référence nécessaires certains appels API

### Recherche d'expéditeurs

```
GET /api/v1/expediteurs/search
```

Permet de rechercher un expéditeur existant.

### Recherche de destinations

```
GET /api/v1/destinations
```

Permet de récupérer les destinations disponibles.
